import Foundation
import SwiftUI
import Combine
import PangeaMapView
import PangeaMapView.MapView
import PangeaMapbox

/// Manages the Pangea MapView and MapViewport lifecycle for SwiftUI integration.
class PangeaViewportManager: ObservableObject {
    @Published var map: MapViewport? = nil
    @Published var mapView: MapView? = nil
    
    // HTTP client for Pangea sources - should have app lifetime
    private var httpClient: HttpClient?

    /// Creates and configures the MapView with Mapbox integration.
    /// - Parameters:
    ///   - mapboxToken: The Mapbox access token
    ///   - sunApiToken: The SUN API token for Pangea sources
    ///   - latitude: Optional initial latitude (defaults to San Francisco)
    ///   - longitude: Optional initial longitude (defaults to San Francisco)
    /// - Returns: Configured MapView instance
    func createMap(mapboxToken: String, sunApiToken: String, latitude: Double? = nil, longitude: Double? = nil) -> MapView {
        let mapView = MapView(frame: .zero)
        // Set a background color for debugging (remove in production)
        mapView.backgroundColor = .red
        // Configure initial map properties
        mapView.basemap = "mapbox://styles/mapbox/streets-v11"
        mapView.maximumBounds = nil
        mapView.latitude = latitude ?? 37.7749 // Use provided latitude or default to San Francisco
        mapView.longitude = longitude ?? -122.4194 // Use provided longitude or default to San Francisco
        mapView.minimumZoom = 0.0
        mapView.maximumZoom = 25.0
        mapView.zoomLevel = 3.0
        mapView.minimumPitch = 0.0
        mapView.maximumPitch = 85.0
        mapView.pitch = 0.0
        mapView.bearing = 0.0
        // mapView.mapMoveIdleTimeout = 64.0 // Removed: Not available in SDK
        // mapView.loadingTimeout = 5000      // Removed: Not available in SDK

        // Validate tokens
        guard !mapboxToken.isEmpty else {
            print("[PangeaViewportManager] Error: MAPBOX_TOKEN is empty")
            return mapView
        }
        
        guard !sunApiToken.isEmpty else {
            print("[PangeaViewportManager] Error: SUN_API_TOKEN is empty")
            return mapView
        }
        
        // Create HTTP client for Pangea sources (if not already created)
        if self.httpClient == nil {
            self.httpClient = UrlSessionHttpEngineKt.createHttpClient()
            print("[PangeaViewportManager] HttpClient created for Pangea sources")
            
            // TODO: Consider implementing custom URLSession configuration for enhanced logging
            // if debugging is enabled in your config system
            #if DEBUG
            print("[PangeaViewportManager] Debug mode: HttpClient logging available via Console.app")
            print("[PangeaViewportManager] Filter by: subsystem:com.apple.network OR process:\(Bundle.main.bundleIdentifier ?? "twc-snow")")
            #endif
        }
        
        // Create the Mapbox viewport
        MapboxViewport.companion.create(mapView: mapView, accessToken: mapboxToken) { map in
            self.map = map
            
            // Add satrad layer once map is ready
          self.addSatRadLayer(to: map, sunApiToken: sunApiToken, mapboxToken: mapboxToken)
        }
        self.mapView = mapView
        return mapView
    }
    
    /// Adds the satellite radar (satrad) layer to the map
    /// - Parameters:
    ///   - map: The MapViewport to add the layer to
    ///   - sunApiToken: The SUN API token for authentication
    private func addSatRadLayer(to map: MapViewport, sunApiToken: String, mapboxToken: String) {
        guard let httpClient = self.httpClient else {
            print("[PangeaViewportManager] Error: HttpClient not available for satrad layer")
            return
        }
        
        print("[PangeaViewportManager] Creating satrad layer...")
        print("[PangeaViewportManager] Using SUN API token: \(String(sunApiToken.prefix(10)))...")
        
        // Create SunRasterSource for satellite radar data
        // Go back to "satrad" productKey like JavaScript, but try different productSet values
      let satRadSource = SunRasterSource(
            productKey: "satrad",  // Back to matching JavaScript exactly
            productSet: "myProductSet",
            apiKey: sunApiToken,
            httpClient: httpClient,
            options: SunRasterSourceOptions().withId(id: "SatRadSource")
        )
        
      print("[PangeaViewportManager] SunRasterSource created with productKey: \(satRadSource.productKey), productSet: \(satRadSource.productSet), ID: \(satRadSource.id)")
        
//      satRadSource.activate()
      
        // Subscribe to source ready event
        let readySubscription = satRadSource.ready.subscribe { [weak self] _ in
            print("[PangeaViewportManager] 🎉 SatRad source ready! Creating layer...")
            
            // Create RasterLayer with the source
            let satRadLayer = RasterLayer(
                source: satRadSource,
                options: RasterLayerOptions().withId(id: "SatRadLayer")
            )
            
            print("[PangeaViewportManager] RasterLayer created with ID: \(satRadLayer.id)")
            
            // Add source and layer to map
            // Note: Force cast to NSString as mentioned in the getting started guide
            map.sources.add(id: satRadSource.id as NSString, item: satRadSource)
            print("[PangeaViewportManager] Source added to map")
            
            map.layers.add(item: satRadLayer)
            print("[PangeaViewportManager] 🗺️ SatRad layer added successfully to map!")
        }
        
        // Subscribe to source error event for debugging
      let errorSubscription = satRadSource.failed.subscribe { [weak self] error in
            print("[PangeaViewportManager] ❌ SatRad source error: \(error)")
        }
    }
} 
