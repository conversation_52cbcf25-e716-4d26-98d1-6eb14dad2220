import Foundation
import React
import PangeaMapView
import PangeaMapbox
import Combine

/// React Native bridge component for Pangea map
@objc(RNMapView)
class RNMapView: UIView {
    
    private var manager: PangeaViewportManager?
    private var pangeaMapView: MapView?
    private var viewport: MapViewport?
    private var cancellables: Set<AnyCancellable> = []
    private var hasValidSize = false
    private var disposables: [Any] = []
    
    @objc var mapboxToken: NSString = "" {
        didSet {
            createMapIfReady()
        }
    }
    
    @objc var sunApiToken: NSString = "" {
        didSet {
            createMapIfReady()
        }
    }
    
    @objc var initialCenter: NSDictionary? {
        didSet {
            createMapIfReady()
        }
    }
    
    @objc var onMapMove: RCTDirectEventBlock?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    deinit {
        // Clean up Combine cancellables
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()
        
        // Clean up Pangea disposables
        disposables.removeAll()
        viewport?.dispose()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // Check if we now have a valid size and create map if needed
        if bounds.width > 0 && bounds.height > 0 && !hasValidSize {
            hasValidSize = true
            print("🤖 Got valid size: \(bounds.size), creating map if ready...")
            createMapIfReady()
        }
    }
    
    private func setupView() {
        backgroundColor = .systemGray6
    }
    

    
    private func createMapIfReady() {
        // Need tokens and valid size
        guard mapboxToken.length > 0 && sunApiToken.length > 0 && hasValidSize else {
            print("🤖 Not ready - tokens: \(mapboxToken.length > 0), \(sunApiToken.length > 0), size: \(hasValidSize)")
            return
        }
        
        // Don't recreate if already exists
        guard pangeaMapView == nil else {
            print("🤖 Map already exists")
            return
        }
        
        print("🤖 Creating Pangea map with size: \(bounds.size)")
        
        // Clean up existing
        self.cancellables.forEach { $0.cancel() }
        self.cancellables.removeAll()
        
        // Create manager and map
        manager = PangeaViewportManager()
        
        // Extract coordinates if provided
        var latitude: Double? = nil
        var longitude: Double? = nil
        if let center = initialCenter,
           let lat = center["latitude"] as? NSNumber,
           let lon = center["longitude"] as? NSNumber {
            latitude = lat.doubleValue
            longitude = lon.doubleValue
            print("🤖 Using initial center: \(lat), \(lon)")
        }
        
        // Create the map with current bounds
        let newMapView = manager?.createMap(
            mapboxToken: String(mapboxToken), 
            sunApiToken: String(sunApiToken), 
            latitude: latitude, 
            longitude: longitude
        )
        
        if let newMapView = newMapView {
            print("🤖 Pangea map created successfully!")
            pangeaMapView = newMapView
            
            // Set the frame explicitly before adding
            newMapView.frame = bounds
            
            // Add map view
            addSubview(newMapView)
            newMapView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                newMapView.topAnchor.constraint(equalTo: topAnchor),
                newMapView.leadingAnchor.constraint(equalTo: leadingAnchor),
                newMapView.trailingAnchor.constraint(equalTo: trailingAnchor),
                newMapView.bottomAnchor.constraint(equalTo: bottomAnchor)
            ])
            
            // Wait for viewport and set up events
            setupViewportSubscription()
        } else {
            print("🤖 Failed to create Pangea map")
        }
    }
    
    private func setupViewportSubscription() {
        print("🤖 Setting up viewport subscription...")
        
        // Subscribe to viewport creation
        manager?.$map
            .compactMap { $0 }
            .first()
            .sink { [weak self] viewport in
                DispatchQueue.main.async {
                    print("🤖 Viewport received: \(viewport)")
                    self?.viewport = viewport
                    self?.setupMapEvents(viewport: viewport)
                }
            }
            .store(in: &self.cancellables)
    }
    
    private func setupMapEvents(viewport: MapViewport) {
        print("🤖 Setting up real Pangea map events for viewport: \(viewport)")
        
        // Subscribe to real Pangea camera movement events
        let movedSubscription = viewport.moved.subscribe { [weak self] region in
            DispatchQueue.main.async {
                print("🤖 Real map moved event received")
                let geoCenter = region.geoCenter
                self?.sendMapMoveEvent(
                    center: (geoCenter.latitude, geoCenter.longitude),
                    zoom: region.zoomLevel
                )
            }
        }
        
        let zoomedSubscription = viewport.zoomed.subscribe { [weak self] region in
            DispatchQueue.main.async {
                print("🤖 Real zoom event received")
                let geoCenter = region.geoCenter
                self?.sendMapMoveEvent(
                    center: (geoCenter.latitude, geoCenter.longitude),
                    zoom: region.zoomLevel
                )
            }
        }
        
        let rotatedSubscription = viewport.rotated.subscribe { [weak self] region in
            DispatchQueue.main.async {
                print("🤖 Real rotation event received")
                let geoCenter = region.geoCenter
                self?.sendMapMoveEvent(
                    center: (geoCenter.latitude, geoCenter.longitude),
                    zoom: region.zoomLevel
                )
            }
        }
        
        // Store subscriptions for cleanup
        disposables.append(movedSubscription)
        disposables.append(zoomedSubscription)
        disposables.append(rotatedSubscription)
    }
    
    private func sendMapMoveEvent(center: (Double, Double), zoom: Double) {
        guard let onMapMove = onMapMove else {
            print("🤖 No onMapMove callback set")
            return
        }
        
        let eventData: [String: Any] = [
            "center": [
                "latitude": center.0,
                "longitude": center.1
            ],
            "zoomLevel": zoom,
            "bearing": 0.0,
            "pitch": 0.0
        ]
        
        onMapMove(eventData)
        print("🤖 Sent map move event: \(center.0), \(center.1), zoom: \(zoom)")
    }
}

@objc(RNMapViewManager)
class RNMapViewManager: RCTViewManager {
    
    override func view() -> UIView! {
        print("🤖 Creating RNMapView with Pangea...")
        return RNMapView()
    }
    
    override static func requiresMainQueueSetup() -> Bool {
        return true
    }
}
