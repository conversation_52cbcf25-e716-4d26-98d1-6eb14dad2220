package com.boneil.twcsnow

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.TextView
import android.os.Handler
import android.os.Looper
import android.graphics.Color
import android.graphics.Typeface
import android.util.Log
import android.view.Gravity
import com.weather.pangea.map.MapView
import com.weather.pangea.map.MapViewport
import com.weather.pangea.mapbox.MapboxViewport
import com.weather.pangea.mapbox.MapboxViewportOptions
import com.weather.pangea.geography.GeoPoint
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.Arguments
import kotlin.random.Random

/**
 * Custom Android view wrapper that holds the Pangea MapView and manages token props
 */
class RNMapView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var mapView: MapView? = null
    private var mapViewport: MapViewport? = null
    private var _mapboxToken: String? = null
    private var _sunApiToken: String? = null
    private var _initialCenter: Map<String, Any>? = null
    
    // Event handling
    private var eventEmitter: RCTEventEmitter? = null
    
    // Debug overlay
    private var debugLabel: TextView? = null
    
    // Real event subscriptions storage (using Any to avoid complex typing issues)
    private val subscriptions = mutableListOf<Any>()
    
    // Flag to track if map needs recreation
    private var needsMapRecreation = false

    var mapboxToken: String?
        get() = _mapboxToken
        set(value) {
            _mapboxToken = value
            updateMapIfReady()
        }

    var sunApiToken: String?
        get() = _sunApiToken
        set(value) {
            _sunApiToken = value
            updateMapIfReady()
        }

    var initialCenter: Map<String, Any>?
        get() = _initialCenter
        set(value) {
            _initialCenter = value
            updateMapCenterIfReady()
        }

    init {
        setupView()
        setupDebugOverlay()
    }

    /**
     * Set the event emitter for dispatching events to React Native
     */
    fun setEventEmitter(emitter: RCTEventEmitter) {
        eventEmitter = emitter
    }

    private fun setupView() {
        // Set up the container view
        layoutParams = LayoutParams(
            LayoutParams.MATCH_PARENT,
            LayoutParams.MATCH_PARENT
        )
    }

    private fun setupDebugOverlay() {
        // Create debug overlay similar to iOS
        debugLabel = TextView(context).apply {
            text = "🤖 RNMapView Status\nInitializing..."
            textAlignment = TextView.TEXT_ALIGNMENT_CENTER
            setTextColor(Color.BLUE)
            setBackgroundColor(Color.argb(230, 255, 255, 255)) // Semi-transparent white
            setTypeface(null, Typeface.BOLD)
            textSize = 12f
            setPadding(16, 16, 16, 16)
            
            layoutParams = LayoutParams(
                LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT
            ).apply {
                gravity = Gravity.TOP or Gravity.START
                setMargins(20, 20, 20, 20)
            }
        }
        
        addView(debugLabel)
    }

    /**
     * Dispatch map move event to React Native
     */
    private fun sendMapMoveEvent(center: Pair<Double, Double>, zoom: Double) {
        val eventData = Arguments.createMap().apply {
            val centerMap = Arguments.createMap().apply {
                putDouble("latitude", center.first)
                putDouble("longitude", center.second)
            }
            putMap("center", centerMap)
            putDouble("zoomLevel", zoom)
            putDouble("bearing", 0.0)
            putDouble("pitch", 0.0)
        }
        
        eventEmitter?.receiveEvent(id, "onMapMove", eventData)
        Log.d("RNMapView", "🤖 Sent map move event: ${center.first}, ${center.second}, zoom: $zoom")
    }

    /**
     * Set up real Pangea map event subscriptions
     */
    private fun setupRealMapEvents(viewport: MapViewport) {
        Log.d("RNMapView", "🤖 Setting up real Pangea map events for viewport: $viewport")
        
        // Clean up any existing subscriptions before creating new ones
        if (subscriptions.isNotEmpty()) {
            Log.d("RNMapView", "🤖 Cleaning up ${subscriptions.size} existing event subscriptions")
            // Properly dispose of each subscription to prevent memory leaks
            subscriptions.forEach { subscription ->
                try {
                    // Use reflection to call dispose method
                    val disposeMethod = subscription.javaClass.getMethod("dispose")
                    disposeMethod.invoke(subscription)
                } catch (e: Exception) {
                    Log.e("RNMapView", "Error disposing subscription: ${e.message}")
                }
            }
            subscriptions.clear()
        }
        
        // Subscribe to real Pangea camera movement events
        val movedSubscription = viewport.moved.subscribe { region ->
            Handler(Looper.getMainLooper()).post {
                Log.d("RNMapView", "🤖 Real map moved event received")
                val geoCenter = region.geoCenter
                sendMapMoveEvent(
                    Pair(geoCenter.latitude, geoCenter.longitude),
                    region.zoomLevel
                )
                updateDebugInfo()
            }
        }
        
        val zoomedSubscription = viewport.zoomed.subscribe { region ->
            Handler(Looper.getMainLooper()).post {
                Log.d("RNMapView", "🤖 Real zoom event received")
                val geoCenter = region.geoCenter
                sendMapMoveEvent(
                    Pair(geoCenter.latitude, geoCenter.longitude),
                    region.zoomLevel
                )
                updateDebugInfo()
            }
        }
        
        val rotatedSubscription = viewport.rotated.subscribe { region ->
            Handler(Looper.getMainLooper()).post {
                Log.d("RNMapView", "🤖 Real rotation event received")
                val geoCenter = region.geoCenter
                sendMapMoveEvent(
                    Pair(geoCenter.latitude, geoCenter.longitude),
                    region.zoomLevel
                )
                updateDebugInfo()
            }
        }
        
        // Store subscriptions for cleanup
        subscriptions.add(movedSubscription)
        subscriptions.add(zoomedSubscription)
        subscriptions.add(rotatedSubscription)
        
        Log.d("RNMapView", "🤖 Successfully set up ${subscriptions.size} event subscriptions")
        updateDebugInfo()
    }

    /**
     * Update debug overlay with current status
     */
    private fun updateDebugInfo() {
        debugLabel?.text = "🤖 RNMapView Status\n" +
                "Tokens: ${if (_mapboxToken?.isNotEmpty() == true) "✅" else "❌"}\n" +
                "Map: ${if (mapView != null) "✅" else "❌"}\n" +
                "Viewport: ${if (mapViewport != null) "✅" else "❌"}\n" +
                "Events: ${subscriptions.size} subscriptions\n" +
                "Needs Recreation: ${if (needsMapRecreation) "🔄" else "✅"}\n" +
                "Size: ${width}x${height}"
    }

    private fun updateMapIfReady() {
        // Only create map when we have both tokens
        val mapboxToken = _mapboxToken
        val sunApiToken = _sunApiToken
        
        if (mapboxToken.isNullOrEmpty() || sunApiToken.isNullOrEmpty()) {
            return
        }

        // Remove existing map view if any
        mapView?.let { removeView(it) }

        try {
            // Create new Pangea MapView
            val newMapView = MapView(context).apply {
                layoutParams = LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT
                )
            }

            // Create viewport options with initial configuration
            val mapboxOptions = MapboxViewportOptions()

            // Initialize the MapView with Mapbox viewport
            MapboxViewport.create(newMapView, mapboxToken, mapboxOptions) { viewport ->
                Log.d("RNMapView", "🤖 MapboxViewport created successfully, setting up events and configuration")
                mapViewport = viewport
                
                // Configure initial map center if we have location data
                configureInitialMapCenter(viewport)
                
                // Set up real Pangea map events
                setupRealMapEvents(viewport)
                
                // Update debug overlay
                updateDebugInfo()
                
                Log.d("RNMapView", "🤖 Map viewport setup completed with ${subscriptions.size} event subscriptions")
                
                // TODO: Store sunApiToken for future weather layer integration
            }

            mapView = newMapView
            addView(newMapView)

        } catch (e: Exception) {
            // Handle any errors during map creation
            e.printStackTrace()
        }
    }

    private fun updateMapCenterIfReady() {
        // If we have a viewport and new center coordinates, update the map center
        mapViewport?.let { viewport ->
            configureInitialMapCenter(viewport)
        }
    }

    private fun configureInitialMapCenter(viewport: MapViewport) {
        val centerData = _initialCenter
        
        // Set default basemap style
        viewport.changeBasemap("mapbox://styles/mapbox/streets-v11")
        
        if (centerData != null) {
            try {
                // Extract latitude and longitude from the center data
                val latitude = when (val lat = centerData["latitude"]) {
                    is Double -> lat
                    is Number -> lat.toDouble()
                    else -> null
                }
                
                val longitude = when (val lng = centerData["longitude"]) {
                    is Double -> lng
                    is Number -> lng.toDouble()
                    else -> null
                }
                
                if (latitude != null && longitude != null) {
                    // Create GeoPoint with user's location (longitude, latitude order per Pangea SDK)
                    val geoCenter = GeoPoint(longitude, latitude)
                    
                    // Set the map center to user's location
                    viewport.center(geoCenter)
                    
                    // Set a reasonable zoom level for user location
                    viewport.zoom(12.0)
                }
            } catch (e: Exception) {
                // Handle any errors during coordinate parsing
                e.printStackTrace()
                // Fall back to default center if parsing fails
                setDefaultMapCenter(viewport)
            }
        } else {
            // Set default center if no initial center provided
            setDefaultMapCenter(viewport)
        }
    }

    private fun setDefaultMapCenter(viewport: MapViewport) {
        // Default to center of US if no user location provided
        val defaultCenter = GeoPoint(-95.0, 39.0) // longitude, latitude
        viewport.center(defaultCenter)
        viewport.zoom(4.0)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        
        Log.d("RNMapView", "🤖 onAttachedToWindow called")
        
        // If we need map recreation and have the required tokens, recreate it
        if (needsMapRecreation || mapView == null) {
            Log.d("RNMapView", "🤖 Recreating map after reattach (needsMapRecreation: $needsMapRecreation, mapView is null: ${mapView == null})")
            updateMapIfReady()
            needsMapRecreation = false
            Log.d("RNMapView", "🤖 Map recreation completed, events should be reattached")
        } else {
            Log.d("RNMapView", "🤖 Map already exists, no recreation needed")
        }
        
        updateDebugInfo()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        
        Log.d("RNMapView", "🤖 onDetachedFromWindow called")
        
        // Clean up Pangea event subscriptions properly
        if (subscriptions.isNotEmpty()) {
            Log.d("RNMapView", "🤖 Disposing ${subscriptions.size} event subscriptions on detach")
            subscriptions.forEach { subscription ->
                try {
                    // Use reflection to call dispose method
                    val disposeMethod = subscription.javaClass.getMethod("dispose")
                    disposeMethod.invoke(subscription)
                } catch (e: Exception) {
                    Log.e("RNMapView", "Error disposing subscription: ${e.message}")
                }
            }
            subscriptions.clear()
        }
        
        // Clean up the map view
        mapView?.let { removeView(it) }
        mapView = null
        mapViewport = null
        
        // Mark that we need recreation, but keep tokens and other state
        needsMapRecreation = true
        
        // Don't null the event emitter - we'll need it when recreating
        // eventEmitter = null
        
        updateDebugInfo()
    }
} 