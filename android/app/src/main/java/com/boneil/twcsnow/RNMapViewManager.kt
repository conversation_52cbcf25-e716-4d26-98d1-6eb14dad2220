package com.boneil.twcsnow

import com.facebook.react.bridge.ReadableMap
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.Arguments

/**
 * React Native ViewManager for the RNMapView component
 */
@ReactModule(name = RNMapViewManager.REACT_CLASS)
class RNMapViewManager : SimpleViewManager<RNMapView>() {

    companion object {
        const val REACT_CLASS = "RNMapView"
    }

    override fun getName(): String {
        return REACT_CLASS
    }

    override fun createViewInstance(reactContext: ThemedReactContext): RNMapView {
        val mapView = RNMapView(reactContext)
        // Set the event emitter reference so the view can dispatch events
        mapView.setEventEmitter(reactContext.getJSModule(RCTEventEmitter::class.java))
        return mapView
    }

    /**
     * Export custom event names to React Native
     */
    override fun getExportedCustomBubblingEventTypeConstants(): Map<String, Any>? {
        return mapOf(
            "onMapMove" to mapOf(
                "phasedRegistrationNames" to mapOf(
                    "bubbled" to "onMapMove"
                )
            )
        )
    }

    /**
     * Sets the Mapbox token prop
     */
    @ReactProp(name = "mapboxToken")
    fun setMapboxToken(view: RNMapView, mapboxToken: String?) {
        view.mapboxToken = mapboxToken
    }

    /**
     * Sets the SUN API token prop
     */
    @ReactProp(name = "sunApiToken")
    fun setSunApiToken(view: RNMapView, sunApiToken: String?) {
        view.sunApiToken = sunApiToken
    }

    /**
     * Sets the initial center coordinates prop
     */
    @ReactProp(name = "initialCenter")
    fun setInitialCenter(view: RNMapView, initialCenter: ReadableMap?) {
        if (initialCenter != null) {
            val centerMap: Map<String, Any> = initialCenter.toHashMap().mapValues { it.value ?: "" }
            view.initialCenter = centerMap
        } else {
            view.initialCenter = null
        }
    }

    /**
     * Called when the view is dropped
     */
    override fun onDropViewInstance(view: RNMapView) {
        super.onDropViewInstance(view)
        // Clean up any resources if needed
    }
} 