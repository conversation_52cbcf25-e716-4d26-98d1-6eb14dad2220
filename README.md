# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Set up environment variables

   Copy the `.env.example` file to `.env` and fill in the required values:

   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your API keys:
   - `WEATHER_API_KEY`: Your weather API key
   - `MAPBOX_TOKEN`: Your Mapbox access token
   - `SUN_API_TOKEN`: Your Sun API token
   - `EAS_PROJECT_ID`: Your EAS project ID

3. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.

## API Dependencies

This app depends on the [weather.com resorts API](https://api.weather.com/v3/location/near) to fetch nearby ski resorts for the Today screen and other features. You must provide a valid `SUN_API_TOKEN` in your `.env` file for this API to work.

- The API is called with the user's geocode (latitude,longitude) and returns a list of nearby ski resorts.
- The `SUN_API_TOKEN` is required as the `apiKey` parameter in the API call.

Example usage:
```
https://api.weather.com/v3/location/near?geocode=33.74,-84.39&product=ski&format=json&apiKey=YOUR_SUN_API_TOKEN
```
