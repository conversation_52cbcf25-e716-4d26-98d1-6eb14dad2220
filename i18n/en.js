const en = {
  configTestScreenTitle: 'Config Test Screen',
  profileTitle: 'Profile',
  profileUnderConstruction: 'This section is under construction.',
  languageLabel: 'Language',
  
  // API Error Messages
  errors: {
    network: 'Network connection failed. Please check your internet connection.',
    timeout: 'Request timed out. Please try again.',
    validation: 'Invalid request data.',
    notFound: 'Requested resource not found.',
    rateLimit: 'Too many requests. Please wait and try again.',
    server: 'Server error occurred. Please try again later.',
    authentication: 'Authentication failed. Please check your credentials.',
    unknown: 'An unexpected error occurred.',
  },
  
  // Probabilistic Snow Card
  probabilisticSnow: {
    title: 'Probable Range',
    loading: 'Loading...',
    loadingError: 'Unable to load snowfall forecast',
    retryButton: 'Retry',
    lessLikely: 'Less Likely',
    moreLikely: 'More Likely',
    mostLikely: 'Most Likely',
    noData: 'No snowfall data available',
    noSnowfallExpected: 'No snowfall expected',
    expectedSnowfall: 'Expected snowfall: {{amount}}, with {{range}} most likely',
    hours24: '24 hours',
    hours48: '48 hours', 
    hours72: '72 hours',
    primaryDescription: 'There is a {{probability}}% chance of snowfall in the next {{timespan}}, with {{range}} most likely',
    alternateDescription: 'There is a {{probability}}% chance of snowfall in the next {{timespan}}',
    someSnow: 'Some Snow',
    atLeastAmount: 'At least {{amount}} in',
    moreThanAmount: 'More than {{amount}} in',
    lessThanAmount: '< {{amount}} in',
    rangeAmount: '{{min}}-{{max}} in',
  },

  mountainElevationCard: {
    rightNow: 'Right Now',
    altitude: 'Altitude',
    gusts: 'Gusts',
    visibility: 'Visibility',
    feet: 'ft',
    mph: 'mph',
    lessThanOneMile: '<1 mi',
    miles: '{{count}} mi',
  },
};
export default en; 