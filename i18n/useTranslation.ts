import i18n from 'i18next';
import { initReactI18next, useTranslation as useI18nextTranslation } from 'react-i18next';
import { NativeModules, Platform } from 'react-native';
import en from './en';
import fr from './fr';

// Get device language for React Native
const getDeviceLanguage = (): string => {
  let locale = 'en'; // fallback
  
  if (Platform.OS === 'ios') {
    locale = NativeModules.SettingsManager?.settings?.AppleLocale || 
             NativeModules.SettingsManager?.settings?.AppleLanguages?.[0] || 'en';
  } else if (Platform.OS === 'android') {
    locale = NativeModules.I18nManager?.localeIdentifier || 'en';
  }
  
  // Extract language code (e.g., 'en-US' -> 'en')
  return locale.split(/[-_]/)[0];
};

// i18next initialization
if (!i18n.isInitialized) {
  const deviceLanguage = getDeviceLanguage();
  const supportedLanguages = ['en', 'fr'];
  const language = supportedLanguages.includes(deviceLanguage) ? deviceLanguage : 'en';
  
  i18n
    .use(initReactI18next)
    .init({
      lng: language, // Set initial language
      fallbackLng: 'en',
      resources: {
        en: { translation: en },
        fr: { translation: fr },
      },
      interpolation: { escapeValue: false }, // React already escapes
      debug: false,
    });
}

// Language switcher
export const setLanguage = (lang: string) => {
  i18n.changeLanguage(lang);
};

// Export the hook
export const useTranslation = () => useI18nextTranslation();

export default i18n;
// TODO: Remove any remaining legacy I18n usage in the app and migrate to i18next/react-i18next.
