import { ThemedText } from '@/components/ThemedText';
import { Picker } from '@react-native-picker/picker';
import { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { setLanguage, useTranslation } from '../../i18n/useTranslation';

export default function ProfileScreen() {
  const { t, i18n } = useTranslation();
  const [language, setLanguageState] = useState(i18n.language.startsWith('fr') ? 'fr' : 'en');

  const handleLanguageChange = (lang: string) => {
    setLanguageState(lang);
    setLanguage(lang);
  };

  return (
    <View style={styles.container}>
      <ThemedText type="title">{t('profileTitle')}</ThemedText>
      <ThemedText>{t('profileUnderConstruction')}</ThemedText>
      {/* Language Switcher */}
      <View style={{ marginTop: 32, width: 200 }}>
        <ThemedText style={{ marginBottom: 8 }}>{t('languageLabel')}</ThemedText>
        <Picker
          selectedValue={language}
          onValueChange={handleLanguageChange}
          mode="dropdown"
        >
          <Picker.Item label="English" value="en" />
          <Picker.Item label="Français" value="fr" />
        </Picker>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
});