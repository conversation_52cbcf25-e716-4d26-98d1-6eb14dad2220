import { useTheme } from '@/app/theme/ThemeContext';
import { CurrentRadarCard } from '@/components/CurrentRadarCard';
import { HeroWeather } from '@/components/HeroWeather';
import { HourlyForecastBar } from '@/components/HourlyForecastBar';
import { RecommendationCard } from '@/components/RecommendationCard';
import ResortCard from '@/components/ResortCard';
import ENV from '@/constants/Env';
import { useLocation } from '@/contexts/LocationContext';
import { useHourlyForecastQuery } from '@/hooks/useHourlyForecastQuery';
import { useResortsQuery } from '@/hooks/useResortsQuery';
import { useReverseGeocodingQuery } from '@/hooks/useReverseGeocodingQuery';
import { useSkiResortsQuery } from '@/hooks/useSkiResortsQuery';
import { useWeatherQuery } from '@/hooks/useWeatherQuery';
import { useQueryClient } from '@tanstack/react-query';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { ActivityIndicator, Platform, RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { log } from '../../services/loggerService';

export default function TodayScreen() {
  // Use the location context to get the user's current location
  const { location, isLoading, error, hasPermission, requestPermission } = useLocation();
  log('[TodayScreen]', 'debug', 'Location context state:', [{ location, isLoading, error, hasPermission }]);

  // Prepare geocode string if location is available
  const geocode = location ? `${location.latitude},${location.longitude}` : '';

  // Data hooks lifted to parent for pull-to-refresh
  const weather = useWeatherQuery(geocode);
  const geo = useReverseGeocodingQuery(geocode);
  const resortsQuery = useResortsQuery(geocode, 2);
  const skiResortsQuery = useSkiResortsQuery();
  const hourly = useHourlyForecastQuery(geocode);

  const queryClient = useQueryClient();

  // Resort card selected state - Note: Each card now manages its own expand/collapse
  const [selectedCard, setSelectedCard] = React.useState<string | null>(null);

  // Fetch both nearby resorts and full resort info
  const { colors } = useTheme();

  // Merge nearby resort data with full resort info (by name, case-insensitive)
  const mergedResorts = React.useMemo(() => {
    if (!resortsQuery.data || !skiResortsQuery.data) return [];
    const merged = resortsQuery.data.map((nearby) => {
      const full = skiResortsQuery.data.find(
        r => r.name.toLowerCase() === nearby.name.toLowerCase()
      );
      // Ensure location is always a string for ResortData compatibility
      let locationString = nearby.location;
      if (!locationString && full && typeof full.location === 'object' && full.location) {
        const { city, state, country } = full.location;
        locationString = [city, state, country].filter(Boolean).join(', ');
      }
      const result = {
        ...nearby,
        ...full,
        // Fallbacks for missing fields
        id: nearby.id,
        name: nearby.name,
        distance: nearby.distance,
        latitude: nearby.latitude || full?.coordinates?.latitude || 0,
        longitude: nearby.longitude || full?.coordinates?.longitude || 0,
        ianaTimeZone: (nearby as any).ianaTimeZone,
        location: locationString || '', // Always a string
        // TODO: Add more merged fields as needed
      };
      log('[TodayScreen]', 'debug', 'Merged resort:', [result]);
      return result;
    });
    log('[TodayScreen]', 'debug', 'All merged resorts:', [merged]);
    return merged;
  }, [resortsQuery.data, skiResortsQuery.data]);
  const insets = useSafeAreaInsets();
  const bottomPadding = Platform.OS === 'android' ? 0 : 64;

  // Log geocode, location, and SUN_API_TOKEN for debugging
  React.useEffect(() => {
    const token = ENV.sunApiToken;
    log('[TodayScreen]', 'debug', 'location:', [location]);
    log('[TodayScreen]', 'debug', 'geocode:', [geocode]);
    if (token) {
      log('[TodayScreen]', 'info', '🔑 SUN_API_TOKEN:', [token.substring(0, 6) + '...' + token.slice(-4)]);
    } else {
      log('[TodayScreen]', 'warn', 'SUN_API_TOKEN is missing or empty');
    }
  }, [location, geocode]);

  // Pull-to-refresh state and handler
  const [refreshing, setRefreshing] = React.useState(false);
  // Add refreshCount state
  const [refreshCount, setRefreshCount] = React.useState(0);
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['weather'] }),
        queryClient.invalidateQueries({ queryKey: ['reverseGeocoding'] }),
        queryClient.invalidateQueries({ queryKey: ['resorts'] }),
        queryClient.invalidateQueries({ queryKey: ['hourlyForecast'] }),
      ]);
      setRefreshCount((c) => c + 1); // Increment to force RecommendationCard remount
    } finally {
      setRefreshing(false);
    }
  }, [queryClient]);

  // Handle loading, permission, and error states for location
  let statusMessage: string | null = null;
  if (isLoading) {
    statusMessage = 'Loading...';
  } else if (!hasPermission) {
    statusMessage = 'Location permission required';
  } else if (error) {
    statusMessage = `Error: ${error}`;
  }
  if (statusMessage) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <LinearGradient colors={['#1E3A8A', '#3B82F6']} style={styles.gradientBg} />
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
          <View style={{ marginTop: 40 }}>
            <HeroWeather location={statusMessage} />
            <View style={{ marginTop: 16 }}>
              <RecommendationCard location={statusMessage} />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  const edges = Platform.OS === 'android' ? (["top"] as const) : (["top", "bottom"] as const);
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f3f4f6' }} edges={edges}>
      <LinearGradient
        colors={['#1E3A8A', '#cdcdcd', '#3B82F6']}
        style={styles.gradientBg}
      />
      <ScrollView
        style={{ flex: 1}}
        contentContainerStyle={[
          styles.scrollContent,
        ]}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <HeroWeather location={location ? `${location.latitude},${location.longitude}` : 'Unknown'} />
        <View style={styles.overlapCard}>
          <RecommendationCard key={refreshCount} location={location ? `${location.latitude},${location.longitude}` : 'Unknown'} />
        </View>
        {/* Resort Cards Section */}
        <View style={{ marginTop: 16, marginBottom: 8 }}>
          {/* Resort Cards List */}
          {resortsQuery.isLoading || skiResortsQuery.isLoading ? (
            <ActivityIndicator size="small" style={{ marginVertical: 24 }} />
          ) : resortsQuery.error || skiResortsQuery.error ? (
            <Text style={{ color: colors.errorText, textAlign: 'center', marginVertical: 24 }}>Failed to load resorts</Text>
          ) : (
            <View>
              {/* <ResortCardList geocode={geocode} limit={2} /> */}
              {mergedResorts.map((resort) => (
                <ResortCard
                  key={resort.id}
                  resort={resort}
                  selectedCard={selectedCard}
                  setSelectedCard={setSelectedCard}
                />
              ))}
            </View>
          )}
        </View>
        {/* End Resort Cards Section */}
        <HourlyForecastBar key={`hourlyForecast-${refreshCount}`} location={location ? `${location.latitude},${location.longitude}` : 'Unknown'} />
        <CurrentRadarCard />
      </ScrollView>
    </SafeAreaView>
  );
}

const HERO_HEIGHT = 340;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  gradientBg: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 0,
  },
  scrollView: {
    flex: 1,
    zIndex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 0,
    paddingBottom: Platform.OS === 'android' ? 0 : 64,
  },
  overlapCard: {
    marginTop: -40,
    zIndex: 2,
  },
});