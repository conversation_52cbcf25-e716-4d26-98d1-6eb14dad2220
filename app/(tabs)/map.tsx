import { useTheme } from '@/app/theme/ThemeContext';
import AnimationTimeline from '@/components/AnimationTimeline';
import MapViewWrapper from '@/components/bridge-components/RNMapView';
import ENV from '@/constants/Env';
import { useLocation } from '@/contexts/LocationContext';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';

export default function MapScreen() {
  const { location } = useLocation();
  const { colors } = useTheme(); // Use our theme
  const [mapInfo, setMapInfo] = useState<{
    center: { latitude: number; longitude: number };
    zoomLevel: number;
    bearing: number;
    pitch: number;
  } | null>(null);

  // Add a key to force remount when returning to the screen
  const [mapKey, setMapKey] = useState(0);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Animation timeline state
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0.3); // 30% progress for demo

  // Force remount the map when the screen comes into focus
  // This is a backup solution in case the native lifecycle fix isn't enough
  useFocusEffect(
    useCallback(() => {
      if (hasInitialized) {
        // Only remount if we've already initialized (i.e., returning from another screen)
        console.log('🗺️ Map screen regained focus - forcing remount');
        setMapKey(prev => prev + 1);
      } else {
        // First time loading
        setHasInitialized(true);
      }
    }, [hasInitialized])
  );

  // Create location object for the map
  const mapCenter = location ? {
    latitude: location.latitude,
    longitude: location.longitude
  } : undefined; // fallback to undefined if no location

  const handleMapMove = (event: any) => {
    const { center, zoomLevel, bearing, pitch } = event.nativeEvent;
    setMapInfo({ center, zoomLevel, bearing, pitch });
    //console.log('Map moved to:', center, 'zoom:', zoomLevel);
  };

  // Demo timeline data
  const startTime = new Date();
  startTime.setHours(15, 0, 0, 0); // 3:00 PM
  const endTime = new Date();
  endTime.setHours(21, 0, 0, 0); // 9:00 PM

  // Animation timeline handlers
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    console.log('Animation play/pause toggled:', !isPlaying);
  };

  const handleProgressChange = useCallback((newProgress: number) => {
    // Round to prevent micro-movements that cause flashing
    const roundedProgress = Math.round(newProgress * 1000) / 1000;

    setProgress(roundedProgress);
    console.log('Animation progress changed:', roundedProgress);

    // Update the current time based on the new progress
    const totalDuration = endTime.getTime() - startTime.getTime();
    const newCurrentTime = new Date(startTime.getTime() + (roundedProgress * totalDuration));
    console.log('New current time:', newCurrentTime.toLocaleTimeString());
  }, [startTime, endTime]);

  // Calculate current time based on progress
  const timelineCurrentTime = new Date(startTime.getTime() + (progress * (endTime.getTime() - startTime.getTime())));

  return (
    <View style={styles.container}>
      <MapViewWrapper
        key={mapKey} // Force remount when returning to screen
        mapboxToken={ENV.mapboxToken}
        sunApiToken={ENV.sunApiToken}
        initialCenter={mapCenter}
        onMapMove={handleMapMove}
      />

      {/* Debug info overlay */}
      {mapInfo && (
        <View style={[styles.debugOverlay, { backgroundColor: colors.card }]}>
          <Text style={[styles.debugText, { color: colors.text }]}>
            📍 Lat: {mapInfo.center.latitude.toFixed(4)}
          </Text>
          <Text style={[styles.debugText, { color: colors.text }]}>
            📍 Lng: {mapInfo.center.longitude.toFixed(4)}
          </Text>
          <Text style={[styles.debugText, { color: colors.text }]}>
            🔍 Zoom: {mapInfo.zoomLevel.toFixed(1)}
          </Text>
          <Text style={[styles.debugText, { color: colors.text }]}>
            🧭 Bearing: {mapInfo.bearing.toFixed(1)}°
          </Text>
          <Text style={[styles.debugText, { color: colors.text }]}>
            📐 Pitch: {mapInfo.pitch.toFixed(1)}°
          </Text>
        </View>
      )}

      {/* Animation Timeline - positioned at bottom above navigation */}
      <View style={styles.timelineContainer}>
        <AnimationTimeline
          currentTime={timelineCurrentTime}
          startTime={startTime}
          endTime={endTime}
          isPlaying={isPlaying}
          progress={progress}
          use12HourFormat={true}
          durationLabel="6 Hrs"
          onPlayPause={handlePlayPause}
          onProgressChange={handleProgressChange}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  debugOverlay: {
    position: 'absolute',
    top: 50,
    right: 0,
    padding: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginVertical: 1,
  },
  timelineContainer: {
    position: 'absolute',
    bottom: 80, // Above the tab bar (typically ~80px height + some margin)
    left: 0,
    right: 0,
  },
});