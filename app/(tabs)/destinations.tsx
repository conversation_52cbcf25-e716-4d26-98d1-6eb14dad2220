import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import React from 'react';
import { StyleSheet, View } from 'react-native';

export default function DestinationsScreen() {
  return (
    <ThemedView style={styles.container}>
      <View style={styles.centered}>
        <ThemedText type="title">Destinations</ThemedText>
        <ThemedText style={styles.subtitle}>Content coming soon.</ThemedText>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  centered: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  subtitle: {
    marginTop: 12,
    fontSize: 16,
    opacity: 0.7,
  },
});