import AnimatedHeaderList from '@/components/AnimatedHeaderList';
import LeaderboardSection from '@/components/LeaderboardSection';
import ResortDetailModal from '@/components/ResortDetailModal'; // Import the modal
import TabBar, { TabDefinition } from '@/components/TabBar';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useLeaderboardQuery } from '@/hooks/useLeaderboardQuery';
import { Resort } from '@/types'; // Import Resort type
import { Feather } from "@expo/vector-icons";
import { useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native'; // Removed useColorScheme
import { useTheme } from '../theme/ThemeContext'; // Corrected relative path

// Define explicit tab key type
type ExploreTabKey = 'top' | 'snowfall' | 'forecast';

export default function ExploreScreen() {
  const LEADERBOARD_LIMIT = 5;
  const [activeTab, setActiveTab] = useState<ExploreTabKey>('top');
  const { data, isLoading, error } = useLeaderboardQuery(LEADERBOARD_LIMIT);
  const { colors } = useTheme();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedResort, setSelectedResort] = useState<Resort | null>(null);

  const handleResortPress = (resort: Resort) => {
    setSelectedResort(resort);
    setIsModalVisible(true);
  };
  const handleCloseModal = () => {
    setIsModalVisible(false);
    setSelectedResort(null);
  };
  const tabs: TabDefinition[] = [
    { key: 'top', title: 'Top' },
    { key: 'snowfall', title: 'Snowfall' },
    { key: 'forecast', title: 'Forecast' },
  ];

  const renderContent = () => {
    if (isLoading) {
      return (
        <ThemedView style={styles.centered}>
          <ActivityIndicator size="large" />
          <ThemedText>Loading leaderboards...</ThemedText>
        </ThemedView>
      );
    }
    if (error) {
      return (
        <ThemedView style={styles.centered}>
          <ThemedText style={{ fontSize: 32, marginBottom: 8 }}>⚠️</ThemedText>
          <ThemedText style={{ color: 'red', fontWeight: 'bold', fontSize: 18, marginBottom: 8 }}>
            Unable to load leaderboards
          </ThemedText>
          <ThemedText style={{ marginBottom: 8 }}>
            There was a problem connecting to the leaderboard service. Please check your internet connection and try again.
          </ThemedText>
          <ThemedText style={{ color: '#888', fontSize: 13, marginBottom: 16 }}>
            {error.message}
          </ThemedText>
        </ThemedView>
      );
    }
    if (!data) {
      return (
        <ThemedView style={styles.centered}>
          <ThemedText>No leaderboard data available.</ThemedText>
        </ThemedView>
      );
    }
    switch (activeTab) {
      case 'top':
        return (
          <>
            <LeaderboardSection
              title="Top Recent Snowfall"
              subtitle="Last 24 hours"
              entries={data.recentSnowfall}
              onEntryPress={handleResortPress}
            />
            <LeaderboardSection
              title="Top Forecasted Snow"
              subtitle="Next 5 days"
              entries={data.forecastSnowfall}
              onEntryPress={handleResortPress}
            />
            <LeaderboardSection
              title="Top Snow Totals"
              subtitle="2025-2026 Season"
              entries={data.seasonTotals}
              onEntryPress={handleResortPress}
            />
          </>
        );
      case 'snowfall':
        return (
          <LeaderboardSection
            title="Top Recent Snowfall"
            subtitle="Last 24 hours"
            entries={data.recentSnowfall}
            onEntryPress={handleResortPress}
          />
        );
      case 'forecast':
        return (
          <LeaderboardSection
            title="Top Forecasted Snow"
            subtitle="Next 5 days"
            entries={data.forecastSnowfall}
            onEntryPress={handleResortPress}
          />
        );
      default:
        return null;
    }
  };

  // Header icons for AnimatedHeaderList using the new function pattern
  const headerIcons = (isCollapsed: boolean) => (
    <View style={styles.headerIconsContainer}>
      <Feather
        name='sliders'
        size={24}
        color={isCollapsed ? colors.text : colors.textOnDark}
        style={isCollapsed ? {  } : {  }}
      />
      <Feather
        name='search'
        size={24}
        color={isCollapsed ? colors.text : colors.textOnDark}
        style={isCollapsed ? { } : {  }}
      />
    </View>
  );

  return (
    <AnimatedHeaderList
      headerImage={require('@/assets/images/explore-background.png')}
      headerTitle="Explore"
      headerIcons={headerIcons}
      hideImageWhenCollapsed={true}
      collapsedHeaderBackgroundColor={colors.background}
      collapsedHeaderIconsStyle={{ borderWidth: 1, borderColor: colors.error }}
      renderTabBar={() => (
        <TabBar
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={(key) => setActiveTab(key as ExploreTabKey)}
          style={{ backgroundColor: colors.background}}
          tabTextStyle={{ color: colors.tabIconDefault }}
          activeTabTextStyle={{ color: colors.tint, fontWeight: 'bold' }}
          activeTabStyle={{ borderBottomColor: colors.tint }}
        />
      )}
      renderContent={() => (
        <>
          <View style={styles.contentOuterContainer}>
            {renderContent()}
          </View>
          <ResortDetailModal
            visible={isModalVisible}
            resort={selectedResort}
            onClose={handleCloseModal}
          />
        </>
      )}
    />
  );
}

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 300, // Ensure it takes some space
  },
  contentOuterContainer: {
    padding: 0, // Reduced padding
    backgroundColor: '#EFEFEF',
  },
  headerIconsContainer: {
    flexDirection: 'row',
    alignSelf: 'flex-end',
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    gap: 4,
    zIndex: 2,
    padding: 4,
  },
});
