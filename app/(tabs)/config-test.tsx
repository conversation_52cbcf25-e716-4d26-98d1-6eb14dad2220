import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';
import { ActivityIndicator, Button, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useConfig } from '../../contexts/ConfigContext';
import { useTranslation } from '../../i18n/useTranslation';

function formatDate(ts?: number) {
  if (!ts) return '--';
  const d = new Date(ts);
  return d.toLocaleString();
}

// Use a light yellow background to indicate this is a test screen
export default function ConfigTestScreen() {
  const { config, meta, status, reload } = useConfig();
  const [clearMsg, setClearMsg] = useState('');
  const [configString, setConfigString] = useState('');
  const { t } = useTranslation();
  
  const handleClear = async () => {
    await AsyncStorage.removeItem('remoteConfigCache');
    setClearMsg('Saved config cleared!');
    setTimeout(() => setClearMsg(''), 2000);
    await reload();
  };

  useEffect(() => {
    console.log('👉 config', JSON.stringify(config, null, 2));
    setConfigString(JSON.stringify(config, null, 2));
  }, [config]);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#FFF9C4' }}>
      <ScrollView contentContainerStyle={[styles.container, { backgroundColor: '#FFF9C4' }]}>
        {/* i18n demo: Config Test Screen title */}
        <View style={{ padding: 16 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 16 }}>
            {t('configTestScreenTitle')}
          </Text>
        </View>
        <Text>Status: {status}</Text>
        <View style={{ flexDirection: 'row', gap: 10, marginBottom: 10 }}>
          <Button title="Reload Config" onPress={reload} />
          <Button title="Clear Saved Config" color="#d32f2f" onPress={handleClear} />
        </View>
        {clearMsg ? <Text style={styles.clearedMsg}>{clearMsg}</Text> : null}
        {status === 'loading' && <ActivityIndicator style={{ marginVertical: 20 }} />}
        <Text style={styles.subtitle}>Config Source & Status:</Text>
        <View style={styles.metaBox}>
          <Text>Source: <Text style={styles.bold}>{meta?.source}</Text></Text>
          <Text>Last Fetch: <Text style={styles.bold}>{formatDate(meta?.timestamp)}</Text></Text>
          <Text>Expiry: <Text style={styles.bold}>{formatDate(meta?.expiry)}</Text></Text>
          <Text>Last Fetch Status: <Text style={styles.bold}>{meta?.lastFetchStatus}</Text></Text>
        </View>
        <Text style={styles.subtitle}>Config Log:</Text>
        <View style={styles.logBox}>
          {(meta?.log || []).map((msg: string, i: number) => (
            <Text key={i} style={styles.logLine}>{msg}</Text>
          ))}
        </View>
        <Text style={styles.subtitle}>Config JSON:</Text>
        <View style={styles.jsonBox}>
          <Text selectable>{configString}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#FFF9C4',
    flexGrow: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 20,
  },
  metaBox: {
    backgroundColor: '#e0f7fa',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
    marginBottom: 10,
  },
  bold: {
    fontWeight: 'bold',
  },
  logBox: {
    backgroundColor: '#f9fbe7',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
    marginBottom: 10,
    maxHeight: 120,
  },
  logLine: {
    fontSize: 12,
    color: '#333',
  },
  jsonBox: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  clearedMsg: {
    color: '#388e3c',
    fontWeight: 'bold',
    marginBottom: 10,
    marginTop: -10,
  },
}); 