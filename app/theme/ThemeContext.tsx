import { Theme } from '@react-navigation/native';
import React, { createContext, ReactNode, useContext, useState } from 'react';

// 1. Define the Base Color Palette
// These are the raw color values.
const baseColors = {
  // Primary Tints
  tintLight: '#0a7ea4', // Previously tintColorLight
  tintDark: '#ffffff',  // Previously tintColorDark

  // Neutral Shades
  neutral900: '#11181C', // For light mode text
  neutral800: '#151718', // For dark mode background
  neutral700: '#374151', // Medium dark gray
  neutral600: '#4B5563', // Medium gray
  neutral500: '#687076', // For light mode icons, default tab icons
  neutral400: '#9BA1A6', // For dark mode icons, default tab icons
  neutral300: '#D1D5DB', // Light gray
  neutral200: '#E5E7EB', // Very light gray
  neutral100: '#ECEDEE', // For dark mode text
  white: '#ffffff',      // For light mode background
  black: '#000000',      // Pure black (if needed)
  
  // Status colors
  red500: '#D32F2F',     // For error text
  red400: '#F87171',     // Lighter red for error surfaces
  // Add a new color for ResortLogo fallback
  resortLogoFallbackBackground: '#2D9CDB', // A blue shade for fallback background
  red600: '#C62828',     // Darker red
  green500: '#22C55E',   // Success green
  green600: '#16A34A',   // Darker green
  yellow500: '#EAB308',  // Warning yellow
  yellow600: '#CA8A04',  // Darker yellow
  blue500: '#3B82F6',    // Info blue
  blue600: '#2563EB',    // Darker blue
  
  // Button colors
  primary500: '#3182CE',  // Primary button
  primary600: '#2C5282',  // Primary button hover
  secondary500: '#E2E8F0', // Secondary button
  secondary600: '#CBD5E0', // Secondary button hover
  
  // Gradient colors
  gradientPink: '#ff6ec4',
  gradientCyan: '#17ead9',
  gradientYellow: '#fff800',
  gradientBlue1: '#1E3A8A',
  gradientBlue2: '#3B82F6',
  
  // Snow/Weather specific colors
  weatherSnow: '#2b7ec1',        // Snow-related data display
  weatherHighlight: 'rgb(254, 138, 13)',   // Weather data highlights
  
  // Overlay/Surface colors
  overlayDark: 'rgba(0,0,0,0.2)',         // Semi-transparent dark overlays
  overlayLight: '#EFEFEF',  // Light overlays
  overlayMedium: 'rgba(255,255,255,0.7)', // Medium opacity overlays
  surfaceAccent: 'rgba(55, 65, 81, 0.12)', // Light gray surfaces
  surfaceSuccess: 'rgba(68, 192, 113, 0.12)', // Success backgrounds
  surfaceError: 'rgba(239, 68, 68, 0.2)',   // Error backgrounds
  surfaceErrorLight: 'rgba(248, 113, 113, 0.1)', // Very light error surface
  
  // Card gradient collections
  cardGradientBlue1: '#1e3a8a',
  cardGradientBlue2: '#3730a3',
  cardGradientPurple1: '#4c1d95',
  cardGradientPurple2: '#581c87',
  cardGradientTeal1: '#164e63',
  cardGradientTeal2: '#155e75',
  cardGradientRed1: '#b91c1c',
  cardGradientRed2: '#dc2626',
  cardGradientGreen1: '#166534',
  cardGradientGreen2: '#15803d',
  cardGradientDefault1: '#ffffff',
  cardGradientDefault2: '#ffffff',
  
  // Additional text shades
  textDark: '#383838',          // Dark text on light backgrounds
  textOnCard: '#333',           // Primary dark text on card surfaces
  textOnCardSecondary: '#222',  // Secondary dark text on cards
  
  // Recommendation card colors
  recommendationBorder: '#FF9EEF',
  recommendationGradient1: '#FF9EEF', // Pink
  recommendationGradient2: '#18FF7C', // Green
  recommendationGradient3: '#DEFF65', // Yellow-green
};

// 2. Define Semantic Color Names and Mappings
// These names describe the UI element's purpose.
export interface SemanticColors {
  // Core colors
  text: string;
  textSecondary: string;
  textMuted: string;
  background: string;
  backgroundSecondary: string;
  card: string;
  border: string;
  shadow: string;
  
  // Text variants
  textOnDark: string;           // White text on dark backgrounds
  textDimmed: string;           // Semi-transparent white text (0.8)
  textFaded: string;            // More transparent white text (0.7)
  textOnCard: string;           // Dark text on light card surfaces
  textOnCardSecondary: string;  // Secondary dark text on cards
  
  // Interactive elements
  tint: string;
  primary: string;
  primaryText: string;
  secondary: string;
  secondaryText: string;
  
  // Icons
  icon: string;
  iconSecondary: string;
  tabIconDefault: string;
  tabIconSelected: string;
  
  // Status colors
  success: string;
  successText: string;
  warning: string;
  warningText: string;
  error: string;
  errorLight: string;
  errorText: string;
  info: string;
  infoText: string;
  
  // Background/Surface variants
  backgroundOverlay: string;    // Semi-transparent dark overlays
  backgroundOverlayLight: string; // Semi-transparent light overlays
  backgroundAccent: string;     // Light accent backgrounds
  surfaceSuccess: string;       // Success state surfaces
  surfaceError: string;         // Error state surfaces
  surfaceErrorLight: string;    // Very light error surface
  surfaceHover: string;         // Interactive hover states
  surfacePressed: string;       // Interactive pressed states
  
  // Weather-specific colors
  weatherSnow: string;          // Snow-related data display
  weatherHighlight: string;     // Weather data highlights
  weatherTemperature: string;   // Temperature displays
  
  // Card gradient arrays
  cardGradients: {
    blue: [string, string];
    purple: [string, string];
    teal: [string, string];
    red: [string, string];
    green: [string, string];
    default: [string, string];
  };
  
  // Component-specific
  leaderboardAccent: string;    // Leaderboard row accents
  heroOverlay: string;          // Hero section overlays
  
  // Special purpose
  heroText: string;
  logoPlaceholderBackground: string;
  placeholderBackground: string;
  gradientStart: string;
  gradientEnd: string;
  recommendationBorder: string;
  recommendationGradient1: string;
  recommendationGradient2: string;
  recommendationGradient3: string;
  
  // Legacy colors (to be phased out)
  trailMapButtonBackground: string;
  // Add a new color for ResortLogo fallback
  resortLogoFallbackBackground: string;
}

const lightSemanticColors: SemanticColors = {
  // Core colors
  text: baseColors.neutral900,
  textSecondary: baseColors.neutral600,
  textMuted: baseColors.neutral500,
  background: baseColors.white,
  backgroundSecondary: baseColors.neutral100,
  card: baseColors.white,
  border: baseColors.neutral200,
  shadow: baseColors.neutral500,
  
  // Text variants
  textOnDark: baseColors.white,
  textDimmed: baseColors.overlayLight,
  textFaded: baseColors.overlayMedium,
  textOnCard: baseColors.textOnCard,
  textOnCardSecondary: baseColors.textOnCardSecondary,
  
  // Interactive elements
  tint: baseColors.tintLight,
  primary: baseColors.primary500,
  primaryText: baseColors.white,
  secondary: baseColors.secondary500,
  secondaryText: baseColors.neutral700,
  
  // Icons
  icon: baseColors.neutral500,
  iconSecondary: baseColors.neutral400,
  tabIconDefault: baseColors.neutral500,
  tabIconSelected: baseColors.tintLight,
  
  // Status colors
  success: baseColors.green500,
  successText: baseColors.white,
  warning: baseColors.yellow500,
  warningText: baseColors.neutral900,
  error: baseColors.red500,
  errorLight: baseColors.red400,
  errorText: baseColors.white,
  info: baseColors.blue500,
  infoText: baseColors.white,
  
  // Background/Surface variants
  backgroundOverlay: baseColors.overlayDark,
  backgroundOverlayLight: baseColors.overlayLight,
  backgroundAccent: baseColors.surfaceAccent,
  surfaceSuccess: baseColors.surfaceSuccess,
  surfaceError: baseColors.surfaceError,
  surfaceErrorLight: baseColors.surfaceErrorLight,
  surfaceHover: baseColors.neutral100,
  surfacePressed: baseColors.neutral200,
  
  // Weather colors
  weatherSnow: baseColors.weatherSnow,
  weatherHighlight: baseColors.weatherHighlight,
  weatherTemperature: baseColors.neutral900,
  
  // Card gradients
  cardGradients: {
    blue: [baseColors.cardGradientBlue1, baseColors.cardGradientBlue2],
    purple: [baseColors.cardGradientPurple1, baseColors.cardGradientPurple2],
    teal: [baseColors.cardGradientTeal1, baseColors.cardGradientTeal2],
    red: [baseColors.cardGradientRed1, baseColors.cardGradientRed2],
    green: [baseColors.cardGradientGreen1, baseColors.cardGradientGreen2],
    default: [baseColors.cardGradientDefault1, baseColors.cardGradientDefault2],
  },
  
  // Component specific
  leaderboardAccent: baseColors.surfaceAccent,
  heroOverlay: baseColors.overlayDark,
  
  // Special purpose
  heroText: baseColors.white,
  logoPlaceholderBackground: baseColors.neutral200,
  placeholderBackground: baseColors.neutral100,
  gradientStart: baseColors.gradientBlue1,
  gradientEnd: baseColors.gradientBlue2,
  recommendationBorder: baseColors.recommendationBorder,
  recommendationGradient1: baseColors.recommendationGradient1,
  recommendationGradient2: baseColors.recommendationGradient2,
  recommendationGradient3: baseColors.recommendationGradient3,
  
  // Legacy colors (to be phased out)
  trailMapButtonBackground: baseColors.tintLight,
  // Add a new color for ResortLogo fallback
  resortLogoFallbackBackground: baseColors.resortLogoFallbackBackground,
};

const darkSemanticColors: SemanticColors = {
  // Core colors
  text: baseColors.neutral100,
  textSecondary: baseColors.neutral300,
  textMuted: baseColors.neutral400,
  background: baseColors.neutral800,
  backgroundSecondary: baseColors.neutral900,
  card: baseColors.neutral900,
  border: baseColors.neutral600,
  shadow: baseColors.black,
  
  // Text variants
  textOnDark: baseColors.white,
  textDimmed: baseColors.overlayLight,
  textFaded: baseColors.overlayMedium,
  textOnCard: baseColors.neutral100,     // Light text on dark cards
  textOnCardSecondary: baseColors.neutral200, // Secondary light text on dark cards
  
  // Interactive elements
  tint: baseColors.tintDark,
  primary: baseColors.blue500,
  primaryText: baseColors.white,
  secondary: baseColors.neutral700,
  secondaryText: baseColors.neutral200,
  
  // Icons
  icon: baseColors.neutral400,
  iconSecondary: baseColors.neutral500,
  tabIconDefault: baseColors.neutral400,
  tabIconSelected: baseColors.tintDark,
  
  // Status colors
  success: baseColors.green500,
  successText: baseColors.white,
  warning: baseColors.yellow500,
  warningText: baseColors.neutral900,
  error: baseColors.red500,
  errorLight: baseColors.red400,
  errorText: baseColors.white,
  info: baseColors.blue500,
  infoText: baseColors.white,
  
  // Background/Surface variants
  backgroundOverlay: baseColors.overlayDark,
  backgroundOverlayLight: '#666666', // Dimmer overlay for dark mode
  backgroundAccent: 'rgba(75, 85, 99, 0.3)',       // Darker accent for dark mode
  surfaceSuccess: 'rgba(34, 197, 94, 0.15)',       // Dimmer success surface
  surfaceError: 'rgba(239, 68, 68, 0.15)',         // Dimmer error surface
  surfaceErrorLight: 'rgba(248, 113, 113, 0.08)',  // Very light error surface for dark mode
  surfaceHover: baseColors.neutral700,
  surfacePressed: baseColors.neutral600,
  
  // Weather colors
  weatherSnow: baseColors.weatherSnow,
  weatherHighlight: baseColors.weatherHighlight,
  weatherTemperature: baseColors.neutral100,
  
  // Card gradients (same as light mode)
  cardGradients: {
    blue: [baseColors.cardGradientBlue1, baseColors.cardGradientBlue2],
    purple: [baseColors.cardGradientPurple1, baseColors.cardGradientPurple2],
    teal: [baseColors.cardGradientTeal1, baseColors.cardGradientTeal2],
    red: [baseColors.cardGradientRed1, baseColors.cardGradientRed2],
    green: [baseColors.cardGradientGreen1, baseColors.cardGradientGreen2],
    default: [baseColors.cardGradientDefault1, baseColors.cardGradientDefault2],
  },
  
  // Component specific
  leaderboardAccent: 'rgba(75, 85, 99, 0.3)',
  heroOverlay: baseColors.overlayDark,
  
  // Special purpose
  heroText: baseColors.white,
  logoPlaceholderBackground: baseColors.neutral600,
  placeholderBackground: baseColors.neutral700,
  gradientStart: baseColors.gradientBlue1,
  gradientEnd: baseColors.gradientBlue2,
  recommendationBorder: baseColors.recommendationBorder,
  recommendationGradient1: baseColors.recommendationGradient1,
  recommendationGradient2: baseColors.recommendationGradient2,
  recommendationGradient3: baseColors.recommendationGradient3,
  
  // Legacy colors (to be phased out)
  trailMapButtonBackground: baseColors.tintDark,
  // Add a new color for ResortLogo fallback
  resortLogoFallbackBackground: '#205072', // A darker blue for fallback background in dark mode
};

// 3. Define the Theme Context Type
interface ThemeContextType {
  colors: SemanticColors;
  isDark: boolean;
  // We could add a function to manually set theme if needed:
  // setTheme: (theme: 'light' | 'dark') => void;
}

// Create the Context with a default value (important for TypeScript)
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 4. Create the ThemeProvider Component
interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Fixed to light theme - no longer follows system color scheme
  const [isDark] = useState(false);

  const currentColors = isDark ? darkSemanticColors : lightSemanticColors;

  const contextValue: ThemeContextType = {
    colors: currentColors,
    isDark,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// 5. Create a Hook to use the Theme
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// 6. Create React Navigation Themes
export const lightNavigationTheme: Theme = {
  dark: false,
  colors: {
    primary: baseColors.tintLight,
    background: baseColors.white,
    card: baseColors.white,
    text: baseColors.neutral900,
    border: baseColors.neutral200,
    notification: baseColors.red500,
  },
  fonts: {
    regular: { fontFamily: 'System', fontWeight: '400' },
    medium: { fontFamily: 'System', fontWeight: '500' },
    bold: { fontFamily: 'System', fontWeight: '600' },
    heavy: { fontFamily: 'System', fontWeight: '700' },
  },
};

export const darkNavigationTheme: Theme = {
  dark: true,
  colors: {
    primary: baseColors.tintDark,
    background: baseColors.neutral800,
    card: baseColors.neutral900,
    text: baseColors.neutral100,
    border: baseColors.neutral600,
    notification: baseColors.red500,
  },
  fonts: {
    regular: { fontFamily: 'System', fontWeight: '400' },
    medium: { fontFamily: 'System', fontWeight: '500' },
    bold: { fontFamily: 'System', fontWeight: '600' },
    heavy: { fontFamily: 'System', fontWeight: '700' },
  },
};

// Default export for the ThemeProvider
export default ThemeProvider;

// Optional: Export base colors if they need to be accessed directly anywhere (rare)
// export { baseColors };
