{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"SkiResort": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the resort"}, "name": {"type": "string", "description": "Display name of the ski resort"}, "location": {"type": "object", "properties": {"city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}}, "required": ["country"]}, "logo": {"type": "string", "description": "URL or asset path to resort logo"}, "coordinates": {"type": "object", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}}}, "backgroundImage": {"type": "string", "description": "URL or asset path to resort background image"}, "description": {"type": "string", "description": "Detailed description of the ski resort"}, "passId": {"type": "string", "description": "Reference to the resort pass ID"}, "website": {"type": "string", "description": "Official website URL for the ski resort", "format": "uri"}}, "required": ["id", "name", "location"]}, "SnowfallMeasurement": {"type": "object", "properties": {"resortId": {"type": "string"}, "inches": {"type": "number", "minimum": 0}, "timeframe": {"type": "string", "enum": ["24_hours", "5_days", "week", "season"]}, "measurementType": {"type": "string", "enum": ["recent", "forecast", "total"]}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["resortId", "inches", "timeframe", "measurementType", "timestamp"]}, "ResortPass": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the resort pass"}, "name": {"type": "string", "description": "Display name of the resort pass"}, "logo": {"type": "string", "description": "URL or asset path to pass logo"}, "website": {"type": "string", "format": "uri", "description": "Official website URL for the resort pass"}}, "required": ["id", "name", "logo", "website"]}, "LeaderboardEntry": {"type": "object", "properties": {"resort": {"$ref": "#/definitions/SkiResort"}, "snowfall": {"type": "number", "minimum": 0}, "rank": {"type": "integer", "minimum": 1}}, "required": ["resort", "snowfall", "rank"]}}}