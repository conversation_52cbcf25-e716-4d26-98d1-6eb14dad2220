import { LocationProvider } from '@/contexts/LocationContext';
import { ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import CacheBypassIndicator from '../components/ui/CacheBypassIndicator';
import { ConfigProvider } from '../contexts/ConfigContext';
import { ThemeProvider as AppThemeProvider, lightNavigationTheme } from './theme/ThemeContext';

const queryClient = new QueryClient();

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ConfigProvider>
      <AppThemeProvider>
        <LocationProvider>
          <QueryClientProvider client={queryClient}>
            <NavigationThemeProvider value={lightNavigationTheme}>
              <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
              <CacheBypassIndicator />
            </NavigationThemeProvider>
          </QueryClientProvider>
        </LocationProvider>
      </AppThemeProvider>
    </ConfigProvider>
  );
}
