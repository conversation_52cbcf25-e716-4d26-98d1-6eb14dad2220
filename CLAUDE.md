# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React Native Expo weather app focused on ski resorts, called "twc-snow". It provides ski conditions, weather forecasts, resort information, and leaderboards for snow enthusiasts. The app uses file-based routing with Expo Router and integrates with Weather.com APIs.

## Development Commands

### Essential Commands
- `npm install` - Install dependencies
- `npm start` - Start with prebuild and Expo dev server
- `npm run prebuild` - Build Pangea bundle and prepare assets
- `npm run android` - Run Android build with prebuild
- `npm run ios` - Run iOS build with prebuild
- `npm run web` - Start web development server
- `npm run lint` - Run ESLint

### Environment Setup
Copy `.env.example` to `.env` and configure:
- `WEATHER_API_KEY` - Weather API key
- `MAPBOX_TOKEN` - Mapbox access token  
- `SUN_API_TOKEN` - Weather.com/Sun API token (required for resort data)
- `EAS_PROJECT_ID` - Expo Application Services project ID

## Architecture

### Core Structure
- **Expo Router**: File-based routing with tabs layout in `app/(tabs)/`
- **React Query**: Data fetching and caching via `@tanstack/react-query`
- **Context Providers**: LocationContext for GPS, ThemeContext for theming
- **Services Layer**: Centralized API calls in `services/` directory
- **Custom Hooks**: Data fetching hooks in `hooks/` directory

### Key Directories
- `app/` - Expo Router pages and layouts
- `components/` - Reusable UI components
- `contexts/` - React context providers
- `hooks/` - Custom React hooks for data fetching
- `services/` - API service functions
- `constants/` - Environment and configuration constants
- `assets/` - Static assets including mock data and images
- `pangea-bundle/` - Embedded Vite project for map functionality

### Data Flow
1. LocationContext provides GPS coordinates
2. Custom hooks (useWeatherQuery, useSkiResortsQuery, etc.) fetch data using React Query
3. Components consume data via hooks and render UI
4. Services handle API communication with Weather.com endpoints

### Data Caching Strategy
All forecast and weather data hooks use React Query with 5-minute caching:
- `useWeatherQuery` - Current weather conditions (5min cache)
- `useHourlyForecastQuery` - 2-day hourly forecast (5min cache)  
- `useThreeDayForecast` - 3-day daily forecast (5min cache)
- Location-based cache keys ensure data is cached separately for each GPS coordinate

### API Integration
- **Weather.com API**: Primary data source using SUN_API_TOKEN
- **Leaderboard API**: AWS Lambda endpoint for resort rankings
- **Mock Data**: JSON files in `assets/mock-data/` for development

### Component Architecture
- Weather condition icons: Generated SVG components in `components/weather_conditions/`
- Resort cards and modals for detailed information
- Theming system: Fixed to light mode (does not follow system dark/light preferences)
- Custom tab bar with haptic feedback

### Build Process
The prebuild script (`scripts/build-pangea-bundle.js`) builds the Vite project in `pangea-bundle/` and copies output to `assets/` for embedding in the mobile app.

### TypeScript
- Shared types in `types.ts` for Resort, WeatherData, etc.
- Path alias `@/*` maps to project root
- Strict TypeScript configuration

### Platform Support
- iOS: Native iOS app with CocoaPods dependencies
- Android: Native Android build
- Web: Expo web support with Metro bundler

# Cache Bypass Debug Feature

## Overview
Added a debug configuration flag that bypasses all caching when the app is running. This is extremely useful for development and debugging to ensure you always get fresh data.

## Visual Indicator
When cache bypass is enabled in development mode, a small 🚫 indicator appears in the bottom right corner above the tab bar. This provides instant visual feedback about cache status without needing to check logs.

## Configuration

### Enable/Disable Cache Bypass
```json
{
  "debug": {
    "bypassCache": true  // Set to false to re-enable caching
  }
}
```

### Programmatic Control (Dev Mode Only)
```typescript
import ConfigService from './services/appConfigService';

// Toggle cache bypass on/off
const isEnabled = ConfigService.toggleCacheBypass();
console.log(`Cache bypass is now: ${isEnabled ? 'ENABLED' : 'DISABLED'}`);

// Set specific value
ConfigService.setDebugOverride('debug.bypassCache', true);
```

## How It Works

The bypass affects all caching layers:

1. **BaseApiService**: `getCachedData()` returns null, `setCachedData()` skips writing
2. **CacheHelpers**: Direct usage also respects the bypass flag
3. **All Services**: Any service extending BaseApiService automatically bypasses cache
4. **Visual Indicator**: Small 🚫 icon appears above tab bar when bypass is active

## Logging

When cache bypass is active, you'll see debug logs:
```
[ServiceName] [DEBUG] 🚫 Cache bypassed (debug.bypassCache=true) operation
[CacheHelpers] [DEBUG] 🚫 Cache write skipped (debug.bypassCache=true) Service:operation
```

## Usage Scenarios

- **API Development**: Test API changes without cache interference
- **Data Debugging**: Ensure you're seeing the latest data from APIs
- **Cache Testing**: Verify cache behavior by toggling on/off
- **Performance Testing**: Compare cached vs non-cached performance

## Important Notes

- ⚠️ **Development Only**: The toggle function and visual indicator only work in dev mode
- 📊 **Performance Impact**: Disabling cache increases API calls and loading times
- 🔄 **React Query Unaffected**: This only affects service-level caching, not React Query cache
- 💾 **Persistent Setting**: The bypass setting persists across app restarts in debug mode
- 👁️ **Visual Feedback**: The 🚫 indicator appears automatically when cache is bypassed

## Example Usage

```typescript
// In your service
export class MyService extends BaseApiService<MyData> {
  async fetchData(): Promise<MyData> {
    // This will automatically respect the bypass flag
    const cached = await this.getCachedData('fetchData', {});
    if (cached) return cached; // Will be null if bypass is enabled
    
    const freshData = await this.fetchFromAPI();
    await this.setCachedData('fetchData', {}, freshData); // Will be skipped if bypass is enabled
    return freshData;
  }
}
```