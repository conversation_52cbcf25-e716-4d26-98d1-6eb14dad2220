/**
 * Configuration interface for API services
 */
export interface ApiConfig {
  url: string;
  timeout: number;
  retries: number;
  ttl: number;
}

/**
 * Standard API error types
 */
export enum ApiErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Standardized API error class
 */
export class ApiError extends Error {
  constructor(
    public type: ApiErrorType,
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Service event map for type-safe event handling
 */
export interface ServiceEventMap {
  dataUpdated: any;
  dataFetched: any;
  cacheCleared: any;
  error: ApiError;
  locationChanged: { location: string };
  configChanged: { config: ApiConfig };
}

/**
 * Cache entry interface
 */
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * Standard validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
} 