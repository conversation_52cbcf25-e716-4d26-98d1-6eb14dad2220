import pangea from "@twc/pangea-web/mapbox";

// Patch console methods to forward logs to React Native WebView
(function () {
  const levels = ['log', 'warn', 'error', 'info', 'debug'];
  levels.forEach(level => {
    const original = console[level];
    console[level] = function (...args) {
      // Call the original console method
      original.apply(console, args);
      // Send to React Native if available
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        try {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'log',
            level,
            args
          }));
        } catch (err) {
          // Fallback: don't break logging
        }
      }
    };
  });
})();

function getUrlParam(name) {
    // First check if the parameter is available as a window variable (from React Native injection)
    if (name === 'SUN_API_TOKEN' && window.SUN_API_TOKEN) {
        console.log('[Pangea] 🔑 Found SUN_API_TOKEN in window variable');
        return window.SUN_API_TOKEN;
    }
    if (name === 'MAPBOX_TOKEN' && window.MAPBOX_TOKEN) {
        console.log('[Pangea] 🔑 Found MAPBOX_TOKEN in window variable');
        return window.MAPBOX_TOKEN;
    }
    
    // Fallback to URL parameters
    const url = new URL(window.location.href);
    return url.searchParams.get(name);
}

/**
 * Comprehensive event handling system for Pangea SDK
 * Follows patterns from the user guide with detailed logging
 */
function setupPangeaEventHandlers(map) {
  // Check if events should be enabled
  if (!window._pangeaEventsEnabled) {
    console.log('[Pangea Events] 🔇 Event handling disabled');
    return;
  }

  // Remove existing handlers first if any
  removePangeaEventHandlers();

  console.log('[Pangea Events] 🎧 Setting up comprehensive event handlers...');

  // Initialize handlers storage
  window._pangeaEventHandlers = {
    map: map,
    layerHandlers: new Map(),
    mapEventHandlers: []
  };

  // === MAP LIFECYCLE EVENTS ===
  console.log('[Pangea Events] 📍 Setting up map lifecycle events...');
  
  // Map loaded event
  if (map.loaded) {
    const loadedHandler = {
      handler: () => {
        console.log('[Pangea Events] ✨ Map is fully loaded and interactive!');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ type: 'pangeaMapEvent', event: 'loaded', data: {} }, '*');
        }
      }
    };
    map.loaded.add(loadedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.loaded, handler: loadedHandler });
  }

  // Style loaded event
  if (map.styleLoaded) {
    const styleLoadedHandler = {
      handler: () => {
        console.log('[Pangea Events] 🎨 Map style loaded successfully');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ type: 'pangeaMapEvent', event: 'styleLoaded', data: {} }, '*');
        }
      }
    };
    map.styleLoaded.add(styleLoadedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.styleLoaded, handler: styleLoadedHandler });
  }

  // === MAP MOVEMENT EVENTS ===
  console.log('[Pangea Events] 🗺️  Setting up map movement events...');
  
  // Move events
  if (map.moveStarted) {
    const moveStartedHandler = {
      handler: () => {
        console.log('[Pangea Events] 🚀 Map movement started');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ type: 'pangeaMapEvent', event: 'moveStarted', data: {} }, '*');
        }
      }
    };
    map.moveStarted.add(moveStartedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.moveStarted, handler: moveStartedHandler });
  }

  if (map.moveEnded) {
    const moveEndedHandler = {
      handler: () => {
        const center = map.geoCenter;
        console.log('[Pangea Events] 🛑 Map movement ended at:', {
          latitude: center.latitude.toFixed(4),
          longitude: center.longitude.toFixed(4),
          zoom: map.zoomLevel.toFixed(2),
          bearing: map.bearing.toFixed(1) + '°',
          pitch: map.pitch.toFixed(1) + '°'
        });
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'moveEnded', 
            data: {
              center: [center.longitude, center.latitude],
              zoom: map.zoomLevel,
              bearing: map.bearing,
              pitch: map.pitch
            }
          }, '*');
        }
      }
    };
    map.moveEnded.add(moveEndedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.moveEnded, handler: moveEndedHandler });
  }

  // === ZOOM EVENTS ===
  console.log('[Pangea Events] 🔍 Setting up zoom events...');
  
  if (map.zoomStarted) {
    const zoomStartedHandler = {
      handler: () => {
        console.log('[Pangea Events] 🔍 Zoom started from level:', map.zoomLevel.toFixed(2));
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'zoomStarted', 
            data: { zoomLevel: map.zoomLevel }
          }, '*');
        }
      }
    };
    map.zoomStarted.add(zoomStartedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.zoomStarted, handler: zoomStartedHandler });
  }

  if (map.zoomEnded) {
    const zoomEndedHandler = {
      handler: () => {
        console.log('[Pangea Events] 🎯 Zoom ended at level:', map.zoomLevel.toFixed(2));
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'zoomEnded', 
            data: { zoomLevel: map.zoomLevel }
          }, '*');
        }
      }
    };
    map.zoomEnded.add(zoomEndedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.zoomEnded, handler: zoomEndedHandler });
  }

  // === ROTATION EVENTS ===
  console.log('[Pangea Events] 🧭 Setting up rotation events...');
  
  if (map.rotateStarted) {
    const rotateStartedHandler = {
      handler: () => {
        console.log('[Pangea Events] 🔄 Map rotation started from:', map.bearing.toFixed(1) + '°');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'rotateStarted', 
            data: { bearing: map.bearing }
          }, '*');
        }
      }
    };
    map.rotateStarted.add(rotateStartedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.rotateStarted, handler: rotateStartedHandler });
  }

  if (map.rotateEnded) {
    const rotateEndedHandler = {
      handler: () => {
        console.log('[Pangea Events] 🧭 Rotation ended at:', map.bearing.toFixed(1) + '°');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'rotateEnded', 
            data: { bearing: map.bearing }
          }, '*');
        }
      }
    };
    map.rotateEnded.add(rotateEndedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.rotateEnded, handler: rotateEndedHandler });
  }

  // === TILT EVENTS ===
  console.log('[Pangea Events] 📐 Setting up tilt events...');
  
  if (map.tiltStarted) {
    const tiltStartedHandler = {
      handler: () => {
        console.log('[Pangea Events] 📐 Map tilt started from:', map.pitch.toFixed(1) + '°');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'tiltStarted', 
            data: { pitch: map.pitch }
          }, '*');
        }
      }
    };
    map.tiltStarted.add(tiltStartedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.tiltStarted, handler: tiltStartedHandler });
  }

  if (map.tiltEnded) {
    const tiltEndedHandler = {
      handler: () => {
        console.log('[Pangea Events] 📐 Tilt ended at:', map.pitch.toFixed(1) + '°');
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaMapEvent', 
            event: 'tiltEnded', 
            data: { pitch: map.pitch }
          }, '*');
        }
      }
    };
    map.tiltEnded.add(tiltEndedHandler);
    window._pangeaEventHandlers.mapEventHandlers.push({ source: map.tiltEnded, handler: tiltEndedHandler });
  }



  console.log('[Pangea Events] ✅ All map event handlers set up successfully');
}

/**
 * Remove all Pangea event handlers
 */
function removePangeaEventHandlers() {
  if (!window._pangeaEventHandlers) return;

  console.log('[Pangea Events] 🧹 Removing all event handlers...');

  // Remove map event handlers
  if (window._pangeaEventHandlers.mapEventHandlers) {
    window._pangeaEventHandlers.mapEventHandlers.forEach(({ source, handler }) => {
      try {
        if (source && source.remove && handler) {
          source.remove(handler);
        }
      } catch (error) {
        console.warn('[Pangea Events] ⚠️ Error removing map event handler:', error);
      }
    });
  }



  // Remove layer event handlers
  if (window._pangeaEventHandlers.layerHandlers) {
    window._pangeaEventHandlers.layerHandlers.forEach((layerInfo, layerId) => {
      removeLayerEventHandlers(layerId);
    });
  }

  // Clear the handlers storage
  window._pangeaEventHandlers = null;
  
  console.log('[Pangea Events] ✅ All event handlers removed');
}

/**
 * Set up event handlers for a specific layer
 */
function setupLayerEventHandlers(layer, layerId) {
  if (!window._pangeaEventsEnabled) return;
  if (!window._pangeaEventHandlers) return;

  console.log(`[Pangea Events] 🗂️  Setting up layer events for: ${layerId}`);

  const layerHandlers = [];

  // Layer loaded event
  if (layer.loaded) {
    const loadedHandler = {
      handler: () => {
        console.log(`[Pangea Events] ✨ Layer '${layerId}' loaded successfully`);
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ 
            type: 'pangeaLayerEvent', 
            event: 'loaded', 
            data: { layerId }
          }, '*');
        }
      }
    };
    layer.loaded.add(loadedHandler);
    layerHandlers.push({ source: layer.loaded, handler: loadedHandler });
  }

  // Layer changed event
  if (layer.changed) {
    const changedHandler = {
      handler: (changes) => {
        console.log(`[Pangea Events] 🔄 Layer '${layerId}' changed:`, changes);
        if (window.parent && window.self !== window.top) {
          // Sanitize changes object for postMessage - only send serializable data
          const sanitizedChanges = {};
          if (changes) {
            // Only include basic properties that can be serialized
            if (typeof changes === 'object') {
              Object.keys(changes).forEach(key => {
                const value = changes[key];
                if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                  sanitizedChanges[key] = value;
                } else if (value && typeof value.toString === 'function') {
                  sanitizedChanges[key] = `[${typeof value}]`;
                }
              });
            }
          }
          
          window.parent.postMessage({ 
            type: 'pangeaLayerEvent', 
            event: 'changed', 
            data: { layerId, changes: sanitizedChanges }
          }, '*');
        }
      }
    };
    layer.changed.add(changedHandler);
    layerHandlers.push({ source: layer.changed, handler: changedHandler });
  }

  // Layer error event
  if (layer.error) {
    const errorHandler = {
      handler: (error) => {
        console.error(`[Pangea Events] ❌ Layer '${layerId}' error:`, error);
        if (window.parent && window.self !== window.top) {
          // Sanitize error object for postMessage
          const sanitizedError = {
            message: error?.message || 'Unknown error',
            name: error?.name || 'Error',
            type: typeof error
          };
          
          window.parent.postMessage({ 
            type: 'pangeaLayerEvent', 
            event: 'error', 
            data: { layerId, error: sanitizedError }
          }, '*');
        }
      }
    };
    layer.error.add(errorHandler);
    layerHandlers.push({ source: layer.error, handler: errorHandler });
  }

  // Store handlers for cleanup
  window._pangeaEventHandlers.layerHandlers.set(layerId, {
    layer: layer,
    handlers: layerHandlers
  });
}

/**
 * Remove event handlers for a specific layer
 */
function removeLayerEventHandlers(layerId) {
  if (!window._pangeaEventHandlers || !window._pangeaEventHandlers.layerHandlers.has(layerId)) {
    return;
  }

  console.log(`[Pangea Events] 🧹 Removing layer events for: ${layerId}`);

  const layerInfo = window._pangeaEventHandlers.layerHandlers.get(layerId);
  if (layerInfo && layerInfo.handlers) {
    layerInfo.handlers.forEach(({ source, handler }) => {
      try {
        if (source && source.remove && handler) {
          source.remove(handler);
        }
      } catch (error) {
        console.warn(`[Pangea Events] ⚠️ Error removing layer event handler for ${layerId}:`, error);
      }
    });
  }

  window._pangeaEventHandlers.layerHandlers.delete(layerId);
}

/**
 * Set up animation event handlers
 */
function setupAnimationEventHandlers() {
  if (!window._pangeaEventsEnabled || !window._pangeaMapInstance) return;
  
  const map = window._pangeaMapInstance;
  if (!map.animator) return;

  console.log('[Pangea Events] 🎬 Setting up animation event handlers...');

  // Animation started event
  if (map.animator.started) {
    map.animator.started.add({
      handler: () => {
        console.log('[Pangea Events] ▶️ Animation started');
        
        const eventData = {
          type: 'pangeaAnimationEvent', 
          event: 'started', 
          data: {
            totalFrames: map.animator.length,
            currentFrame: map.animator.playhead.frame
          }
        };
        
        // Send to React Native WebView
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
          window.ReactNativeWebView.postMessage(JSON.stringify(eventData));
        }
        
        // Send to parent window (testharness)
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage(eventData, '*');
        }
      }
    });
  }

  // Animation stopped event
  if (map.animator.stopped) {
    map.animator.stopped.add({
      handler: () => {
        console.log('[Pangea Events] ⏸️ Animation stopped');
        
        const eventData = {
          type: 'pangeaAnimationEvent', 
          event: 'stopped', 
          data: {
            totalFrames: map.animator.length,
            currentFrame: map.animator.playhead.frame
          }
        };
        
        // Send to React Native WebView
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
          window.ReactNativeWebView.postMessage(JSON.stringify(eventData));
        }
        
        // Send to parent window (testharness)
        if (window.parent && window.self !== window.top) {
          window.parent.postMessage(eventData, '*');
        }
      }
    });
  }

  // Frame changed events
  if (map.animator.playhead && map.animator.playhead.frameChanged) {
    const frameChangeHandler = (e) => {
      console.log('[Pangea Events] 🎞️ Animation frame changed:', {
        frame: map.animator.playhead.frame,
        totalFrames: map.animator.length,
        time: map.animator.time.toISOString()
      });
      
      const eventData = {
        type: 'pangeaAnimationEvent', 
        event: 'frameChanged', 
        data: {
          currentFrame: map.animator.playhead.frame,
          totalFrames: map.animator.length,
          currentTime: map.animator.time.toISOString()
        }
      };
      
      // Send to React Native WebView
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        window.ReactNativeWebView.postMessage(JSON.stringify(eventData));
      }
      
      // Send to parent window (testharness)
      if (window.parent && window.self !== window.top) {
        window.parent.postMessage(eventData, '*');
      }
    };

    // Use the recursive once pattern from the working code
    function addFrameListener() {
      map.animator.playhead.frameChanged.once({
        handler: function(e) {
          frameChangeHandler(e);
          // Re-add listener for continuous updates
          if (window._pangeaEventsEnabled && map.animator.playhead.frameChanged) {
            addFrameListener();
          }
        }
      });
    }
    
    addFrameListener();
  }

  console.log('[Pangea Events] ✅ Animation event handlers set up successfully');
}

/**
 * Initializes the Pangea map on the page with detailed logging for debugging.
 */
export default function createMap() {
  window.addEventListener("DOMContentLoaded", () => {
    (async function() {
      console.log("[Pangea] Starting map initialization...");
      
      // Initialize events flag - default to enabled
      if (window._pangeaEventsEnabled === undefined) {
        window._pangeaEventsEnabled = true;
      }
      
      try {
        let mapboxToken = getUrlParam('MAPBOX_TOKEN') || window.MAPBOX_TOKEN;
        let sunApiToken = getUrlParam('SUN_API_TOKEN') || window.SUN_API_TOKEN;
        if (!mapboxToken) {
          throw new Error('MAPBOX_TOKEN not set on window or URL');
        }
        if (!sunApiToken) {
          throw new Error('SUN_API_TOKEN not set on window or URL');
        }

        let initialGeoCenter = [-98.98, 32.10];
        if (window.INITIAL_LOCATION && typeof window.INITIAL_LOCATION.latitude === 'number' && typeof window.INITIAL_LOCATION.longitude === 'number') {
          initialGeoCenter = [window.INITIAL_LOCATION.longitude, window.INITIAL_LOCATION.latitude];
        }

        let initialZoomLevel = 4;
        if (typeof window.INITIAL_ZOOM_LEVEL === 'number') {
          initialZoomLevel = window.INITIAL_ZOOM_LEVEL;
        }

        let initialMapStyle = "mapbox://styles/mapbox/standard";
        if (typeof window.INITIAL_MAP_STYLE === 'string') {
          initialMapStyle = window.INITIAL_MAP_STYLE;
        }
        
        console.log("[Pangea] Creating MapboxViewport...");
        const map = await pangea.mapbox.MapboxViewport.create(
          "pangeaMap",
          mapboxToken,
          {
            basemap: initialMapStyle,
            geoCenter: initialGeoCenter,
            zoomLevel: initialZoomLevel,
          }
        );
        
        console.log("[Pangea] MapboxViewport created successfully.");

        window._pangeaMapInstance = map;
        window._pangeaActiveLayers = new Map();
        window._pangeaAnimatableLayers = new Map();
        window._pangeaAnimationReady = false;
        window._pangeaAnimationUpdateInterval = null;
        window._pangeaEventListenersSetup = false;

        // Set up comprehensive event handling
        setupPangeaEventHandlers(map);

        if (window.parent && window.self !== window.top) {
          window.parent.postMessage({ type: 'PangeaMapReady' }, '*');
        }

        console.log("[Pangea] Map initialization complete. Ready for commands.");

      } catch (err) {
        console.error("[Pangea] Failed to initialize Pangea map:", err);
        const pangeaMapDiv = document.getElementById('pangeaMap');
        if (pangeaMapDiv) {
            pangeaMapDiv.innerHTML = `<div style="color: red; padding: 20px;"><strong>Map Initialization Failed:</strong><br/>${err.message}</div>`;
        }
      }
    })();
  });

  // Listen for messages from React Native for user location
  window.addEventListener('message', function(event) {
    const postResponse = (requestId, status, data) => {
      if (requestId && window.parent && window.self !== window.top) {
        window.parent.postMessage({ requestId, status, data }, '*');
      }
    };

    console.log('[Pangea] 📩 Received message:', event.data);
    const { type, payload, requestId } = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
    console.log('[Pangea] 📩 Parsed message - type:', type, 'payload:', payload, 'requestId:', requestId);

    if (!type) {
      console.log('[Pangea] ❌ No message type provided');
      return;
    }
    
    console.log('[Pangea] 🔍 Checking map instance readiness for command:', type);
    console.log('[Pangea] 🔍 Map instance exists:', !!window._pangeaMapInstance);
    
    if (!window._pangeaMapInstance) {
      console.warn('[Pangea] Map instance not ready for command:', type);
      postResponse(requestId, 'error', { message: 'Map instance not ready' });
      return;
    }
    
    console.log('[Pangea] 🔍 Entering try block for command:', type);

    try {
      const map = window._pangeaMapInstance;

      switch (type) {
        case 'toggleEvents': {
          const { enabled } = payload;
          window._pangeaEventsEnabled = enabled;
          console.log(`[Pangea Events] 📣 Events ${enabled ? 'enabled' : 'disabled'}`);
          
          if (enabled) {
            // Re-setup event handlers
            setupPangeaEventHandlers(map);
            // Re-setup animation handlers if available
            if (window._pangeaAnimationReady) {
              setupAnimationEventHandlers();
            }
          } else {
            // Remove all event handlers
            removePangeaEventHandlers();
          }
          
          postResponse(requestId, 'success', { eventsEnabled: enabled });
          break;
        }
        case 'setUserLocation': {
          const { latitude, longitude } = payload;
          if (typeof latitude !== 'number' || typeof longitude !== 'number') {
            throw new Error('Invalid coordinates provided.');
          }

          const userLocation = [longitude, latitude];
          const layerId = 'user-location';
          
          // If layer exists, remove it first to update its position
          if (window._pangeaActiveLayers.has(layerId)) {
              const { layer } = window._pangeaActiveLayers.get(layerId);
              map.layers.remove(layer);
              window._pangeaActiveLayers.delete(layerId);
          }
      
          // Create and add the new layer with the location marker
          const marker = new pangea.overlays.CircleMarker(
              new pangea.geography.GeoPoint(longitude, latitude), 
              { 
                  radius: 8,
                  fill: new pangea.visuals.Color(0, 191, 255), // #00BFFF
                  stroke: { color: pangea.visuals.Color.WHITE, width: 2 }
              }
          );
          const source = new pangea.sources.StaticOverlaySource([marker], { id: 'user-location-source' });
          const layer = new pangea.layers.OverlayLayer(source, { id: 'user-location-layer' });
          
          // Set up event handlers for this layer
          setupLayerEventHandlers(layer, layerId);
          
          map.layers.add(layer);
          window._pangeaActiveLayers.set(layerId, { source, layer });
          
          map.move({
            geoCenter: userLocation,
            zoomLevel: 10
          }, { duration: 1000 });
          
          console.log('[Pangea] User location set and map centered:', userLocation);
          postResponse(requestId, 'success', { latitude, longitude });
          break;
        }
        case 'addLayer': {
            console.log('[Pangea] 🗂️ Processing addLayer command for layerId:', payload.layerId);
            const { layerId } = payload;
            const sunApiToken = getUrlParam('SUN_API_TOKEN');
            console.log('[Pangea] 🗂️ SUN_API_TOKEN:', sunApiToken ? 'found' : 'missing');
            
            if (window._pangeaActiveLayers.has(layerId)) {
                postResponse(requestId, 'success', { message: `Layer ${layerId} already exists.` });
                return;
            }

            let source;
            if (layerId === 'satrad') {
                source = new pangea.sources.SunRasterSource("satrad", sunApiToken, { id: "SatRadSource" });
            } else if (layerId === 'snow') {
                source = new pangea.sources.SunGridSource("31:VAR01242FROM25501surface0MinuteAccumulation", sunApiToken, { id: "SnowAccumSource" });
            } else {
                postResponse(requestId, 'error', { message: `Unknown layerId: ${layerId}` });
                return;
            }
            
            source.ready.once({
                handler: () => {
                    let layer;
                    if (layerId === 'satrad') {
                        layer = new pangea.layers.RasterLayer(source, { id: "SatRadLayer" });
                        
                        // Set up animator immediately when source is ready
                        if (source.currentSequence && source.currentSequence.times) {
                            console.log(`[Pangea] Satellite layer ready with ${source.currentSequence.times.length} frames.`);
                            
                            // Set up animator BEFORE adding layer to map
                            const framesLength = source.currentSequence.times.length;
                            const startTime = source.currentSequence.times[framesLength - 1].instant;
                            
                            console.log(`[Pangea] Setting up animator for ${layerId} with ${framesLength} frames.`);
                            map.animator.startTime = startTime;
                            map.animator.storyboard = [{
                                frames: framesLength,
                                span: framesLength * 900000 // 15 minutes per frame
                            }];
                            map.animator.playhead.move(framesLength);
                            
                            // Connect source to animator BEFORE adding layer
                            source.animator = map.animator;
                            console.log(`[Pangea] Connected ${layerId} source to animator before adding layer.`);
                            
                            window._pangeaAnimatableLayers.set(layerId, { source, layer });
                            window._pangeaAnimationReady = true;
                            
                            // Setup animation event handlers
                            setupAnimationEventHandlers();
                            
                            // Notify UI that animation is available
                            console.log('[Pangea] 🎬 Attempting to send animationUpdate message...');
                            console.log('[Pangea] 🎬 Window context check:', {
                                hasParent: !!window.parent,
                                selfNotTop: window.self !== window.top,
                                hasReactNativeWebView: !!window.ReactNativeWebView,
                                hasPostMessage: !!window.ReactNativeWebView?.postMessage
                            });
                            
                            const data = {
                                active: true,
                                totalFrames: framesLength,
                                currentFrame: framesLength,
                                currentTime: startTime.toISOString(),
                                isPlaying: false
                            };
                            
                            console.log('[Pangea] 🎬 Animation data to send:', data);
                            
                            // Try both React Native WebView and parent window approaches
                            if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                                console.log('[Pangea] 🎬 Sending via ReactNativeWebView.postMessage');
                                window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'animationUpdate', data }));
                            } else if (window.parent && window.self !== window.top) {
                                console.log('[Pangea] 🎬 Sending via window.parent.postMessage');
                                window.parent.postMessage({ type: 'animationUpdate', data }, '*');
                            } else {
                                console.warn('[Pangea] 🎬 No suitable postMessage method found!');
                            }
                        }
                    } else {
                        const snowPalette = new pangea.visuals.Palette([
                            { value: 0.00025, color: [8, 193, 230, 1] },
                            { value: 0.03, color: [8, 155, 186, 1] },
                            { value: 0.07, color: [8, 123, 153, 1] },
                            { value: 0.12, color: [161, 145, 255, 1] },
                            { value: 0.20, color: [150, 96, 255, 1] },
                            { value: 0.30, color: [104, 42, 186, 1] },
                            { value: 0.45, color: [255, 73, 145, 1] },
                            { value: 0.60, color: [235, 165, 226, 1] },
                        ], { id: "Snow (M)", interpolation: "LINEAR", minimumColorExtended: false, precision: 5 });
                        const snowGridStyle = new pangea.visuals.GridRasterStyle(snowPalette, {});
                        layer = new pangea.layers.GridLayer(source, snowGridStyle, { id: "SnowAccumLayer" });
                        
                        // Set up animator for snow layer too
                        if (source.currentSequence && source.currentSequence.times) {
                            console.log(`[Pangea] Snow layer ready with ${source.currentSequence.times.length} frames.`);
                            
                            // Set up animator BEFORE adding layer to map
                            const framesLength = source.currentSequence.times.length;
                            const startTime = source.currentSequence.times[0].instant; // Snow starts from first frame
                            
                            console.log(`[Pangea] Setting up animator for ${layerId} with ${framesLength} frames.`);
                            map.animator.startTime = startTime;
                            map.animator.storyboard = [{
                                frames: framesLength,
                                span: framesLength * 3600000 // 1 hour per frame for snow
                            }];
                            map.animator.playhead.move(1);
                            
                            // Connect source to animator BEFORE adding layer
                            source.animator = map.animator;
                            console.log(`[Pangea] Connected ${layerId} source to animator before adding layer.`);
                            
                            window._pangeaAnimatableLayers.set(layerId, { source, layer });
                            window._pangeaAnimationReady = true;
                            
                            // Setup animation event handlers
                            setupAnimationEventHandlers();
                            
                            // Notify UI that animation is available
                            console.log('[Pangea] 🎬 Attempting to send animationUpdate message for snow...');
                            
                            const data = {
                                active: true,
                                totalFrames: framesLength,
                                currentFrame: 1,
                                currentTime: startTime.toISOString(),
                                isPlaying: false
                            };
                            
                            console.log('[Pangea] 🎬 Snow animation data to send:', data);
                            
                            // Try both React Native WebView and parent window approaches
                            if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                                console.log('[Pangea] 🎬 Sending snow animation via ReactNativeWebView.postMessage');
                                window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'animationUpdate', data }));
                            } else if (window.parent && window.self !== window.top) {
                                console.log('[Pangea] 🎬 Sending snow animation via window.parent.postMessage');
                                window.parent.postMessage({ type: 'animationUpdate', data }, '*');
                            } else {
                                console.warn('[Pangea] 🎬 No suitable postMessage method found for snow animation!');
                            }
                        }
                    }
                    
                    // Set up layer event handlers
                    setupLayerEventHandlers(layer, layerId);
                    
                    map.layers.add(layer);
                    window._pangeaActiveLayers.set(layerId, { source, layer });

                    console.log(`[Pangea] Layer ${layerId} added.`);
                    postResponse(requestId, 'success', { layerId });
                }
            });
        
            if (source.error) {
                source.error.once({
                    handler: (error) => {
                        console.error(`[Pangea] Failed to load source for layer ${layerId}:`, error);
                        postResponse(requestId, 'error', { message: `Failed to load source for ${layerId}` });
                    }
                });
            }
            break;
        }
        case 'addLayerWithData': {
            const { layerId, data } = payload;
            if (window._pangeaActiveLayers.has(layerId)) {
                postResponse(requestId, 'success', { message: `Layer ${layerId} already exists.` });
                return;
            }

            try {
                const source = new pangea.sources.GeoJSONFeatureSource(data, { id: `${layerId}-source` });
                const layer = new pangea.layers.FeatureLayer(source, {
                    id: `${layerId}-layer`,
                    styleFeature: (feature) => {
                        const overlays = [];
                        if (feature.geometry instanceof pangea.geography.GeoPoint) {
                            overlays.push(new pangea.overlays.CircleMarker(
                                8,
                                feature.geometry,
                                {
                                    fill: { color: new pangea.visuals.Color(255, 0, 0), opacity: 0.8 },
                                    stroke: { color: pangea.visuals.Color.WHITE, width: 2 }
                                },
                                { id: `circle-${feature.id}` }
                            ));
                        }
                        return overlays;
                    }
                });
                
                // Set up layer event handlers
                setupLayerEventHandlers(layer, layerId);
                
                map.layers.add(layer);
                window._pangeaActiveLayers.set(layerId, { source, layer });
                console.log(`[Pangea] Layer ${layerId} added with data.`);
                postResponse(requestId, 'success', { layerId });

            } catch(error) {
                console.error(`[Pangea] Failed to create layer ${layerId} with data:`, error);
                postResponse(requestId, 'error', { message: `Failed to create layer ${layerId}: ${error.message}` });
            }
            break;
        }
        case 'removeLayer': {
            const { layerId } = payload;
            if (!window._pangeaActiveLayers.has(layerId)) {
                postResponse(requestId, 'success', { message: `Layer ${layerId} not found.` });
                return;
            }

            const { source, layer } = window._pangeaActiveLayers.get(layerId);
            map.layers.remove(layer);
            source.dispose();
            window._pangeaActiveLayers.delete(layerId);

            // Clean up event handlers
            removeLayerEventHandlers(layerId);

            if (window._pangeaAnimatableLayers.has(layerId)) {
                window._pangeaAnimatableLayers.delete(layerId);
                // Reset listeners flag and disable animation if no more animatable layers
                if (window._pangeaAnimatableLayers.size === 0) {
                    window._pangeaAnimatorListenersAdded = false;
                    window._pangeaAnimationReady = false;
                    
                    console.log('[Pangea] 🎬 Sending animation disabled message...');
                    const data = { active: false };
                    
                    // Try both React Native WebView and parent window approaches
                    if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                        console.log('[Pangea] 🎬 Sending animation disabled via ReactNativeWebView.postMessage');
                        window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'animationUpdate', data }));
                    } else if (window.parent && window.self !== window.top) {
                        console.log('[Pangea] 🎬 Sending animation disabled via window.parent.postMessage');
                        window.parent.postMessage({ type: 'animationUpdate', data }, '*');
                    } else {
                        console.warn('[Pangea] 🎬 No suitable postMessage method found for animation disabled!');
                    }
                    
                    console.log('[Pangea] No more animatable layers. Animation controls disabled.');
                }
            }

            console.log(`[Pangea] Layer ${layerId} removed.`);
            postResponse(requestId, 'success', { layerId });
            break;
        }
        case 'setBasemap': {
            const { styleUrl } = payload;
            if (!styleUrl) {
                throw new Error('No styleUrl provided.');
            }
            console.log(`[Pangea] Setting basemap to ${styleUrl}`);
            map.changeBasemap(styleUrl);
            
            // Send style change notification to React Native
            if (window.parent && window.self !== window.top) {
              window.parent.postMessage({ 
                type: 'mapStyleChanged', 
                styleUrl: styleUrl 
              }, '*');
            }
            
            postResponse(requestId, 'success', { styleUrl });
            break;
        }
        case 'center': {
            const { center, animationOptions } = payload;
            console.log(`[Pangea] Centering map on`, center, 'with options', animationOptions);
            map.center(center, animationOptions);
            postResponse(requestId, 'success', { center });
            break;
        }
        case 'zoom': {
            const { zoomLevel, animationOptions } = payload;
            console.log(`[Pangea] Zooming map to ${zoomLevel}`, 'with options', animationOptions);
            map.zoom(zoomLevel, animationOptions);
            postResponse(requestId, 'success', { zoomLevel });
            break;
        }
        case 'zoomIn': {
            const { animationOptions } = payload;
            console.log(`[Pangea] Zooming in`, 'with options', animationOptions);
            map.zoomIn(animationOptions);
            postResponse(requestId, 'success', {});
            break;
        }
        case 'zoomOut': {
            const { animationOptions } = payload;
            console.log(`[Pangea] Zooming out`, 'with options', animationOptions);
            map.zoomOut(animationOptions);
            postResponse(requestId, 'success', {});
            break;
        }
        case 'rotate': {
            const { bearing, animationOptions } = payload;
            console.log(`[Pangea] Rotating map to ${bearing}`, 'with options', animationOptions);
            map.rotate(bearing, animationOptions);
            postResponse(requestId, 'success', { bearing });
            break;
        }
        case 'tilt': {
            const { pitch, animationOptions } = payload;
            console.log(`[Pangea] Tilting map to ${pitch}`, 'with options', animationOptions);
            map.tilt(pitch, animationOptions);
            postResponse(requestId, 'success', { pitch });
            break;
        }
        case 'move': {
            const { viewOptions, animationOptions } = payload;
            console.log(`[Pangea] Moving map with view`, viewOptions, 'and options', animationOptions);
            map.move(viewOptions, animationOptions);
            postResponse(requestId, 'success', { viewOptions });
            break;
        }
        case 'pan': {
            const { offset, animationOptions } = payload;
            console.log(`[Pangea] Panning map by`, offset, 'with options', animationOptions);
            map.pan(offset, animationOptions);
            postResponse(requestId, 'success', { offset });
            break;
        }
        case 'fit': {
            const { bounds, padding, animationOptions } = payload;
            console.log(`[Pangea] Fitting map to bounds`, bounds, 'with padding', padding, 'and options', animationOptions);
            map.fit(bounds, padding, animationOptions);
            postResponse(requestId, 'success', { bounds });
            break;
        }
        case 'playAnimation': {
            try {
                if (window._pangeaAnimationReady) {
                    console.log('[Pangea] Starting animation...');
                    console.log('[Pangea] Animator state before play:', {
                        length: map.animator.length,
                        currentFrame: map.animator.playhead.frame,
                        isPlaying: map.animator.isPlaying,
                        hasStoryboard: !!map.animator.storyboard,
                        startTime: map.animator.startTime
                    });
                    
                    map.animator.play();
                    console.log('[Pangea] Animation started successfully.');
                    
                    // Start polling for UI updates (fallback)
                    if (!window._pangeaAnimationUpdateInterval) {
                        window._pangeaAnimationUpdateInterval = setInterval(() => {
                            if (window.parent && window.self !== window.top) {
                                const map = window._pangeaMapInstance;
                                const data = {
                                    active: true,
                                    totalFrames: map.animator.length,
                                    currentFrame: map.animator.playhead.frame,
                                    currentTime: map.animator.time.toISOString(),
                                    isPlaying: map.animator.isPlaying
                                };
                                window.parent.postMessage({ type: 'animationUpdate', data }, '*');
                                
                                // Stop polling if animation stops
                                if (!map.animator.isPlaying) {
                                    clearInterval(window._pangeaAnimationUpdateInterval);
                                    window._pangeaAnimationUpdateInterval = null;
                                    console.log('[Pangea] Animation update polling stopped.');
                                }
                            }
                        }, 100);
                        console.log('[Pangea] Started animation update polling.');
                    }
                } else {
                    console.warn('[Pangea] Animation not ready. Add an animatable layer first.');
                }
                
                postResponse(requestId, 'success', {});
            } catch (error) {
                console.error('[Pangea] Error in playAnimation:', error);
                postResponse(requestId, 'error', { message: error.message });
            }
            break;
        }
        case 'stopAnimation': {
            try {
                if (window._pangeaAnimationReady) {
                    map.animator.stop();
                    console.log('[Pangea] Animation stopped.');
                    
                    // Stop polling and send final update
                    if (window._pangeaAnimationUpdateInterval) {
                        clearInterval(window._pangeaAnimationUpdateInterval);
                        window._pangeaAnimationUpdateInterval = null;
                        console.log('[Pangea] Animation update polling stopped.');
                    }
                    
                    // Send final state update
                    if (window.parent && window.self !== window.top) {
                        const data = {
                            active: true,
                            totalFrames: map.animator.length,
                            currentFrame: map.animator.playhead.frame,
                            currentTime: map.animator.time.toISOString(),
                            isPlaying: false
                        };
                        window.parent.postMessage({ type: 'animationUpdate', data }, '*');
                    }
                }
                postResponse(requestId, 'success', {});
            } catch (error) {
                console.error('[Pangea] Error in stopAnimation:', error);
                postResponse(requestId, 'error', { message: error.message });
            }
            break;
        }
        case 'setAnimationFrame': {
            try {
                const { frame } = payload;
                if (window._pangeaAnimationReady && typeof frame === 'number') {
                    map.animator.playhead.move(frame);
                    console.log(`[Pangea] Moved to frame ${frame}.`);
                    
                    // Send immediate update for manual frame changes
                    if (window.parent && window.self !== window.top) {
                        const data = {
                            active: true,
                            totalFrames: map.animator.length,
                            currentFrame: map.animator.playhead.frame,
                            currentTime: map.animator.time.toISOString(),
                            isPlaying: map.animator.isPlaying
                        };
                        window.parent.postMessage({ type: 'animationUpdate', data }, '*');
                    }
                }
                postResponse(requestId, 'success', {});
            } catch (error) {
                console.error('[Pangea] Error in setAnimationFrame:', error);
                postResponse(requestId, 'error', { message: error.message });
            }
            break;
        }

        default:
          console.warn(`[Pangea] Unknown message type received: ${type}`);
          postResponse(requestId, 'error', { message: `Unknown message type: ${type}` });
      }
    } catch (err) {
      console.error(`[Pangea] Failed to handle ${type} message:`, err);
      postResponse(requestId, 'error', { message: err.message });
    }
  });
} 