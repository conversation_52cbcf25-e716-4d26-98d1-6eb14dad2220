# Pangea Bundle

This project packages up Pangea maps for embedding in a React Native mobile application.

## Purpose
This repository provides a buildable and previewable bundle of Pangea map components, making it easy to integrate advanced mapping features into your React Native app.

## Getting Started

To run the project, simply use the preview command. The preview command will automatically build the project before launching the preview.

```sh
npm run preview
```

This will build the bundle and start a local server for previewing the map integration.

## Notes
- Ensure you have all required dependencies installed (`npm install`).
- You may need to provide your own Mapbox token and Sun provisioning key in the source code for full functionality.

---

For more details on embedding or customizing the map, refer to the source code in `src/pangeaMap.js`. 