import React from 'react';
import { Text } from 'react-native';
// Import the icon code mapping (ensure resolveJsonModule is enabled in tsconfig)
import iconCodeToComponentMap from '../components/weather_conditions/iconCodeToComponentMap.json';

const FallbackIcon: React.FC<any> = () => React.createElement(Text, {}, '❓');

/**
 * Dynamically resolves a weather icon component by name.
 * @param {string} name - The component name as a string
 * @returns {React.FC<any> | undefined}
 */
function resolveWeatherIconComponent(name: string): React.FC<any> | undefined {
  const components: Record<string, React.FC<any>> = {
    Condition_breezy_style_color_light: require('../components/weather_conditions/Condition_breezy_style_color_light').default,
    Condition_clear_night_style_color_light: require('../components/weather_conditions/Condition_clear_night_style_color_light').default,
    Condition_cloudy_style_color_light: require('../components/weather_conditions/Condition_cloudy_style_color_light').default,
    Condition_foggy_style_color_light: require('../components/weather_conditions/Condition_foggy_style_color_light').default,
    Condition_mostly_cloudy_style_color_light: require('../components/weather_conditions/Condition_mostly_cloudy_style_color_light').default,
    Condition_partly_cloudy_day_style_color_light: require('../components/weather_conditions/Condition_partly_cloudy_day_style_color_light').default,
    Condition_partly_cloudy_night_style_color_light: require('../components/weather_conditions/Condition_partly_cloudy_night_style_color_light').default,
    Condition_rain_heavy_style_color_light: require('../components/weather_conditions/Condition_rain_heavy_style_color_light').default,
    Condition_rain_style_color_light: require('../components/weather_conditions/Condition_rain_style_color_light').default,
    Condition_snow_style_color_light: require('../components/weather_conditions/Condition_snow_style_color_light').default,
    Condition_sunny_day_style_color_light: require('../components/weather_conditions/Condition_sunny_day_style_color_light').default,
    Condition_thunderstorms_style_color_light: require('../components/weather_conditions/Condition_thunderstorms_style_color_light').default,
    Condition_heavy_snow_style_color_light: require('../components/weather_conditions/Condition_heavy_snow_style_color_light').default,
    // Add more as needed
  };
  return components[name];
}

/**
 * Returns the correct weather icon component for the given icon code and time.
 * Falls back to a question mark if the code is unmapped.
 */
export function getWeatherIconComponent(
  iconCode: number | string,
  isoString?: string,
  dayOrNight?: string
): React.FC<any> | undefined {
  // Always use string for iconCode
  const icon = String(iconCode);
  // Use the dayOrNight property if present, otherwise fallback to hour
  let isNight: boolean;
  if (dayOrNight === 'N') {
    isNight = true;
  } else if (dayOrNight === 'D') {
    isNight = false;
  } else if (isoString) {
    const date = new Date(isoString);
    const hour = date.getHours();
    isNight = hour < 8 || hour >= 20;
  } else {
    isNight = false;
  }
  const iconSet = (iconCodeToComponentMap as any)[icon];
  if (!iconSet) {
    // Fallback: unmapped icon code
    return FallbackIcon;
  }
  const componentName = isNight ? iconSet.night : iconSet.day;
  const IconComponent = resolveWeatherIconComponent(componentName);
  if (!IconComponent) {
    // Fallback: unmapped component
    return FallbackIcon;
  }
  return IconComponent;
}
// NOTE: If you get a TypeScript error on the iconCodeToComponentMap import, enable "resolveJsonModule": true in your tsconfig.json. 