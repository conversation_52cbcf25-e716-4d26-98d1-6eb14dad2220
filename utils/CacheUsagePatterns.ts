import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { CacheHelpers, getCacheManager } from './CacheManager';

/**
 * Cache Usage Patterns - When to use <PERSON>acheManager vs React Query
 * 
 * This file demonstrates the recommended patterns for using our tiered cache
 * alongside react-query for optimal performance and user experience.
 */

// =============================================================================
// PATTERN 1: React Query for UI Components (RECOMMENDED for most cases)
// =============================================================================

/**
 * ✅ USE REACT QUERY for:
 * - UI component data fetching
 * - Automatic background refresh
 * - Loading states and error handling
 * - Optimistic updates
 * - Data that needs frequent updates
 */

// Example hook for weather data fetching
export function useWeatherQuery(location: string): UseQueryResult<any> {
  return useQuery({
    queryKey: ['weather', location],
    queryFn: async () => {
      // This would call your actual weather service
      const response = await fetch(`/api/weather?location=${location}`);
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    enabled: !!location
  });
}

// =============================================================================
// PATTERN 2: CacheManager for Service-to-Service Communication
// =============================================================================

/**
 * ✅ USE CACHEMANAGER for:
 * - Service-to-service data sharing
 * - Cross-session persistence
 * - Background/offline scenarios
 * - Configuration and settings
 * - Data that doesn't need UI reactivity
 */
export class RecommendationService {
  
  async generateRecommendation(location: string): Promise<string> {
    // Check if we have cached weather data first (from previous API calls)
    let weatherData = await CacheHelpers.getCachedServiceData<any>(
      'WeatherService', 
      'fetchWeatherData', 
      { location }
    );

    // If not cached, fetch fresh data
    if (!weatherData) {
      // This would call your actual weather service
      const response = await fetch(`/api/weather?location=${location}`);
      weatherData = await response.json();
      
      // Cache it for other services to use
      await CacheHelpers.cacheServiceData(
        'WeatherService',
        'fetchWeatherData',
        { location },
        weatherData
      );
    }

    // Generate recommendation based on weather
    const recommendation = this.createRecommendation(weatherData);
    
    // Cache the recommendation for 1 hour
    await CacheHelpers.cacheServiceData(
      'RecommendationService',
      'generateRecommendation',
      { location },
      recommendation,
      { ttl: 60 * 60 * 1000 } // 1 hour
    );

    return recommendation;
  }

  private createRecommendation(weather: any): string {
    if (weather.temperature < 32) {
      return "Perfect snow conditions! Great day for skiing!";
    } else if (weather.temperature > 60) {
      return "Warm weather - maybe save skiing for another day.";
    }
    return "Good conditions for winter sports!";
  }
}

// =============================================================================
// PATTERN 3: Hybrid Approach - CacheManager + React Query
// =============================================================================

/**
 * ✅ USE HYBRID APPROACH for:
 * - Initial data loading from cache
 * - Seeding react-query with cached data
 * - Offline-first scenarios
 */
export function useWeatherWithOfflineSupport(location: string): UseQueryResult<any> {
  return useQuery({
    queryKey: ['weather', location],
    queryFn: async () => {
      try {
        // Try to fetch fresh data
        const response = await fetch(`/api/weather?location=${location}`);
        const data = await response.json();
        
        // Cache in our persistent cache for offline use
        await CacheHelpers.cacheServiceData(
          'WeatherService',
          'fetchWeatherData',
          { location },
          data
        );
        
        return data;
      } catch (error) {
        // On network error, try cache as fallback
        const cached = await CacheHelpers.getCachedServiceData<any>(
          'WeatherService',
          'fetchWeatherData', 
          { location }
        );
        
        if (cached) {
          console.log('Using cached data due to network error');
          return cached;
        }
        
        throw error; // Re-throw if no cache available
      }
    },
    staleTime: 5 * 60 * 1000
  });
}

// =============================================================================
// PATTERN 4: User Preferences and Settings
// =============================================================================

/**
 * ✅ USE CACHEMANAGER for user settings that need persistence
 */
export const UserPreferences = {
  async saveTemperatureUnit(unit: 'F' | 'C'): Promise<void> {
    await CacheHelpers.cacheUserData('temperatureUnit', unit);
  },

  async getTemperatureUnit(): Promise<'F' | 'C'> {
    return (await getCacheManager().get<'F' | 'C'>('user:temperatureUnit')) || 'F';
  },

  async saveLastLocation(location: string): Promise<void> {
    await CacheHelpers.cacheUserData('lastLocation', location);
  },

  async getLastLocation(): Promise<string | null> {
    return getCacheManager().get<string>('user:lastLocation');
  }
};

// =============================================================================
// PATTERN 5: Background Data Pre-loading
// =============================================================================

/**
 * ✅ USE CACHEMANAGER for background data preparation
 */
export class BackgroundDataService {
  
  // Pre-load data based on user behavior patterns
  async preloadNearbyWeather(currentLocation: string): Promise<void> {
    const nearbyLocations = this.getNearbyLocations(currentLocation);
    
    // Pre-load weather for nearby locations in background
    const promises = nearbyLocations.map(async (location) => {
      try {
        const response = await fetch(`/api/weather?location=${location}`);
        const data = await response.json();
        
        // Cache with shorter TTL since it's speculative
        await CacheHelpers.cacheServiceData(
          'WeatherService',
          'fetchWeatherData',
          { location },
          data,
          { ttl: 10 * 60 * 1000 } // 10 minutes
        );
      } catch (error) {
        // Ignore errors for background loading
        console.log(`Background load failed for ${location}`);
      }
    });

    await Promise.allSettled(promises);
  }

  private getNearbyLocations(location: string): string[] {
    // Implementation would return nearby coordinates
    // This is just a placeholder
    return [`${location}-north`, `${location}-south`, `${location}-east`, `${location}-west`];
  }
}

// =============================================================================
// PATTERN 6: Cache Management and Cleanup
// =============================================================================

/**
 * ✅ CACHE MAINTENANCE patterns
 */
export const CacheMaintenance = {
  
  // Run periodic cleanup (e.g., on app start)
  async performMaintenance(): Promise<void> {
    const cache = getCacheManager();
    
    // Clean expired entries
    await cache.cleanupExpired();
    
    // Log cache statistics
    const stats = await cache.getStats();
    console.log('Cache stats:', stats);
    
    // Clear cache if it gets too large (memory management)
    if (stats.memoryEntries > 1000) {
      console.log('Cache size limit reached, clearing...');
      await cache.clear();
    }
  },

  // Clear cache for specific scenarios
  async handleUserLogout(): Promise<void> {
    const cache = getCacheManager();
    
    // Keep user preferences but clear sensitive data
    const preferences = await getCacheManager().get('user:temperatureUnit');
    await cache.clear();
    
    if (preferences) {
      await CacheHelpers.cacheUserData('temperatureUnit', preferences);
    }
  }
};

// =============================================================================
// PATTERN 7: Service-to-Service Communication Example
// =============================================================================

/**
 * Example showing how services can share cached data
 */
export class ResortRecommendationService {
  
  async getRecommendationsForLocation(location: string): Promise<any[]> {
    // Get weather data that might already be cached by the WeatherService
    let weatherData = await CacheHelpers.getCachedServiceData<any>(
      'WeatherService',
      'fetchWeatherData',
      { location }
    );

    // Get resort data that might be cached by ResortService
    let resortData = await CacheHelpers.getCachedServiceData<any[]>(
      'ResortService',
      'getResortsNearLocation',
      { location }
    );

    // If we don't have the data, we'd fetch it (not implemented here)
    if (!weatherData || !resortData) {
      // Would implement actual data fetching here
      console.log('Would fetch missing data and cache it');
      return [];
    }

    // Combine data to create recommendations
    const recommendations = resortData.map(resort => ({
      resort,
      weatherScore: this.calculateWeatherScore(weatherData),
      recommendation: this.createRecommendation(resort, weatherData)
    }));

    // Cache the combined recommendations
    await CacheHelpers.cacheServiceData(
      'ResortRecommendationService',
      'getRecommendationsForLocation',
      { location },
      recommendations,
      { ttl: 30 * 60 * 1000 } // 30 minutes
    );

    return recommendations;
  }

  private calculateWeatherScore(weather: any): number {
    // Simple scoring algorithm
    if (weather.temperature < 32 && weather.snowfall > 0) return 10;
    if (weather.temperature < 40) return 7;
    return 3;
  }

  private createRecommendation(resort: any, weather: any): string {
    const score = this.calculateWeatherScore(weather);
    if (score >= 8) return `Excellent conditions at ${resort.name}!`;
    if (score >= 6) return `Good conditions at ${resort.name}`;
    return `Fair conditions at ${resort.name}`;
  }
}

// =============================================================================
// SUMMARY OF USAGE PATTERNS
// =============================================================================

/**
 * QUICK REFERENCE:
 * 
 * 📱 UI Components → React Query
 *    - useQuery, useMutation
 *    - Automatic loading states
 *    - Background refresh
 *    - Optimistic updates
 *    - Real-time UI updates
 * 
 * 🔄 Service Communication → CacheManager  
 *    - Service-to-service data sharing
 *    - Cross-session persistence
 *    - Background processing
 *    - Data that doesn't need UI reactivity
 * 
 * 👤 User Settings → CacheManager
 *    - Preferences that survive app restarts
 *    - User configuration
 *    - Last known states
 *    - Authentication tokens
 * 
 * 📶 Offline Support → Hybrid
 *    - React Query for primary data fetching
 *    - CacheManager as fallback
 *    - Initial data seeding
 *    - Network failure recovery
 * 
 * 🧹 Maintenance → CacheManager
 *    - Periodic cleanup
 *    - Memory management
 *    - Cache statistics
 *    - Storage optimization
 * 
 * 🚀 Performance → Both
 *    - React Query for UI performance
 *    - CacheManager for cross-session speed
 *    - Background pre-loading
 *    - Smart cache invalidation
 */ 