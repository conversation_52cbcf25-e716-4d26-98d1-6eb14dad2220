import { ValidationResult } from '../types/api';

/**
 * Common validation utilities for API services
 */
export class ValidationUtils {
  
  /**
   * Validate latitude/longitude coordinate string
   * Expected format: "lat,lon" (e.g., "37.7749,-122.4194")
   */
  static validateCoordinates(coords: string): ValidationResult {
    const errors: string[] = [];
    
    if (!coords || typeof coords !== 'string') {
      errors.push('Coordinates must be a non-empty string');
      return { isValid: false, errors };
    }
    
    const coordPattern = /^[-\d.]+,[-\d.]+$/;
    if (!coordPattern.test(coords)) {
      errors.push('Coordinates must be in format "lat,lon" (e.g., "37.7749,-122.4194")');
      return { isValid: false, errors };
    }
    
    const [latStr, lonStr] = coords.split(',');
    const lat = parseFloat(latStr);
    const lon = parseFloat(lonStr);
    
    if (isNaN(lat) || lat < -90 || lat > 90) {
      errors.push('Latitude must be a number between -90 and 90');
    }
    
    if (isNaN(lon) || lon < -180 || lon > 180) {
      errors.push('Longitude must be a number between -180 and 180');
    }
    
    return { isValid: errors.length === 0, errors };
  }
  
  /**
   * Validate API key format (basic checks)
   */
  static validateApiKey(apiKey: string | undefined, serviceName?: string): ValidationResult {
    const errors: string[] = [];
    
    if (!apiKey) {
      const keyName = serviceName ? `${serviceName} API key` : 'API key';
      errors.push(`${keyName} is required`);
      return { isValid: false, errors };
    }
    
    if (typeof apiKey !== 'string') {
      errors.push('API key must be a string');
      return { isValid: false, errors };
    }
    
    if (apiKey.length < 10) {
      errors.push('API key appears to be too short');
    }
    
    if (apiKey.includes(' ')) {
      errors.push('API key should not contain spaces');
    }
    
    return { isValid: errors.length === 0, errors };
  }
  
  /**
   * Validate URL format
   */
  static validateUrl(url: string): ValidationResult {
    const errors: string[] = [];
    
    if (!url || typeof url !== 'string') {
      errors.push('URL must be a non-empty string');
      return { isValid: false, errors };
    }
    
    try {
      const urlObj = new URL(url);
      
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        errors.push('URL must use HTTP or HTTPS protocol');
      }
      
      if (!urlObj.hostname) {
        errors.push('URL must have a valid hostname');
      }
      
    } catch (error) {
      errors.push('URL format is invalid');
    }
    
    return { isValid: errors.length === 0, errors };
  }
  
  /**
   * Validate required string parameter
   */
  static validateRequiredString(
    value: any, 
    fieldName: string, 
    minLength: number = 1
  ): ValidationResult {
    const errors: string[] = [];
    
    if (value === undefined || value === null) {
      errors.push(`${fieldName} is required`);
      return { isValid: false, errors };
    }
    
    if (typeof value !== 'string') {
      errors.push(`${fieldName} must be a string`);
      return { isValid: false, errors };
    }
    
    if (value.length < minLength) {
      errors.push(`${fieldName} must be at least ${minLength} character${minLength > 1 ? 's' : ''} long`);
    }
    
    return { isValid: errors.length === 0, errors };
  }
  
  /**
   * Validate numeric parameter within range
   */
  static validateNumber(
    value: any,
    fieldName: string,
    min?: number,
    max?: number
  ): ValidationResult {
    const errors: string[] = [];
    
    if (value === undefined || value === null) {
      errors.push(`${fieldName} is required`);
      return { isValid: false, errors };
    }
    
    const num = Number(value);
    if (isNaN(num)) {
      errors.push(`${fieldName} must be a valid number`);
      return { isValid: false, errors };
    }
    
    if (min !== undefined && num < min) {
      errors.push(`${fieldName} must be at least ${min}`);
    }
    
    if (max !== undefined && num > max) {
      errors.push(`${fieldName} must be at most ${max}`);
    }
    
    return { isValid: errors.length === 0, errors };
  }
  
  /**
   * Validate ski resort ID (numbers or strings that can be converted to numbers)
   */
  static validateSkiResortId(id: any): ValidationResult {
    const errors: string[] = [];
    
    if (id === undefined || id === null) {
      errors.push('Ski resort ID is required');
      return { isValid: false, errors };
    }
    
    const numId = typeof id === 'string' ? parseInt(id, 10) : Number(id);
    if (isNaN(numId) || numId <= 0) {
      errors.push('Ski resort ID must be a positive number');
    }
    
    return { isValid: errors.length === 0, errors };
  }
  
  /**
   * Validate multiple fields at once
   */
  static validateMultiple(validations: Array<() => ValidationResult>): ValidationResult {
    const allErrors: string[] = [];
    
    for (const validation of validations) {
      const result = validation();
      if (!result.isValid) {
        allErrors.push(...result.errors);
      }
    }
    
    return { isValid: allErrors.length === 0, errors: allErrors };
  }
  
  /**
   * Sanitize input string (basic cleanup)
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/\s+/g, ' ') // Normalize whitespace
      .substring(0, 1000); // Limit length to prevent abuse
  }
} 