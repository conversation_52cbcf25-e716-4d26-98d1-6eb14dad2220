import { log } from '../services/loggerService';

/**
 * Precision timer utility for measuring operation performance
 * Uses high-resolution time for accurate measurements
 */
export class PrecisionTimer {
  private static timers: Map<string, number> = new Map();

  /**
   * Start a timer with the given name
   * @param name Unique name for the timer
   */
  public static start(name: string): void {
    const startTime = performance.now();
    this.timers.set(name, startTime);
    log(`[${name}]`, 'debug', `⏱️ Timer started`);
  }

  /**
   * Stop a timer and return the elapsed time in milliseconds
   * @param name Name of the timer to stop
   * @returns Elapsed time in milliseconds, or null if timer not found
   */
  public static stop(name: string): number | null {
    const endTime = performance.now();
    const startTime = this.timers.get(name);
    
    if (startTime === undefined) {
      log(`[${name}]`, 'warn', `⚠️ Timer not found`);
      return null;
    }

    const elapsed = endTime - startTime;
    this.timers.delete(name);
    
    log(`[${name}]`, 'debug', `⏹️ Timer stopped: ${elapsed.toFixed(2)}ms`);
    return elapsed;
  }

  /**
   * Get elapsed time without stopping the timer
   * @param name Name of the timer
   * @returns Elapsed time in milliseconds, or null if timer not found
   */
  public static elapsed(name: string): number | null {
    const currentTime = performance.now();
    const startTime = this.timers.get(name);
    
    if (startTime === undefined) {
      log(`[${name}]`, 'warn', `⚠️ Timer not found`);
      return null;
    }

    return currentTime - startTime;
  }

  /**
   * Check if a timer is currently running
   * @param name Name of the timer
   * @returns True if timer is running, false otherwise
   */
  public static isRunning(name: string): boolean {
    return this.timers.has(name);
  }

  /**
   * Stop all running timers
   */
  public static stopAll(): void {
    for (const name of this.timers.keys()) {
      this.stop(name);
    }
  }

  /**
   * Get all currently running timer names
   * @returns Array of timer names
   */
  public static getRunningTimers(): string[] {
    return Array.from(this.timers.keys());
  }

  /**
   * Clear all timers without stopping them
   * Use with caution - this will lose timing data
   */
  public static clear(): void {
    this.timers.clear();
    log('[PrecisionTimer]', 'debug', '🧹 All timers cleared');
  }

  /**
   * Measure the execution time of a function
   * @param name Name for the measurement
   * @param fn Function to measure
   * @returns Result of the function and elapsed time
   */
  public static async measure<T>(
    name: string, 
    fn: () => Promise<T>
  ): Promise<{ result: T; elapsed: number }> {
    this.start(name);
    try {
      const result = await fn();
      const elapsed = this.stop(name) || 0;
      return { result, elapsed };
    } catch (error) {
      const elapsed = this.stop(name) || 0;
      log(`[${name}]`, 'error', `❌ Error in measured function (${elapsed.toFixed(2)}ms):`, [error]);
      throw error;
    }
  }

  /**
   * Measure the execution time of a synchronous function
   * @param name Name for the measurement
   * @param fn Function to measure
   * @returns Result of the function and elapsed time
   */
  public static measureSync<T>(
    name: string, 
    fn: () => T
  ): { result: T; elapsed: number } {
    this.start(name);
    try {
      const result = fn();
      const elapsed = this.stop(name) || 0;
      return { result, elapsed };
    } catch (error) {
      const elapsed = this.stop(name) || 0;
      log(`[${name}]`, 'error', `❌ Error in measured function (${elapsed.toFixed(2)}ms):`, [error]);
      throw error;
    }
  }
} 