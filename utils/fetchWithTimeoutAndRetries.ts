// Fetch helper with timeout and retry support
// Usage: fetchWithTimeoutAndRetries(url, options, timeoutMs, retries)

/**
 * Fetches a URL with timeout and retry logic.
 * @param url - The URL to fetch
 * @param options - Fetch options (optional)
 * @param timeoutMs - Timeout in milliseconds (default: 5000)
 * @param retries - Number of retries on failure (default: 2)
 * @returns Response object from fetch
 * @throws Error if all retries fail or timeout occurs
 */
export async function fetchWithTimeoutAndRetries(
  url: string,
  options: RequestInit = {},
  timeoutMs: number = 5000,
  retries: number = 2
): Promise<Response> {
  let lastError: any;
  for (let attempt = 0; attempt <= retries; attempt++) {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeoutMs);
    try {
      const response = await fetch(url, { ...options, signal: controller.signal });
      clearTimeout(id);
      return response;
    } catch (error: any) {
      clearTimeout(id);
      lastError = error;
      // Only retry on network errors or abort (timeout)
      if (attempt < retries && (error.name === 'AbortError' || error instanceof TypeError)) {
        // Optionally: add a small delay before retrying
        await new Promise(res => setTimeout(res, 100));
        continue;
      } else {
        break;
      }
    }
  }
  // If we reach here, all retries failed
  throw lastError || new Error('fetchWithTimeoutAndRetries: Unknown error');
}

// TODO: Add exponential backoff or jitter if needed for advanced retry strategies 