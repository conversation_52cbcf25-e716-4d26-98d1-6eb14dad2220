import ConfigService from '../services/appConfigService';
import { log } from '../services/loggerService';
import { ApiErrorHandler } from './ApiErrorHandler';
import { CacheHelpers } from './CacheManager';
import { fetchWithTimeoutAndRetries } from './fetchWithTimeoutAndRetries';

/**
 * Simple base class for API services
 * Provides caching, standardized fetch operations, and URL parameter replacement
 * Uses CacheManager for tiered caching (memory + AsyncStorage)
 */
export abstract class BaseApiService<T = any> {
  
  constructor(
    protected serviceName: string
  ) {}

  /**
   * Replace template parameters in URL string
   * Example: replaceUrlParams("https://api.com/${id}?key=${apiKey}", { id: "123", apiKey: "abc" })
   * Returns: "https://api.com/123?key=abc"
   */
  protected replaceUrlParams(url: string, params: Record<string, string | number>): string {
    let processedUrl = url;
    
    // Debug logging for URL template replacement
    log(`[${this.serviceName}]`, 'debug', `[URL Template] Original URL: ${url}`);
    log(`[${this.serviceName}]`, 'debug', `[URL Template] Parameters:`, [params]);
    
    for (const [key, value] of Object.entries(params)) {
      const placeholder = `\${${key}}`;
      const before = processedUrl;
      processedUrl = processedUrl.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), String(value));
      
      if (before !== processedUrl) {
        log(`[${this.serviceName}]`, 'debug', `[URL Template] Replaced ${placeholder} with ${value}`);
      }
    }
    
    // Log warning if there are still unresolved placeholders
    const unresolvedParams = processedUrl.match(/\$\{[^}]+\}/g);
    if (unresolvedParams?.length) {
      log(`[${this.serviceName}]`, 'warn', `Unresolved URL parameters: ${unresolvedParams.join(', ')} in URL: ${processedUrl}`);
    }
    
    log(`[${this.serviceName}]`, 'debug', `[URL Template] Final URL: ${processedUrl}`);
    return processedUrl;
  }

  /**
   * Standard fetch with URL parameter replacement
   */
  protected async fetchData(
    url: string, 
    urlParams: Record<string, string | number> = {},
    options: RequestInit = {},
    timeout: number = 5000,
    retries: number = 2
  ): Promise<Response> {
    // Replace template parameters in the URL
    const processedUrl = this.replaceUrlParams(url, urlParams);
    
    log(`[${this.serviceName}]`, 'debug', `Fetching from: ${processedUrl}`);
    
    try {
      const response = await fetchWithTimeoutAndRetries(
        processedUrl, 
        options, 
        timeout, 
        retries
      );
      
      if (!response.ok) {
        const apiError = ApiErrorHandler.createApiError(
          { status: response.status, statusText: response.statusText },
          this.serviceName
        );
        ApiErrorHandler.logError(apiError, this.serviceName, 'fetchData');
        throw apiError;
      }
      
      return response;
    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'fetchData');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchData');
      throw apiError;
    }
  }

  /**
   * Cache management using CacheManager
   * For UI components, prefer react-query hooks instead
   * Respects debug.bypassCache config flag for development
   * @param operation - The operation name for cache key
   * @param params - Parameters used for cache key generation
   * @param bypassCache - Optional flag to force bypass cache for this request
   */
  protected async getCachedData(operation: string, params: any = {}, bypassCache: boolean = false): Promise<T | null> {
    // Check if cache bypass is enabled in debug config or passed as parameter
    const debugBypassCache = ConfigService.get('debug.bypassCache', false);
    const shouldBypassCache = debugBypassCache || bypassCache;
    
    if (shouldBypassCache) {
      const reason = debugBypassCache ? 'debug.bypassCache=true' : 'force refresh requested';
      log(`[${this.serviceName}]`, 'debug', `🚫 Cache bypassed (${reason})`, [operation]);
      return null;
    }
    
    return CacheHelpers.getCachedServiceData<T>(this.serviceName, operation, params);
  }

  protected async setCachedData(operation: string, params: any = {}, data: T, ttl: number = 300000): Promise<void> {
    // Check if cache bypass is enabled in debug config
    const bypassCache = ConfigService.get('debug.bypassCache', false);
    if (bypassCache) {
      log(`[${this.serviceName}]`, 'debug', '🚫 Cache write skipped (debug.bypassCache=true)', [operation]);
      return;
    }
    
    await CacheHelpers.cacheServiceData(
      this.serviceName, 
      operation, 
      params, 
      data, 
      { 
        ttl,
        persistToStorage: true // Enable cross-session persistence
      }
    );
  }

  /**
   * Clear cache for this service
   * Note: This only clears the service cache, not react-query cache
   */
  public async clearCache(): Promise<void> {
    // For now, we'll clear all cache - could be more selective in the future
    const cache = await import('./CacheManager').then(m => m.getCacheManager());
    await cache.clear();
    log(`[${this.serviceName}]`, 'info', 'Service cache cleared');
  }
} 