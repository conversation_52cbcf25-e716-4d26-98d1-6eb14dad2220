import { log } from '../services/loggerService';
import { ApiError, ApiErrorType } from '../types/api';

/**
 * Simple error handler for API services
 * Provides basic error transformation and logging
 */
export class ApiErrorHandler {
  /**
   * Transform any error into a standardized ApiError
   */
  static createApiError(
    error: any, 
    serviceName: string, 
    context?: string
  ): ApiError {
    // If it's already an ApiError, return as-is
    if (error instanceof ApiError) {
      return error;
    }

    let errorType = ApiErrorType.UNKNOWN_ERROR;
    let statusCode: number | undefined;
    let message = 'An unknown error occurred';

    // Handle Response errors (HTTP status codes)
    if (error.status || error.statusCode) {
      statusCode = error.status || error.statusCode;
      
      if (statusCode && statusCode >= 400 && statusCode < 500) {
        errorType = statusCode === 404 ? ApiErrorType.NOT_FOUND_ERROR : ApiErrorType.VALIDATION_ERROR;
      } else if (statusCode && statusCode >= 500) {
        errorType = ApiErrorType.SERVER_ERROR;
      }
      
      message = `HTTP ${statusCode}: ${error.statusText || error.message || 'Request failed'}`;
    }
    // Handle network/fetch errors
    else if (error.name === 'AbortError') {
      errorType = ApiErrorType.TIMEOUT_ERROR;
      message = 'Request timed out';
    }
    else if (error.name === 'TypeError' || error.message?.includes('network')) {
      errorType = ApiErrorType.NETWORK_ERROR;
      message = 'Network connection failed';
    }
    // Handle generic errors
    else if (error.message) {
      message = error.message;
    }

    // Add context to message if provided
    if (context) {
      message = `${context}: ${message}`;
    }

    log(`[${serviceName}]`, 'debug', `Error transformed to type: ${errorType}`);

    return new ApiError(errorType, message, statusCode, error);
  }

  /**
   * Log error with appropriate level
   */
  static logError(
    error: ApiError, 
    serviceName: string, 
    operation: string
  ): void {
    const logLevel = error.type === ApiErrorType.RATE_LIMIT_ERROR ? 'warn' : 'error';
    
    log(
      `[${serviceName}]`, 
      logLevel, 
      `${operation} failed: ${error.message}`,
      [error.originalError]
    );
  }
} 