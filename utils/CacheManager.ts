import AsyncStorage from '@react-native-async-storage/async-storage';
import { log } from '../services/loggerService';

/**
 * Cache entry interface
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

/**
 * Cache configuration options
 */
interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  persistToStorage?: boolean; // Whether to persist to AsyncStorage
  memoryOnly?: boolean; // Only use memory cache
}

/**
 * Simple tiered cache manager
 * Memory (fast) + AsyncStorage (persistent)
 * Designed to complement react-query, not replace it
 */
export class CacheManager {
  private static instance: CacheManager;
  private memoryCache: Map<string, CacheEntry<any>> = new Map();
  private readonly STORAGE_PREFIX = 'cache_';
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Get data from cache (checks memory first, then AsyncStorage)
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      // Check memory cache first (fast)
      const memoryEntry = this.memoryCache.get(key);
      if (memoryEntry && !this.isExpired(memoryEntry)) {
        log('[CacheManager]', 'debug', `Memory cache hit: ${key}`);
        return memoryEntry.data as T;
      }

      // Remove expired memory entry
      if (memoryEntry && this.isExpired(memoryEntry)) {
        this.memoryCache.delete(key);
      }

      // Check AsyncStorage (persistent)
      const storageKey = this.getStorageKey(key);
      const storageData = await AsyncStorage.getItem(storageKey);
      
      if (storageData) {
        const entry: CacheEntry<T> = JSON.parse(storageData);
        
        if (!this.isExpired(entry)) {
          log('[CacheManager]', 'debug', `Storage cache hit: ${key}`);
          
          // Promote to memory cache for future fast access
          this.memoryCache.set(key, entry);
          return entry.data;
        } else {
          // Remove expired storage entry
          await AsyncStorage.removeItem(storageKey);
        }
      }

      log('[CacheManager]', 'debug', `Cache miss: ${key}`);
      return null;
    } catch (error) {
      log('[CacheManager]', 'warn', `Cache get error for key ${key}:`, [error]);
      return null;
    }
  }

  /**
   * Set data in cache
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const {
      ttl = this.DEFAULT_TTL,
      persistToStorage = true,
      memoryOnly = false
    } = options;

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      key
    };

    try {
      // Always store in memory for fast access
      this.memoryCache.set(key, entry);
      log('[CacheManager]', 'debug', `Stored in memory cache: ${key}`);

      // Store in AsyncStorage for persistence (unless memoryOnly)
      if (persistToStorage && !memoryOnly) {
        const storageKey = this.getStorageKey(key);
        await AsyncStorage.setItem(storageKey, JSON.stringify(entry));
        log('[CacheManager]', 'debug', `Stored in persistent cache: ${key}`);
      }
    } catch (error) {
      log('[CacheManager]', 'warn', `Cache set error for key ${key}:`, [error]);
    }
  }

  /**
   * Remove item from cache
   */
  async remove(key: string): Promise<void> {
    try {
      // Remove from memory
      this.memoryCache.delete(key);
      
      // Remove from AsyncStorage
      const storageKey = this.getStorageKey(key);
      await AsyncStorage.removeItem(storageKey);
      
      log('[CacheManager]', 'debug', `Removed from cache: ${key}`);
    } catch (error) {
      log('[CacheManager]', 'warn', `Cache remove error for key ${key}:`, [error]);
    }
  }

  /**
   * Clear all cache (memory and storage)
   */
  async clear(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();
      
      // Clear AsyncStorage cache entries
      const allKeys = await AsyncStorage.getAllKeys();
      const cacheKeys = allKeys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      await AsyncStorage.multiRemove(cacheKeys);
      
      log('[CacheManager]', 'info', `Cleared all cache (${cacheKeys.length} persistent entries)`);
    } catch (error) {
      log('[CacheManager]', 'warn', 'Cache clear error:', [error]);
    }
  }

  /**
   * Clear expired entries from both memory and storage
   */
  async cleanupExpired(): Promise<void> {
    try {
      let removedCount = 0;

      // Clean memory cache
      for (const [key, entry] of this.memoryCache.entries()) {
        if (this.isExpired(entry)) {
          this.memoryCache.delete(key);
          removedCount++;
        }
      }

      // Clean AsyncStorage cache
      const allKeys = await AsyncStorage.getAllKeys();
      const cacheKeys = allKeys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      
      for (const storageKey of cacheKeys) {
        try {
          const data = await AsyncStorage.getItem(storageKey);
          if (data) {
            const entry = JSON.parse(data);
            if (this.isExpired(entry)) {
              await AsyncStorage.removeItem(storageKey);
              removedCount++;
            }
          }
        } catch (error) {
          // Remove invalid entries
          await AsyncStorage.removeItem(storageKey);
          removedCount++;
        }
      }

      if (removedCount > 0) {
        log('[CacheManager]', 'info', `Cleaned up ${removedCount} expired cache entries`);
      }
    } catch (error) {
      log('[CacheManager]', 'warn', 'Cache cleanup error:', [error]);
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    memoryEntries: number;
    storageEntries: number;
    memoryKeys: string[];
    storageKeys: string[];
  }> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const storageKeys = allKeys.filter(key => key.startsWith(this.STORAGE_PREFIX));
      
      return {
        memoryEntries: this.memoryCache.size,
        storageEntries: storageKeys.length,
        memoryKeys: Array.from(this.memoryCache.keys()),
        storageKeys: storageKeys.map(key => key.replace(this.STORAGE_PREFIX, ''))
      };
    } catch (error) {
      log('[CacheManager]', 'warn', 'Cache stats error:', [error]);
      return {
        memoryEntries: this.memoryCache.size,
        storageEntries: 0,
        memoryKeys: Array.from(this.memoryCache.keys()),
        storageKeys: []
      };
    }
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Get AsyncStorage key with prefix
   */
  private getStorageKey(key: string): string {
    return `${this.STORAGE_PREFIX}${key}`;
  }
}

/**
 * Convenience function to get the global cache manager
 */
export const getCacheManager = () => CacheManager.getInstance();

/**
 * Helper functions for common cache patterns
 * All helpers respect debug.bypassCache config flag
 */
export const CacheHelpers = {
  /**
   * Cache data with service-specific key pattern
   */
  async cacheServiceData<T>(
    serviceName: string, 
    operation: string, 
    params: any, 
    data: T, 
    options?: CacheOptions
  ): Promise<void> {
    // Check if cache bypass is enabled in debug config
    const ConfigService = await import('../services/appConfigService').then(m => m.default);
    const bypassCache = ConfigService.get('debug.bypassCache', false);
    if (bypassCache) {
      log('[CacheHelpers]', 'debug', '🚫 Cache write skipped (debug.bypassCache=true)', [`${serviceName}:${operation}`]);
      return;
    }
    
    const key = `${serviceName}:${operation}:${JSON.stringify(params)}`;
    await getCacheManager().set(key, data, options);
  },

  /**
   * Get cached service data
   */
  async getCachedServiceData<T>(
    serviceName: string, 
    operation: string, 
    params: any
  ): Promise<T | null> {
    // Check if cache bypass is enabled in debug config
    const ConfigService = await import('../services/appConfigService').then(m => m.default);
    const bypassCache = ConfigService.get('debug.bypassCache', false);
    if (bypassCache) {
      log('[CacheHelpers]', 'debug', '🚫 Cache bypassed (debug.bypassCache=true)', [`${serviceName}:${operation}`]);
      return null;
    }
    
    const key = `${serviceName}:${operation}:${JSON.stringify(params)}`;
    return getCacheManager().get<T>(key);
  },

  /**
   * Cache user preferences/settings (longer TTL, always persistent)
   */
  async cacheUserData<T>(key: string, data: T): Promise<void> {
    await getCacheManager().set(`user:${key}`, data, {
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      persistToStorage: true
    });
  },

  /**
   * Cache temporary/session data (memory only, shorter TTL)
   */
  async cacheSessionData<T>(key: string, data: T): Promise<void> {
    await getCacheManager().set(`session:${key}`, data, {
      ttl: 30 * 60 * 1000, // 30 minutes
      memoryOnly: true
    });
  }
}; 