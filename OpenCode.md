# OpenCode.md - TWC Snow App

## Commands
- Build/Start: `npm start` or `npx expo start`
- Android: `npm run android`
- iOS: `npm run ios`
- Web: `npm run web`
- Lint: `npm run lint` or `expo lint`
- Reset project: `npm run reset-project`

## Code Style
- **TypeScript**: Strict mode enabled
- **Imports**: Use absolute imports with `@/` prefix (e.g., `import { Colors } from '@/constants/Colors'`)
- **Components**: Use functional components with explicit type definitions
- **Props**: Define prop types with interfaces or type aliases (e.g., `export type ThemedTextProps = TextProps & {...}`)
- **Styling**: Use StyleSheet.create for styles
- **Naming**: 
  - Components: PascalCase (e.g., `ThemedText.tsx`)
  - Hooks: camelCase with 'use' prefix (e.g., `useThemeColor.ts`)
  - Files: Component files match component name
- **Platform-specific**: Use .ios.tsx and .android.tsx extensions for platform-specific code