# Configuration Requirements

## Summary
The configuration should be loaded and merged follow a specific order with later config loads overriding earlier ones if properties are the same.  There should be a local config that is packaged with the application.  The remote config should be loaded from an API endpoint.  The remote config should be cached on device until the next time it is downloaded and used as a fallback if the remote config fails to load.  The application should attempt to load the remote config every time it is launched.  If the TTL for the remote config is reached, it should be downloaded again in the background.  

## Local Config
- There should be a separate local file `app-settions.json` that contains the information to load the remote config (this should not be part of the config itself)
- The app setting config should contain the following properties:
  - `remoteConfigUrl`: The URL to download the remote config from.
  - `remoteConfigTTL`: The time to live for the remote config in milliseconds.
  - `remoteConfigRetryDelay`: The delay in milliseconds before retrying to download the remote config if it fails.
- The local config should contins as many fallback values as possible for any other config values that may be needed.

## Debug Config
- There should be a local file `debug-config.json` that overrides any config values for development and testing purposes.
- The debug config is only applied when `__DEV__` is true.
- The debug config takes priority over both local and remote config but can be overridden by runtime debug overrides.

## Remote Config
- The remote config should be a JSON file that is downloaded from an API endpoint.

## Application Config Usage
- The remote config should be downloaded and merged with the local config at application start up.
- The merged config should be stored locally on the device.
- Do not allow loading of the remote config to block the application from starting for more that 1 second.  
- If it takes longer than that, use the last known good config and continue to load the remote config in the background.
- If the remote config fails to load, use the last known good config and retry after the `remoteConfigRetryDelay` has passed from the app settings.
- If the remote config is successfully loaded, cache it on device and use it until the `remoteConfigTTL` has passed from the app settings.
- The config should be available as a singleton instance that can be imported and used anywhere in the application.
- The application should use a Config Component that wraps the application and provides the config to the rest of the application and blocks rendering until either the process of getting and merging the config is complete or the decision is made to use the last known good config.
- The ConfigComponent should also provide a way to reload the config if needed.
- The config should be available as a React context that can be used in functional components.

## Debug Runtime Overrides (DEV Mode Only)
- In development mode, the ConfigService provides methods to override config values at runtime for testing purposes.
- `setDebugOverride(path, value)`: Set a runtime override for a specific config path.
- `clearDebugOverrides()`: Clear all runtime overrides.
- `getDebugOverrides()`: Get current runtime overrides.
- Runtime overrides are cached using AsyncStorage and persist across app restarts in development mode.
- All debug override methods are no-ops in production builds.

## Config loading process
1. Load local config
2. Check for cached remote config
3. If cached remote config exists, merge with local config
4. Attempt to load remote config
5. If remote config load fails, use cached config if available
6. If remote config load succeeds, cache and merge with local config
7. **If in DEV mode, merge debug config**
8. **If in DEV mode and runtime overrides exist, apply them**
9. Provide merged config to application
10. If remote config TTL is reached, load remote config in background

## Config Merge Order (Lowest to Highest Priority)
1. **Local Config** (`local-config.json`) - Base configuration with fallback values
2. **Remote Config** - Downloaded from API, cached locally
3. **Debug Config** (`debug-config.json`) - DEV mode only, for testing scenarios
4. **Runtime Debug Overrides** - DEV mode only, set via ConfigService methods

## Config Sections
- Application - Information about the application, default settings (lanugage, units, etc.)
- API - API endpoints and configuration
- AI - Prompt templates and configuration by application usage
- Text - All User facing text (copy) should come from the text section and be divided by lanugage and named by feature or screen.  The majority of the standard text should be in a local language file and overriden by the config by merging in the config values.
- Debug - Debug-specific settings like enabling debug screens, logging levels, etc.

## Config Defaults System
- The config should support a defaults system under `app.defaults` that provides fallback values for missing config properties
- Defaults can use wildcard matching with "*" to apply to multiple paths (Note: "*" is a valid JSON property name)
- When a config value is requested and not found, the system should check for applicable defaults before falling back to the provided default value
- Default resolution order:
  1. Exact config path (e.g., `api.reverse-geocode.units`)
  2. Wildcard default match (e.g., `app.defaults.api.*` containing `units`)
  3. Provided default value parameter

### Example Defaults Structure:
```json
{
  "app": {
    "defaults": {
      "api": {
        "*": {
          "units": "e",
          "language": "en-US", 
          "format": "json"
        }
      }
    }
  }
}
```

This would make `getConfigValue(config, 'api.reverse-geocode.units', 'imperial')` return `"e"` even if `api.reverse-geocode.units` doesn't exist specifically, because it matches the `api.*` wildcard default.

**Technical Design Notes**
- The config should be loaded and merged using a deep merge function that allows for nested objects and arrays.
- The config should be cached using AsyncStorage.
- The config should be loaded using a service that provides a singleton instance of the config.
- The config should be available as a React context that can be used in functional components.
- The config should be available as a service that can be used in class based components.
- Keep the config object simple and do not create strong types for it.  The usage should be either by pathing to a property with a helper function getConfigValue(config, 'path.to.property', defaultValue).
- Use React Query to manage the remote config loading and caching but do not limit the usage to functional Components as we might need to reference values outside of that.
- Debug functionality should be completely disabled in production builds to prevent any security or performance concerns.