# External Data Loading Pattern

> TL;DR – 1 API ⇒ 1 Service ⇒ React-Query ⇒ UI

This guide shows the **minimal, repeatable** way to consume ANY external API while meeting our requirements for configuration, caching, error handling, and background refresh. Treat it as a checklist – if every bullet is green, you’re done.

---

## 1. Architecture

```
                            API
                             │
                             ▼
┌─────────────┐         ┌─────────────────────────┐         ┌─────────────┐
│ Fetch Helper│◄────────│   Application Service   │────────►│   Config    │
└─────────────┘         │                         │         └─────────────┘
                        │   Change Listeners      │
                        └─────────────────────────┘
                                     │
                        ┌────────────┼────────────┐
                        │            │            │
                        ▼            ▼            ▼
                ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
                │   Context   │ │Service to   │ │ react-query │
                │             │ │  Service    │ │             │
                └─────────────┘ └─────────────┘ │             │
                                                │             │
                                                ▼             │
                                        ┌─────────────┐       │
                                        │UI/Functional│◄──────┘
                                        │ Component   │
                                        └─────────────┘
```

*Exactly one* `MyApiService` class exists per external API.  Aggregator services may combine several services but **never** hit an API directly.

---

## 2. Golden Rules

1. 100 % of settings via `ConfigService`.
2. No mock or fallback data inside services – they **fail gracefully**.
3. Errors bubble up as `ApiError` and are logged via `ApiErrorHandler`.
4. Services do not call each other (aggregators are the exception).
5. Use listener pattern (`subscribe/emit`) if the data can change in the background.
6. Prefer **React-Query** for UI caching; **CacheManager** for cross-session/service sharing.
7. Tests must mock both `ConfigService` *and* `fetchWithTimeoutAndRetries`.
8. **All services and hooks must honor a `bypassCache` option for cache control.** This is required for compliance and must be supported in both the service and the React-Query hook.

---

## 3. Service Skeleton

```