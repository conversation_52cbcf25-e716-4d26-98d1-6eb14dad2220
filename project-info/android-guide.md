# Pangea Android User Guide

## Overview

The Pangea Android SDK provides a comprehensive mapping solution with native Android integration. This guide covers the essential features and usage patterns for integrating Pangea into your Android application. The SDK handles all mapping functionality internally, so you don't need to configure Mapbox directly.

## Setup and Installation

### Requirements

- Android API 21 (Android 5.0) or higher
- Kotlin 1.8.0 or higher
- Android Gradle Plugin 8.0 or higher

### Repository Configuration

Add the Pangea repository to your `build.gradle` (Project level):

```gradle
allprojects {
    repositories {
        google()
        mavenCentral()
        
        // Pangea repository - needs to be early in the list for proper resolution
        maven {
            url "https://repo.artifacts.weather.com/artifactory/pangea-mobile-kotlin-local"
            credentials {
                username = "your-username"
                password = "your-password"
            }
        }
        
        maven { url 'https://www.jitpack.io' }
    }
}
```

> **Note**: Contact your Pangea representative to obtain the appropriate credentials for accessing the Pangea artifact repository.

### Dependencies

Add the Pangea dependency to your `build.gradle` (Module/App level):

```gradle
dependencies {
    // Core library desugaring for Pangea SDK Java 8+ features
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.5")
    
    // Pangea SDK
    implementation("com.weather.pangea.kotlin:pangea-mapbox:$pangeaVersion")
}
```

And define the Pangea version in your app-level `build.gradle`:

```gradle
def pangeaVersion = "5.8.8"
```

Also ensure you have core library desugaring enabled in your `android` block:

```gradle
android {
    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true
        // Sets Java compatibility to Java 8
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
```

### Authentication

The Pangea SDK handles authentication internally. You don't need to provide separate Mapbox access tokens when using Pangea - the SDK manages map tiles and authentication automatically through the Pangea service.

### Basic MapView Setup

#### XML Layout

```xml
<com.weather.pangea.map.MapView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/map_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:basemap="mapbox://styles/mapbox/streets-v11"
    app:latitude="40.7128"
    app:longitude="-74.0060"
    app:zoomLevel="12.0" />
```

#### Activity Integration

```kotlin
import com.weather.pangea.map.MapView
import com.weather.pangea.map.MapViewport
import com.weather.pangea.mapbox.MapboxViewport
import com.weather.pangea.mapbox.MapboxViewportOptions
import com.weather.pangea.geography.GeoPoint

class MainActivity : AppCompatActivity() {
    private lateinit var mapView: com.weather.pangea.map.MapView
    private var mapViewport: com.weather.pangea.map.MapViewport? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        mapView = findViewById(R.id.map_view)
        mapView.onCreate(savedInstanceState)
        lifecycle.addObserver(mapView) // Automatic lifecycle management
        
        setupMap()
    }
    
    private fun setupMap() {
        // Create viewport asynchronously
        val mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
        
        com.weather.pangea.mapbox.MapboxViewport.create(
            mapView = mapView,
            mapboxOptions = mapboxOptions
        ) { viewport ->
            mapViewport = viewport
            configureMap(viewport)
        }
    }
    
    private fun configureMap(viewport: com.weather.pangea.map.MapViewport) {
        // Map is ready - configure as needed
        val geoCenter = com.weather.pangea.geography.GeoPoint(
            longitude = -95.0,
            latitude = 39.0
        )
        viewport.center(geoCenter)
        viewport.zoom(4.0)
    }
}
```

## Map Configuration

### Programmatic Configuration

```kotlin
import com.weather.pangea.map.MapViewportOptions
import com.weather.pangea.geography.GeoPoint
import com.weather.pangea.core.DoubleRange

val options = com.weather.pangea.map.MapViewportOptions()
    .withGeoCenter(com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128)) // longitude, latitude
    .withZoomLevel(12.0)
    .withBearing(0.0)
    .withPitch(0.0)
    .withZoomRange(com.weather.pangea.core.DoubleRange(1.0, 18.0))
    .withBasemap("mapbox://styles/mapbox/streets-v11")

val mapView = com.weather.pangea.map.MapView(context, options)
```

### XML Attributes

```xml
<com.weather.pangea.map.MapView
    android:id="@+id/map_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:basemap="mapbox://styles/mapbox/streets-v11"
    app:latitude="40.7128"
    app:longitude="-74.0060"
    app:zoomLevel="12.0"
    app:bearing="0.0"
    app:pitch="0.0"
    app:minimumZoom="1.0"
    app:maximumZoom="18.0"
    app:maximumBounds="-180,-85,180,85"
    app:loadingTimeout="5000"
    app:locale="en_US"
    app:projection="mercator" />
```

## Camera Controls

### Basic Movements

```kotlin
import com.weather.pangea.geography.GeoPoint
import com.weather.pangea.time.AnimationOptions
import com.weather.pangea.time.TimeSpan

// Move to location
val geoPoint = com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128)
viewport.center(geoPoint)
viewport.zoom(14.0)
viewport.rotate(45.0) // bearing in degrees
viewport.tilt(30.0)   // pitch in degrees

// Animated movements
val animationOptions = com.weather.pangea.time.AnimationOptions(
    duration = com.weather.pangea.time.TimeSpan.seconds(2)
)
viewport.center(geoPoint, animationOptions)

// Complex moves using ViewOptions
val viewOptions = com.weather.pangea.map.ViewOptions()
    .withGeoCenter(com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128))
    .withZoomLevel(14.0)
    .withBearing(45.0)
    .withPitch(30.0)

viewport.move(viewOptions, animationOptions)
```

### Fit to Bounds

```kotlin
import com.weather.pangea.geography.GeoBounds
import com.weather.pangea.core.Padding

// Fit to specific bounds
val bounds = com.weather.pangea.geography.GeoBounds(
    west = -74.1,
    south = 40.6,
    east = -73.9,
    north = 40.8
)

val padding = com.weather.pangea.core.Padding(
    left = 50.0,
    top = 50.0,
    right = 50.0,
    bottom = 50.0
)

val animationOptions = com.weather.pangea.time.AnimationOptions(
    duration = com.weather.pangea.time.TimeSpan.seconds(1)
)

viewport.fit(bounds, padding, animationOptions)
```

### Coordinate Conversion

```kotlin
import com.weather.pangea.core.Point

// Convert between geographic and screen coordinates
val geoPoint = com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128)
val screenPoint = viewport.convertToScreen(geoPoint)

val backToGeo = viewport.convertToGeo(com.weather.pangea.core.Point(100.0, 200.0))

// Get current map state
val currentCenter = viewport.geoCenter
val currentZoom = viewport.zoomLevel
val currentBounds = viewport.bounds
```

## User Interactions

### Configure Behaviors

```kotlin
// Enable/disable user interactions
viewport.behaviors.dragging = true
viewport.behaviors.zooming = true
viewport.behaviors.rotating = true
viewport.behaviors.tilting = true

// Disable all interactions
viewport.behaviors.disable()

// Enable specific interactions
viewport.behaviors.enable(
    dragging = true,
    zooming = true,
    rotating = false,
    tilting = false
)
```

### Touch Events

```kotlin
import com.weather.pangea.core.Subscription

class MapActivity : AppCompatActivity() {
    private val subscriptions = mutableListOf<com.weather.pangea.core.Subscription>()
    
    private fun setupTouchEvents() {
        // Tap events
        val tapSubscription = viewport.touch.tap.subscribe { touch ->
            val geoPoint = touch.geoPoint
            val screenPoint = touch.screenPoint
            println("Tap at: ${geoPoint.latitude}, ${geoPoint.longitude}")
            println("Screen coordinates: ${screenPoint.x}, ${screenPoint.y}")
        }
        subscriptions.add(tapSubscription)
        
        // Long press events
        val longPressSubscription = viewport.touch.longPress.subscribe { touch ->
            val geoPoint = touch.geoPoint
            println("Long press at: ${geoPoint.latitude}, ${geoPoint.longitude}")
        }
        subscriptions.add(longPressSubscription)
        
        // Touch down/move/up events
        val downSubscription = viewport.touch.down.subscribe { touch ->
            println("Touch down: ${touch.touchId}")
        }
        subscriptions.add(downSubscription)
        
        val moveSubscription = viewport.touch.move.subscribe { touch ->
            println("Touch move: ${touch.touchId}")
        }
        subscriptions.add(moveSubscription)
        
        val upSubscription = viewport.touch.up.subscribe { touch ->
            println("Touch up: ${touch.touchId}")
        }
        subscriptions.add(upSubscription)
    }
    
    override fun onDestroy() {
        subscriptions.forEach { it.dispose() }
        super.onDestroy()
    }
}
```

## Camera Events

### Movement Events

```kotlin
private fun setupCameraEvents() {
    // General camera movement
    val movedSubscription = viewport.moved.subscribe { region ->
        println("Camera moved to: ${region.geoCenter}")
    }
    subscriptions.add(movedSubscription)
    
    // Specific movement types
    val zoomStartedSubscription = viewport.zoomStarted.subscribe { 
        println("Zoom started") 
    }
    subscriptions.add(zoomStartedSubscription)
    
    val zoomedSubscription = viewport.zoomed.subscribe { region -> 
        println("Zoom level: ${region.zoomLevel}") 
    }
    subscriptions.add(zoomedSubscription)
    
    val zoomEndedSubscription = viewport.zoomEnded.subscribe { 
        println("Zoom ended") 
    }
    subscriptions.add(zoomEndedSubscription)
    
    val rotatedSubscription = viewport.rotated.subscribe { region -> 
        println("Bearing: ${region.bearing}") 
    }
    subscriptions.add(rotatedSubscription)
    
    val tiltedSubscription = viewport.tilted.subscribe { region -> 
        println("Pitch: ${region.pitch}") 
    }
    subscriptions.add(tiltedSubscription)
}
```

### Loading Events

```kotlin
private fun setupLoadingEvents() {
    // Map loading state
    val loadingSubscription = viewport.loading.subscribe { 
        println("Map started loading") 
    }
    subscriptions.add(loadingSubscription)
    
    val loadedSubscription = viewport.loaded.subscribe { 
        println("Map finished loading") 
    }
    subscriptions.add(loadedSubscription)
    
    // Basemap changes
    val basemapChangingSubscription = viewport.basemapChanging.subscribe { 
        println("Basemap changing") 
    }
    subscriptions.add(basemapChangingSubscription)
    
    val basemapChangedSubscription = viewport.basemapChanged.subscribe { 
        println("Basemap changed") 
    }
    subscriptions.add(basemapChangedSubscription)
}
```

## Layer Management

### Adding Layers

```kotlin
import com.weather.pangea.layer.feature.FeatureLayer
import com.weather.pangea.layer.raster.RasterLayer
import com.weather.pangea.layer.LayerSlot
import com.weather.pangea.source.feature.StaticFeatureSource
import com.weather.pangea.source.raster.SunRasterSource
import com.weather.pangea.geography.Feature
import com.weather.pangea.visual.Color

class LayerManager(private val viewport: com.weather.pangea.map.MapViewport) {
    
    fun addFeatureLayer() {
        // Create feature source
        val features = createSampleFeatures()
        val featureSource = com.weather.pangea.source.feature.StaticFeatureSource(features)
        
        // Create feature styler
        val featureStyler = com.weather.pangea.layer.feature.FeatureStyler()
        
        // Create layer
        val featureLayer = com.weather.pangea.layer.feature.FeatureLayer(
            id = "my-features",
            source = featureSource,
            styler = featureStyler
        )
        
        // Add source and layer to viewport
        viewport.sources["feature-source"] = featureSource
        viewport.layers += featureLayer
    }
    
    fun addRasterLayer() {
        // Create raster source
        val rasterSource = com.weather.pangea.source.raster.SunRasterSource(
            id = "satellite",
            baseUrl = "https://example.com/tiles/{z}/{x}/{y}.png",
            tileSize = 256
        )
        
        // Create raster layer
        val rasterLayer = com.weather.pangea.layer.raster.RasterLayer(
            id = "satellite",
            source = rasterSource
        )
        
        // Control rendering order with slots
        rasterLayer.slot = com.weather.pangea.layer.LayerSlot.MIDDLE
        
        // Add to viewport
        viewport.sources["raster-source"] = rasterSource
        viewport.layers += rasterLayer
    }
    
    fun addBackgroundLayer() {
        // Create background layer
        val backgroundLayer = com.weather.pangea.layer.BackgroundLayer(
            id = "background",
            color = com.weather.pangea.visual.Color.BLUE
        )
        
        backgroundLayer.slot = com.weather.pangea.layer.LayerSlot.BOTTOM
        viewport.layers += backgroundLayer
    }
    
    private fun createSampleFeatures(): List<com.weather.pangea.geography.Feature> {
        val point1 = com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128)
        val point2 = com.weather.pangea.geography.GeoPoint(-122.4194, 37.7749)
        
        val feature1 = com.weather.pangea.geography.Feature(
            id = "1",
            geometry = point1,
            properties = mapOf("name" to "New York City")
        )
        
        val feature2 = com.weather.pangea.geography.Feature(
            id = "2",
            geometry = point2,
            properties = mapOf("name" to "San Francisco")
        )
        
        return listOf(feature1, feature2)
    }
}
```

### Layer Properties

```kotlin
import com.weather.pangea.time.TimeRange
import com.weather.pangea.time.Instant
import com.weather.pangea.time.TimeSpan
import com.weather.pangea.core.DoubleRange

// Visibility and opacity
layer.opacity = 0.8f
layer.show()  // or layer.hide()

// Time range for animation
val startTime = com.weather.pangea.time.Instant.now()
val endTime = startTime.plus(com.weather.pangea.time.TimeSpan.hours(1))
layer.timeRange = com.weather.pangea.time.TimeRange(startTime, endTime)

// Zoom range
layer.zoomRange = com.weather.pangea.core.DoubleRange(5.0, 15.0)

// Layer events
val loadedSubscription = layer.loaded.subscribe { 
    println("Layer loaded") 
}
val loadingSubscription = layer.loading.subscribe { 
    println("Layer loading") 
}
```

### Data Sources

```kotlin
import com.weather.pangea.source.raster.RasterSource
import com.weather.pangea.source.feature.HttpGeoJsonFeatureSource
import com.weather.pangea.source.grid.SunGridSource

// HTTP GeoJSON Feature Source
val featureSource = com.weather.pangea.source.feature.HttpGeoJsonFeatureSource(
    id = "geojson-data",
    url = "https://example.com/data.geojson"
)

// Sun Grid Source (Weather data)
val gridSource = com.weather.pangea.source.grid.SunGridSource(
    id = "weather-grid",
    baseUrl = "https://api.weather.com/v1/grid",
    parameters = mapOf("apikey" to "your-api-key")
)

// Add to viewport
viewport.sources["feature-data"] = featureSource
viewport.sources["grid-data"] = gridSource
```

## Advanced Features

### Map Capture

```kotlin
import android.content.ContentValues
import android.graphics.Bitmap
import android.provider.MediaStore
import kotlinx.coroutines.launch

// Capture map as bitmap
viewport.capture { bitmap ->
    bitmap?.let {
        // Save to gallery
        saveToGallery(it)
        
        // Or share via intent
        shareImage(it)
    }
}

// Async capture with coroutines
lifecycleScope.launch {
    val bitmap = viewport.awaitCapture()
    bitmap?.let {
        // Handle captured bitmap
        saveToGallery(it)
    }
}

private fun saveToGallery(bitmap: Bitmap) {
    val values = ContentValues().apply {
        put(MediaStore.Images.Media.DISPLAY_NAME, "map_capture_${System.currentTimeMillis()}.jpg")
        put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
        put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/Maps")
    }
    
    val uri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)
    uri?.let { imageUri ->
        contentResolver.openOutputStream(imageUri)?.use { outputStream ->
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
        }
    }
}
```

### Basemap Management

```kotlin
// Change basemap
viewport.changeBasemap("mapbox://styles/mapbox/satellite-v9")

// With callback
viewport.changeBasemap("mapbox://styles/mapbox/dark-v10") {
    println("Basemap changed")
}

// Async basemap change
lifecycleScope.launch {
    viewport.awaitChangeBasemap("mapbox://styles/mapbox/light-v10")
    println("Basemap changed")
}
```

### Coordinate System

```kotlin
import com.weather.pangea.geography.MapProjection
import com.weather.pangea.geography.WorldView

// Set projection
viewport.projection = com.weather.pangea.geography.MapProjection.MERCATOR
viewport.projection = com.weather.pangea.geography.MapProjection.GLOBE

// World view settings
viewport.changeWorldView(com.weather.pangea.geography.WorldView.UNITED_STATES)
viewport.changeWorldView(com.weather.pangea.geography.WorldView.CHINA)
```

### Overlay Management

```kotlin
import com.weather.pangea.overlay.ImageMarker
import com.weather.pangea.overlay.TextMarker
import com.weather.pangea.overlay.LinePath
import com.weather.pangea.overlay.PolygonPath
import com.weather.pangea.visual.TextStyle
import com.weather.pangea.visual.LineStyle
import com.weather.pangea.visual.FillStyle

// Add image marker
val geoPoint = com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128)
val imageMarker = com.weather.pangea.overlay.ImageMarker(
    id = "marker1",
    geoPoint = geoPoint,
    bitmap = BitmapFactory.decodeResource(resources, R.drawable.marker_icon)
)
viewport.overlays += imageMarker

// Add text marker
val textMarker = com.weather.pangea.overlay.TextMarker(
    id = "text1",
    geoPoint = geoPoint,
    text = "New York City",
    style = com.weather.pangea.visual.TextStyle(
        font = resources.getFont(R.font.roboto_regular),
        color = com.weather.pangea.visual.Color.BLACK,
        size = 16f
    )
)
viewport.overlays += textMarker

// Add line path
val points = listOf(
    com.weather.pangea.geography.GeoPoint(-74.0060, 40.7128),
    com.weather.pangea.geography.GeoPoint(-122.4194, 37.7749)
)
val linePath = com.weather.pangea.overlay.LinePath(
    id = "line1",
    points = points,
    style = com.weather.pangea.visual.LineStyle(
        color = com.weather.pangea.visual.Color.BLUE,
        width = 3.0f
    )
)
viewport.overlays += linePath

// Add polygon path
val polygonPoints = listOf(
    com.weather.pangea.geography.GeoPoint(-74.0, 40.7),
    com.weather.pangea.geography.GeoPoint(-74.1, 40.7),
    com.weather.pangea.geography.GeoPoint(-74.1, 40.8),
    com.weather.pangea.geography.GeoPoint(-74.0, 40.8)
)
val polygonPath = com.weather.pangea.overlay.PolygonPath(
    id = "polygon1",
    points = polygonPoints,
    style = com.weather.pangea.visual.FillStyle(
        color = com.weather.pangea.visual.Color.RED,
        alpha = 0.5f
    )
)
viewport.overlays += polygonPath
```

## Lifecycle Management

### Automatic Lifecycle

```kotlin
import androidx.lifecycle.lifecycleScope

class MapActivity : AppCompatActivity() {
    private lateinit var mapView: com.weather.pangea.map.MapView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        mapView = findViewById(R.id.map_view)
        mapView.onCreate(savedInstanceState)
        lifecycle.addObserver(mapView) // Automatic lifecycle management
    }
}
```

### Manual Lifecycle

```kotlin
override fun onStart() {
    super.onStart()
    mapView.onStart()
}

override fun onResume() {
    super.onResume()
    mapView.onResume()
}

override fun onPause() {
    super.onPause()
    mapView.onPause()
}

override fun onStop() {
    super.onStop()
    mapView.onStop()
}

override fun onDestroy() {
    mapView.onDestroy()
    super.onDestroy()
}
```

### Subscription Management

```kotlin
class MapActivity : AppCompatActivity() {
    private val subscriptions = mutableListOf<com.weather.pangea.core.Subscription>()
    
    private fun setupSubscriptions() {
        // Store all subscriptions for cleanup
        subscriptions.add(
            viewport.moved.subscribe { region ->
                // Handle movement
            }
        )
        
        subscriptions.add(
            viewport.touch.tap.subscribe { touch ->
                // Handle tap
            }
        )
    }
    
    override fun onDestroy() {
        // Clean up all subscriptions
        subscriptions.forEach { it.dispose() }
        subscriptions.clear()
        
        mapView.onDestroy()
        super.onDestroy()
    }
}
```

## Best Practices

1. **Always initialize MapView**: Call `onCreate()` before using the MapView
2. **Use lifecycle observer**: Register with lifecycle owner for automatic management
3. **Handle async creation**: MapViewport creation is asynchronous
4. **Dispose subscriptions**: Clean up event subscriptions in `onDestroy()`
5. **Set reasonable limits**: Configure appropriate zoom and pitch ranges
6. **Layer ordering**: Use layer slots to control rendering order
7. **Namespace awareness**: Always use full namespaces for better code clarity
8. **Memory management**: Store and dispose of subscriptions properly

## Common Issues and Solutions

### Viewport Creation Errors

```kotlin
private fun createViewportSafely() {
    try {
        val mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
        
        com.weather.pangea.mapbox.MapboxViewport.create(
            mapView = mapView,
            mapboxOptions = mapboxOptions
        ) { viewport ->
            this.mapViewport = viewport
            setupMap(viewport)
        }
    } catch (e: Exception) {
        Log.e("MapView", "Failed to create viewport", e)
        // Handle error - maybe show user message
    }
}
```

### Loading Timeout Handling

```kotlin
private fun setupLoadingHandling() {
    val loadingSubscription = viewport.loading.subscribe {
        // Show loading indicator
        showLoadingIndicator()
    }
    
    val loadedSubscription = viewport.loaded.subscribe {
        // Hide loading indicator
        hideLoadingIndicator()
    }
    
    // Handle loading timeout
    Handler(Looper.getMainLooper()).postDelayed({
        if (viewport.isLoading) {
            Log.w("MapView", "Map loading timeout")
            // Handle timeout - maybe retry or show error
        }
    }, 10000) // 10 second timeout
}
```

## Fragment Integration

```kotlin
import androidx.fragment.app.Fragment

class MapFragment : Fragment() {
    private lateinit var mapView: com.weather.pangea.map.MapView
    private var mapViewport: com.weather.pangea.map.MapViewport? = null
    private val subscriptions = mutableListOf<com.weather.pangea.core.Subscription>()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_map, container, false)
        
        mapView = view.findViewById(R.id.map_view)
        mapView.onCreate(savedInstanceState)
        
        setupMap()
        return view
    }
    
    private fun setupMap() {
        val mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
        
        com.weather.pangea.mapbox.MapboxViewport.create(
            mapView = mapView,
            mapboxOptions = mapboxOptions
        ) { viewport ->
            mapViewport = viewport
            setupEvents()
        }
    }
    
    private fun setupEvents() {
        mapViewport?.let { viewport ->
            subscriptions.add(
                viewport.touch.tap.subscribe { touch ->
                    // Handle tap
                }
            )
        }
    }
    
    override fun onStart() {
        super.onStart()
        mapView.onStart()
    }
    
    override fun onResume() {
        super.onResume()
        mapView.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        mapView.onPause()
    }
    
    override fun onStop() {
        super.onStop()
        mapView.onStop()
    }
    
    override fun onDestroy() {
        subscriptions.forEach { it.dispose() }
        subscriptions.clear()
        mapView.onDestroy()
        super.onDestroy()
    }
}
```

## Quick Start Example

Here's a complete minimal example to get you started:

```kotlin
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.weather.pangea.map.MapView
import com.weather.pangea.map.MapViewport
import com.weather.pangea.mapbox.MapboxViewport
import com.weather.pangea.mapbox.MapboxViewportOptions
import com.weather.pangea.geography.GeoPoint
import com.weather.pangea.core.Subscription

class MainActivity : AppCompatActivity() {
    private lateinit var mapView: com.weather.pangea.map.MapView
    private var viewport: com.weather.pangea.map.MapViewport? = null
    private val subscriptions = mutableListOf<com.weather.pangea.core.Subscription>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        setupMap()
    }

    private fun setupMap() {
        mapView = findViewById(R.id.map_view)
        mapView.onCreate(savedInstanceState)
        lifecycle.addObserver(mapView)

        // Configure initial map properties
        mapView.apply {
            latitude = 37.7749
            longitude = -122.4194
            zoomLevel = 10.0
            basemap = "mapbox://styles/mapbox/streets-v11"
        }

        // Create viewport
        val mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
        
        com.weather.pangea.mapbox.MapboxViewport.create(
            mapView = mapView,
            mapboxOptions = mapboxOptions
        ) { viewport ->
            this.viewport = viewport
            setupEvents()
            println("Map is ready!")
        }
    }
    
    private fun setupEvents() {
        viewport?.let { vp ->
            // Set up touch event handling
            subscriptions.add(
                vp.touch.tap.subscribe { touch ->
                    val geoPoint = touch.geoPoint
                    println("Tap at: ${geoPoint.latitude}, ${geoPoint.longitude}")
                }
            )
            
            // Set up camera events
            subscriptions.add(
                vp.moved.subscribe { region ->
                    println("Camera moved to: ${region.geoCenter}")
                }
            )
        }
    }

    override fun onDestroy() {
        subscriptions.forEach { it.dispose() }
        subscriptions.clear()
        mapView.onDestroy()
        super.onDestroy()
    }
}
```

This comprehensive guide provides you with everything needed to get started with the Pangea Android SDK. The setup is straightforward - just add the Pangea repository and dependency, and you're ready to use the full mapping functionality without any additional Mapbox configuration. All examples include full namespace references to make implementation easier and less error-prone.