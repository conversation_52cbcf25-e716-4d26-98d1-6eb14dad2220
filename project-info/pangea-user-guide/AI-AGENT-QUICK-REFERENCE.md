# Pangea Web SDK - AI Agent Quick Reference

This is a concise reference guide specifically designed for AI agents working with the Pangea Web SDK. It covers essential patterns, common tasks, and critical implementation details.

## Core Architecture

```typescript
// Import pattern
import pangea from '@twc/pangea-web/mapbox';

// Core components hierarchy
pangea = {
  mapbox: { MapboxViewport },     // Map engines
  layers: { LayerSlot, Layer types },
  sources: { Source types },
  overlays: { Overlay types },
  geography: { GeoPoint, GeoBounds, etc },
  visuals: { Color, Anchor, etc },
  time: { Animator },
  data: { Feature types }
}
```

## Essential Patterns

### 1. Map Creation (Always Required)
```typescript
const map = await pangea.mapbox.MapboxViewport.create(
  'container-id',
  'mapbox-access-token',
  {
    geoCenter: [longitude, latitude],
    zoomLevel: 10,
    basemap: 'mapbox://styles/mapbox/standard'
  }
);
```

### 2. Layer Creation Pattern
```typescript
// 1. Create Source
const source = new pangea.sources.GeoJSONFeatureSource(data, { id: 'source-id' });

// 2. Create Layer
const layer = new pangea.layers.FeatureLayer(source, {
  id: 'layer-id',
  slot: pangea.layers.LayerSlot.MIDDLE,
  styleFeature: (feature) => [/* return overlays */]
});

// 3. Add to Map
map.layers.add(layer);
```

### 3. Error Handling Pattern
```typescript
try {
  const result = await operation();
} catch (error) {
  if (error instanceof pangea.ArgumentRangeError) {
    // Handle range errors
  } else if (error.message.includes('network')) {
    // Handle network errors
  }
  // Always log and potentially retry
}
```

## Layer Types & Use Cases

| Layer Type | Use Case | Source Type |
|------------|----------|-------------|
| `RasterLayer` | Tile imagery, satellite | `HttpRasterSource` |
| `FeatureLayer` | Data visualization | `GeoJSONFeatureSource` |
| `OverlayLayer` | Manual graphics | `StaticOverlaySource` |
| `HeatmapLayer` | Density visualization | `StaticFeatureSource` |

## Source Types & Data

| Source Type | Data Format | Use Case |
|-------------|-------------|----------|
| `GeoJSONFeatureSource` | GeoJSON | Vector data |
| `HttpRasterSource` | Tile URLs | Raster imagery |
| `StaticOverlaySource` | Overlay objects | Manual graphics |
| `StaticFeatureSource` | Feature objects | Static data |

## Overlay Types & Graphics

| Overlay Type | Purpose | Key Properties |
|--------------|---------|----------------|
| `CircleMarker` | Points | radius, fill, stroke |
| `TextMarker` | Labels | text, color, size, halo |
| `ImageMarker` | Icons | url, size, anchor |
| `LinePath` | Lines | stroke (color, width) |
| `PolygonPath` | Areas | fill, stroke |

## Critical Implementation Rules

### Layer Slots (Rendering Order)
```typescript
pangea.layers.LayerSlot.BOTTOM  // Base imagery
pangea.layers.LayerSlot.MIDDLE  // Data layers
pangea.layers.LayerSlot.TOP     // Overlays, labels
```

### Geographic Coordinates
```typescript
// Always [longitude, latitude] - NOT [lat, lng]
const point = [-74.006, 40.7128]; // NYC
const geoPoint = new pangea.geography.GeoPoint(-74.006, 40.7128);
```

### Event Handling
```typescript
// Map events
map.loaded.add(() => {});
map.moveEnded.add(() => {});
map.zoomEnded.add(() => {});

// Layer events
layer.loaded.add(() => {});
layer.changed.add((changes) => {});

// Mouse events
map.mouse.click.add((event) => {
  const geoPoint = event.geoPoint;
  const screenPoint = event.screenPoint;
});
```

## Common Tasks

### Add GeoJSON Data
```typescript
const data = { type: 'FeatureCollection', features: [...] };
const source = new pangea.sources.GeoJSONFeatureSource(data, { id: 'data-source' });
const layer = new pangea.layers.FeatureLayer(source, {
  id: 'data-layer',
  slot: pangea.layers.LayerSlot.MIDDLE,
  styleFeature: (feature) => [
    new pangea.overlays.CircleMarker(8, feature.geometry, {
      fill: { color: '#ff4444', opacity: 0.8 },
      stroke: { color: '#ffffff', width: 2 }
    })
  ]
});
map.layers.add(layer);
```

### Add Manual Graphics
```typescript
const overlays = [
  new pangea.overlays.CircleMarker(10, [-74.006, 40.7128], {
    fill: { color: '#ff0000', opacity: 0.7 }
  }),
  new pangea.overlays.TextMarker('NYC', [-74.006, 40.7128], {
    color: pangea.visuals.Color.BLACK,
    size: 14
  })
];
const source = new pangea.sources.StaticOverlaySource(overlays, { id: 'graphics' });
const layer = new pangea.layers.OverlayLayer(source, { id: 'graphics-layer' });
map.layers.add(layer);
```

### Handle Interactions
```typescript
map.mouse.click.add((event) => {
  // Check layers for interactions
  for (const layer of map.layers.items) {
    if (layer.inspect) {
      const results = layer.inspect(event.screenPoint);
      if (results?.length > 0) {
        // Handle interaction
        const overlay = results[0];
        console.log('Clicked:', overlay.id);
      }
    }
  }
});
```

### Control Map View
```typescript
// Move map
map.center([-122.4194, 37.7749]); // San Francisco
map.zoom(12);
map.move({
  geoCenter: [2.3522, 48.8566], // Paris
  zoomLevel: 10,
  bearing: 45,
  pitch: 30
}, { duration: 2000 });

// Fit to bounds [west, south, east, north]
map.fit([-74.047285, 40.683921, -73.926072, 40.879038]);
```

### Animations
```typescript
// Animation setup with weather data
const source = new pangea.sources.SunRasterSource("satradFcst", sunApiToken);
source.ready.once({ handler: () => {
  map.animator.startTime = source.currentSequence.times[0].instant;
  map.animator.storyboard = [{ frames: 20, span: 20 * 900000 }];
  source.animator = map.animator;
  
  const layer = new pangea.layers.RasterLayer(source);
  map.layers.add(layer);
  
  map.animator.play(); // Start animation
}});
```

## Event Monitoring System

The Pangea SDK provides comprehensive event handling for all map interactions, layer operations, and animations. A complete event monitoring system is available for debugging and application integration.

### Event Categories

#### Map Events
```typescript
// Map lifecycle events
map.loaded.add({
  handler: () => console.log('✨ Map is fully loaded and interactive!')
});

map.styleLoaded.add({
  handler: () => console.log('🎨 Map style loaded successfully')
});

// Map movement events
map.moveStarted.add({
  handler: () => console.log('🚀 Map movement started')
});

map.moveEnded.add({
  handler: () => {
    const center = map.geoCenter;
    console.log('🛑 Map movement ended at:', {
      latitude: center.latitude.toFixed(4),
      longitude: center.longitude.toFixed(4),
      zoom: map.zoomLevel.toFixed(2),
      bearing: map.bearing.toFixed(1) + '°',
      pitch: map.pitch.toFixed(1) + '°'
    });
  }
});

// Zoom events
map.zoomStarted.add({
  handler: () => console.log('🔍 Zoom started from level:', map.zoomLevel.toFixed(2))
});

map.zoomEnded.add({
  handler: () => console.log('🎯 Zoom ended at level:', map.zoomLevel.toFixed(2))
});

// Rotation events
map.rotateStarted.add({
  handler: () => console.log('🔄 Map rotation started from:', map.bearing.toFixed(1) + '°')
});

map.rotateEnded.add({
  handler: () => console.log('🧭 Rotation ended at:', map.bearing.toFixed(1) + '°')
});

// Tilt events
map.tiltStarted.add({
  handler: () => console.log('📐 Map tilt started from:', map.pitch.toFixed(1) + '°')
});

map.tiltEnded.add({
  handler: () => console.log('📐 Tilt ended at:', map.pitch.toFixed(1) + '°')
});
```

#### Touch Events (Mobile Optimized)
**Note**: Mouse events are excluded for mobile app compatibility. Touch gestures automatically trigger map movement events.

```typescript
// Touch interactions are handled through map movement events
// No explicit touch event handlers needed - map responds to:
// - Pan gestures → moveStarted/moveEnded
// - Pinch gestures → zoomStarted/zoomEnded  
// - Rotation gestures → rotateStarted/rotateEnded
// - Tilt gestures → tiltStarted/tiltEnded

// For layer interactions, use movement end events
map.moveEnded.add({
  handler: () => {
    // Check if user tapped/clicked on features
    // Implementation depends on specific use case
    console.log('🗺️ Map interaction completed');
  }
});
```

#### Layer Events
```typescript
// Layer lifecycle events
layer.loaded.add({
  handler: () => {
    console.log(`✨ Layer '${layerId}' loaded successfully`);
  }
});

layer.changed.add({
  handler: (changes) => {
    console.log(`🔄 Layer '${layerId}' changed:`, changes);
    
    // IMPORTANT: For postMessage/mobile integration, sanitize complex objects
    if (window.parent && window.self !== window.top) {
      const sanitizedChanges = {};
      if (changes && typeof changes === 'object') {
        Object.keys(changes).forEach(key => {
          const value = changes[key];
          if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
            sanitizedChanges[key] = value;
          } else if (value && typeof value.toString === 'function') {
            sanitizedChanges[key] = `[${typeof value}]`;
          }
        });
      }
      window.parent.postMessage({ 
        type: 'pangeaLayerEvent', 
        event: 'changed', 
        data: { layerId, changes: sanitizedChanges }
      }, '*');
    }
  }
});

layer.error.add({
  handler: (error) => {
    console.error(`❌ Layer '${layerId}' error:`, error);
    
    // IMPORTANT: Sanitize error objects for postMessage
    if (window.parent && window.self !== window.top) {
      const sanitizedError = {
        message: error?.message || 'Unknown error',
        name: error?.name || 'Error',
        type: typeof error
      };
      window.parent.postMessage({ 
        type: 'pangeaLayerEvent', 
        event: 'error', 
        data: { layerId, error: sanitizedError }
      }, '*');
    }
  }
});
```

#### Animation Events
```typescript
// Animation control events
map.animator.started.add({
  handler: () => {
    console.log('▶️ Animation started');
  }
});

map.animator.stopped.add({
  handler: () => {
    console.log('⏸️ Animation stopped');
  }
});

// Frame change events (using recursive once pattern)
function addFrameListener() {
  map.animator.playhead.frameChanged.once({
    handler: function(e) {
      console.log('🎞️ Animation frame changed:', {
        frame: map.animator.playhead.frame,
        totalFrames: map.animator.length,
        time: map.animator.time.toISOString()
      });
      
      // Re-add listener for continuous updates
      if (map.animator.playhead.frameChanged) {
        addFrameListener();
      }
    }
  });
}
addFrameListener();
```

### Event Control System

#### Toggle Events On/Off
```typescript
// Control event monitoring
window._pangeaEventsEnabled = true;  // Enable events
window._pangeaEventsEnabled = false; // Disable events

// Or use the toggleEvents message
postMapMessage('toggleEvents', { enabled: true });
```

#### Event Statistics and Logging
```typescript
// Event statistics tracking
const eventStats = {
  map: 0,        // Map lifecycle and movement events
  layer: 0,      // Layer loading and changes  
  animation: 0   // Animation playback events
};

// Event logging with detailed information
function addEventToLog(type, event, data = {}) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`[${timestamp}] ${type}: ${event}`, data);
}
```

### Test Harness Integration

The test harness provides a complete event monitoring interface:

- **Real-time Event Log**: Shows last 20 events with timestamps and details
- **Event Statistics**: Counts events by category (Map, Layer, Animation)
- **Event Toggle**: Enable/disable event monitoring with checkbox
- **Visual Indicators**: Color-coded events with emoji icons
- **Data Formatting**: Smart formatting of coordinates, zoom levels, etc.

#### Using the Test Harness
1. Load map with valid tokens
2. Check "Enable Pangea Events" to start monitoring
3. Interact with map (pan, zoom, click, add layers)
4. Watch event log populate with detailed information
5. Use "Clear Event Log" to reset statistics and log

#### Event Message Types
- `pangeaMapEvent`: Map lifecycle and movement events
- `pangeaLayerEvent`: Layer loading and change events  
- `pangeaAnimationEvent`: Animation playback events

**Critical**: All event data sent via postMessage must be serializable. Complex Pangea SDK objects (sources, layers, etc.) cannot be directly transmitted and must be sanitized first.

All events include detailed data payloads for comprehensive debugging and application integration.

## Error Handling Essentials

### Map Creation Errors
```typescript
async function createMapSafely() {
  try {
    return await pangea.mapbox.MapboxViewport.create(containerId, token, options);
  } catch (error) {
    if (error.message.includes('access token')) {
      throw new Error('Invalid Mapbox token');
    } else if (error.message.includes('container')) {
      throw new Error('Map container not found');
    } else if (error.message.includes('WebGL')) {
      throw new Error('WebGL not supported');
    }
    throw error;
  }
}
```

### Layer Loading Errors
```typescript
layer.loading.add(() => console.log('Loading...'));
layer.loaded.add(() => console.log('Loaded'));
layer.error?.add((error) => {
  console.error('Layer error:', error);
  // Implement fallback strategy
});
```

### Network Errors
```typescript
async function fetchWithRetry(url, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return await response.json();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)));
    }
  }
}
```

## Performance Guidelines

### Layer Management
- Limit visible layers (< 5 recommended)
- Use appropriate layer slots
- Remove unused layers: `map.layers.remove(layerId)`

### Memory Management
- Clean up event listeners
- Dispose of resources: `resource.dispose()`
- Clear timers and intervals

### Data Optimization
- Limit feature count (< 1000 per layer recommended)
- Use appropriate zoom ranges: `zoomRange: [5, 15]`
- Simplify geometries for better performance

## TypeScript Types

```typescript
// Key interfaces
interface MapboxViewportOptions {
  geoCenter: [number, number];
  zoomLevel: number;
  bearing?: number;
  pitch?: number;
  basemap: string;
  zoomRange?: [number, number];
}

interface LayerOptions {
  id: string;
  opacity?: number;
  slot?: LayerSlot;
  zoomRange?: [number, number];
}

interface OverlayOptions {
  id?: string;
  zIndex?: number;
}
```

## Debugging Tools

```typescript
// Enable debug mode
window.pangeaDebug = {
  map: map,
  inspectLayer: (id) => map.layers.find(id),
  dumpState: () => ({
    center: [map.geoCenter.longitude, map.geoCenter.latitude],
    zoom: map.zoomLevel,
    layers: map.layers.length
  })
};

// Performance monitoring
const monitor = {
  start: performance.now(),
  log: (label) => console.log(`${label}: ${performance.now() - monitor.start}ms`)
};
```

## Common Pitfalls

1. **Coordinate Order**: Always [longitude, latitude], not [lat, lng]
2. **Async Operations**: Always await map creation and layer loading
3. **Event Cleanup**: Remove event listeners to prevent memory leaks
4. **Layer Slots**: Use appropriate slots for rendering order
5. **Error Handling**: Always wrap operations in try-catch blocks
6. **Token Management**: Ensure Mapbox tokens are valid and have permissions
7. **Container Timing**: Ensure DOM container exists before map creation
8. **Animation Setup Order**: Configure animator BEFORE adding animated layers
9. **Event Handler Pattern**: Use `{ handler: function }` object pattern for EventSource
10. **Source Connection**: Connect source to animator before adding layer to map
11. **Mobile Optimization**: Exclude mouse events for mobile/WebView environments
12. **Data Serialization**: Sanitize complex objects before postMessage transmission

## Quick Troubleshooting

| Issue | Check |
|-------|-------|
| Map not loading | Token validity, container existence, WebGL support |
| Layer not visible | Zoom range, opacity, slot, data validity |
| Animation not working | Setup order, source connection, .once() events |
| Poor performance | Layer count, feature count, memory usage |
| Interactions not working | Event listeners, layer.inspect() method |
| Memory leaks | Event cleanup, resource disposal, timer cleanup |

## Essential Imports Reference

```typescript
// Core
import pangea from '@twc/pangea-web/mapbox';

// Types (if needed)
import { 
  MapboxViewportOptions,
  LayerOptions,
  OverlayOptions,
  ArgumentError,
  ArgumentRangeError 
} from '@twc/pangea-web/mapbox';
```

This reference covers 90% of common Pangea SDK usage patterns. For complex scenarios, refer to the detailed sections in the full guide.
