# 3. Basic Map Operations

Once your Pangea map is initialized, you'll often need to control its view programmatically. This section covers common map operations like changing the center, zoom level, bearing, pitch, and fitting the map to specific bounds.

Assume you have a `map` instance obtained from `pangea.mapbox.MapboxViewport.create()`.

```typescript
// Assuming 'map' is your Pangea MapboxViewport instance
// const map: pangea.mapbox.MapboxViewport = await pangea.mapbox.MapboxViewport.create(...);
```

## Getting Current Map State

Before changing the map view, it's often useful to get its current state:

```typescript
// Get current geographic center
const currentCenter: pangea.geography.GeoPoint = map.geoCenter; // Returns a GeoPoint instance
console.log('📍 Current Center:', {
    latitude: currentCenter.latitude.toFixed(4),
    longitude: currentCenter.longitude.toFixed(4)
});

// Get current zoom level
const currentZoom: number = map.zoomLevel;
console.log('🔍 Current Zoom:', currentZoom.toFixed(2));

// Get current bearing (rotation)
const currentBearing: number = map.bearing;
console.log('🧭 Current Bearing:', currentBearing.toFixed(1) + '°');

// Get current pitch (tilt)
const currentPitch: number = map.pitch;
console.log('📐 Current Pitch:', currentPitch.toFixed(1) + '°');

// Get current geographic bounds
const currentBounds: pangea.geography.GeoBounds = map.bounds; // Returns a GeoBounds instance
console.log('🗺️  Current Bounds:', {
    north: currentBounds.north.toFixed(4),
    south: currentBounds.south.toFixed(4),
    east: currentBounds.east.toFixed(4),
    west: currentBounds.west.toFixed(4)
});

// Get the full current view options
const currentView: pangea.maps.ViewOptions = map.view;
console.log('👁️  Current View:', currentView);

// Get the current camera options (more relevant for 3D/globe views)
const currentCamera: pangea.maps.CameraOptions = map.camera;
console.log('📷 Current Camera:', currentCamera);

// Get map size and other properties
console.log('📊 Map Properties:', {
    size: `${map.size.width}x${map.size.height}px`,
    projection: map.projection,
    zoomRange: `${map.zoomRange.start}-${map.zoomRange.end}`,
    pitchRange: `${map.pitchRange.start}-${map.pitchRange.end}°`
});
```

## Setting Map View

Pangea provides several methods to change the map's viewport.

### Centering the Map (`map.center()`)

To change the map's center to a new geographic point:

```typescript
const newCenter: pangea.geography.GeoPointLike = [-122.4194, 37.7749]; // San Francisco [longitude, latitude]

console.log('🎯 Centering map on San Francisco...');

// Center immediately
map.center(newCenter);
console.log('✅ Map centered instantly');

// Center with animation
const animationOptions: pangea.maps.AnimationOptions = {
    duration: 2000, // 2 seconds
    easing: true    // Use default easing function
};

map.center(newCenter, animationOptions);
console.log('🎬 Map centering with 2-second animation');

// Set up event listener to know when centering is complete
map.moveEnded.add({
    handler: () => {
        const center: pangea.geography.GeoPoint = map.geoCenter;
        console.log(`📍 Map centered at: ${center.latitude.toFixed(4)}, ${center.longitude.toFixed(4)}`);
    }
});
```

### Zooming the Map (`map.zoom()`, `map.zoomIn()`, `map.zoomOut()`)

To change the map's zoom level:

```javascript
console.log('🔍 Current zoom level:', map.zoomLevel.toFixed(2));

// Set to a specific zoom level
map.zoom(12); // Zoom to level 12
console.log('🎯 Zooming to level 12');

// Zoom to level 14 with animation
map.zoom(14, { duration: 1000 });
console.log('🎬 Zooming to level 14 with animation');

// Zoom in by one level
map.zoomIn();
console.log('➕ Zooming in by one level');

// Zoom in with animation
map.zoomIn({ duration: 500 });
console.log('🎬 Zooming in with 500ms animation');

// Zoom out by one level
map.zoomOut();
console.log('➖ Zooming out by one level');

// Zoom out with animation
map.zoomOut({ duration: 500 });
console.log('🎬 Zooming out with 500ms animation');

// Listen for zoom events
map.zoomStarted.add({
    handler: () => {
        console.log('🔄 Zoom started...');
    }
});

map.zoomEnded.add({
    handler: () => {
        console.log(`✅ Zoom completed at level: ${map.zoomLevel.toFixed(2)}`);
    }
});
```

### Rotating the Map (Bearing) (`map.rotate()`)

To change the map's bearing (rotation):

```javascript
console.log('🧭 Current bearing:', map.bearing.toFixed(1) + '°');

// Rotate to 45 degrees (north-east up)
map.rotate(45);
console.log('🔄 Rotating to 45° (northeast up)');

// Rotate to 90 degrees (east up) with animation
map.rotate(90, { duration: 1500 });
console.log('🎬 Rotating to 90° (east up) with animation');

// Listen for rotation events
map.rotateStarted.add({
    handler: () => {
        console.log('🔄 Rotation started...');
    }
});

map.rotateEnded.add({
    handler: () => {
        console.log(`✅ Rotation completed at: ${map.bearing.toFixed(1)}°`);
    }
});
```

### Tilting the Map (Pitch) (`map.tilt()`)

To change the map's pitch (tilt angle):

```javascript
console.log('📐 Current pitch:', map.pitch.toFixed(1) + '°');

// Tilt to 30 degrees
map.tilt(30);
console.log('📐 Tilting to 30°');

// Tilt to 60 degrees with animation
map.tilt(60, { duration: 1000 });
console.log('🎬 Tilting to 60° with animation');

// Listen for tilt events
map.tiltStarted.add({
    handler: () => {
        console.log('🔄 Tilt started...');
    }
});

map.tiltEnded.add({
    handler: () => {
        console.log(`✅ Tilt completed at: ${map.pitch.toFixed(1)}°`);
    }
});
```

### Moving the Map (`map.move()`)

The `map.move()` method allows you to set multiple view parameters (center, zoom, bearing, pitch) at once. It's very flexible.

```javascript
const viewOptions = {
    geoCenter: [2.3522, 48.8566], // Paris
    zoomLevel: 13,
    bearing: 20,
    pitch: 45
};

console.log('🗼 Moving to Paris with custom view...');

// Move immediately
map.move(viewOptions);
console.log('✅ Moved to Paris instantly');

// Move with animation
map.move(viewOptions, { duration: 2500, flying: true }); // 'flying: true' gives a "fly-to" effect
console.log('🎬 Flying to Paris with 2.5-second animation');

// You can also specify an origin (screen point where geoCenter will be placed) and offset
map.move({
    geoCenter: [139.6917, 35.6895], // Tokyo
    zoomLevel: 10,
    origin: [100, 100],         // Place Tokyo at screen coordinates (100,100)
    offset: [50, 0]             // Then offset the view 50px to the right
});
console.log('🗾 Moving to Tokyo with custom origin and offset');

// Listen for move events
map.moveStarted.add({
    handler: () => {
        console.log('🚀 Map movement started...');
    }
});

map.moveEnded.add({
    handler: () => {
        console.log('✅ Map movement completed');
    }
});
```

### Orienting the Camera (`map.orient()`) (Experimental)

For more direct camera control, especially in 3D or globe projections:

```javascript
const cameraOptions = {
    position: [-73.9851, 40.7589], // Camera's geographic position
    altitude: 1500,                // Camera's altitude in meters
    target: [-74.006, 40.7128]     // Point the camera is looking at
};

console.log('📷 Orienting camera for 3D view...');
map.orient(cameraOptions);

// With animation
map.orient(cameraOptions, { duration: 3000 });
console.log('🎬 Orienting camera with 3-second animation');

// Note: map.orient() is marked as experimental in MapViewport.ts
console.log('⚠️  Note: Camera orientation is experimental');
```

### Panning the Map (`map.pan()`)

To pan the map by a specific number of screen pixels:

```javascript
console.log('👆 Panning map by pixel offset...');

// Pan 100 pixels to the right and 50 pixels down
map.pan([100, 50]); // [xOffset, yOffset]
console.log('➡️  Panned 100px right, 50px down');

// Pan with animation
map.pan([-50, -25], { duration: 750 }); // 50px left, 25px up
console.log('🎬 Panning 50px left, 25px up with animation');

// Listen for pan completion
map.moveEnded.add({
    handler: () => {
        const center = map.geoCenter;
        console.log(`📍 Pan completed, new center: ${center.latitude.toFixed(4)}, ${center.longitude.toFixed(4)}`);
    }
});
```

## Fitting the Map to Bounds (`map.fit()`)

To adjust the map view to encompass a specific geographic bounding box:

```javascript
// Bounds for New York City [west, south, east, north]
const bounds = [-74.047285, 40.683921, -73.926072, 40.879038];

console.log('🗽 Fitting map to New York City bounds...');

// Fit to bounds immediately
map.fit(bounds);
console.log('✅ Map fitted to bounds instantly');

// Fit to bounds with padding (10px on all sides) and animation
const padding = { top: 10, bottom: 10, left: 10, right: 10 };
map.fit(bounds, padding, { duration: 2000 });
console.log('🎬 Fitting to bounds with padding and 2-second animation');

// You can also use a single number for padding (applied to all sides)
map.fit(bounds, 20, { duration: 1000 }); // 20px padding
console.log('🎬 Fitting to bounds with 20px padding');

// Listen for fit completion
map.moveEnded.add({
    handler: () => {
        const mapBounds = map.bounds;
        console.log('📦 Fit completed, new bounds:', {
            north: mapBounds.north.toFixed(4),
            south: mapBounds.south.toFixed(4),
            east: mapBounds.east.toFixed(4),
            west: mapBounds.west.toFixed(4)
        });
    }
});
```

## Setting Maximum Bounds (`map.maximumBounds`)

You can restrict the pannable area of the map:

```javascript
// Restrict to most of the world
const maxBounds = [-180, -85, 180, 85];

console.log('🌍 Setting maximum bounds to restrict panning...');
map.maximumBounds = maxBounds;
console.log('✅ Maximum bounds set');

// To remove maximum bounds:
map.maximumBounds = null;
console.log('🔓 Maximum bounds removed');

// Note: As of the current codebase review, the maximumBounds setter 
// in MapboxViewport.ts is commented out, so this may not work with Mapbox engine
console.log('⚠️  Note: maximumBounds may not be fully implemented for Mapbox engine');
```

## Animation Options

Most view-changing methods accept an `AnimationOptions` object:

```javascript
// Available animation options
const animationOptions = {
    duration: 2000,     // Duration in milliseconds
    easing: true,       // Use default easing function
    flying: true        // For map.move(), creates a "fly-to" effect
};

console.log('🎬 Animation options available:', animationOptions);

// Example usage with different animations
console.log('🎭 Demonstrating different animation types...');

// Quick zoom
map.zoom(15, { duration: 500 });
console.log('⚡ Quick zoom (500ms)');

// Smooth rotation
map.rotate(180, { duration: 2000, easing: true });
console.log('🌊 Smooth rotation with easing (2000ms)');

// Flying movement
map.move({
    geoCenter: [0, 0], // Null Island
    zoomLevel: 5
}, { duration: 3000, flying: true });
console.log('✈️  Flying to Null Island (3000ms)');
```

## Event Handling for Map Movements

Pangea emits events when the map view changes. Here's comprehensive event logging:

```javascript
console.log('🎧 Setting up comprehensive map event listeners...');

// Movement events
map.moveStarted.add({
    handler: () => {
        console.log('🚀 Map movement started');
    }
});

map.moveEnded.add({
    handler: () => {
        console.log('🛑 Map movement ended');
    }
});

// Zoom events
map.zoomStarted.add({
    handler: () => {
        console.log('🔍 Map zoom started');
    }
});

map.zoomEnded.add({
    handler: () => {
        console.log('🎯 Map zoom ended');
    }
});

// Rotation events
map.rotateStarted.add({
    handler: () => {
        console.log('🔄 Map rotation started');
    }
});

map.rotateEnded.add({
    handler: () => {
        console.log('🧭 Map rotation ended');
    }
});

// Tilt events
map.tiltStarted.add({
    handler: () => {
        console.log('📐 Map tilt started');
    }
});

map.tiltEnded.add({
    handler: () => {
        console.log('📐 Map tilt ended');
    }
});

console.log('✅ All map event listeners configured');
```

## Changing Map Type and Style (`map.changeBasemap()`)

After creating your map, you can dynamically change its visual style and type by updating the basemap. This allows you to switch between different map styles like satellite, terrain, street maps, or custom styles. The `map.basemap` property is a getter that returns the current style, to change it you need to call `map.changeBasemap()`.

### Getting Current Map Style

First, let's see how to check the current map style:

```javascript
// Get the current basemap/style
const currentBasemap = map.basemap;
console.log('🗺️  Current basemap:', currentBasemap);

// The basemap property returns the style URL or identifier currently in use
```

### Changing to Predefined Mapbox Styles

Mapbox provides several built-in styles that you can switch to:

```javascript
console.log('🎨 Switching between different Mapbox styles...');

// Switch to satellite style
map.changeBasemap('mapbox://styles/mapbox/satellite-v9');
console.log('🛰️  Switched to satellite view');

// Switch to terrain style
map.changeBasemap('mapbox://styles/mapbox/outdoors-v12');
console.log('🏔️  Switched to terrain/outdoors style');

// Switch to dark style
map.changeBasemap('mapbox://styles/mapbox/dark-v11');
console.log('🌙 Switched to dark style');

// Switch to light style
map.changeBasemap('mapbox://styles/mapbox/light-v11');
console.log('☀️  Switched to light style');

// Switch to streets style
map.changeBasemap('mapbox://styles/mapbox/streets-v12');
console.log('🛣️  Switched to streets style');

// Switch to navigation style (optimized for turn-by-turn navigation)
map.changeBasemap('mapbox://styles/mapbox/navigation-day-v1');
console.log('🧭 Switched to navigation style');

// Switch to standard style (Mapbox's default modern style)
map.changeBasemap('mapbox://styles/mapbox/standard');
console.log('📍 Switched to standard style');
```

### Common Mapbox Style URLs

Here's a reference of commonly used Mapbox styles:

```javascript
const mapboxStyles = {
    // Basic styles
    standard: 'mapbox://styles/mapbox/standard',
    streets: 'mapbox://styles/mapbox/streets-v12',
    outdoors: 'mapbox://styles/mapbox/outdoors-v12',
    light: 'mapbox://styles/mapbox/light-v11',
    dark: 'mapbox://styles/mapbox/dark-v11',
    
    // Satellite and imagery
    satellite: 'mapbox://styles/mapbox/satellite-v9',
    satelliteStreets: 'mapbox://styles/mapbox/satellite-streets-v12',
    
    // Navigation styles
    navigationDay: 'mapbox://styles/mapbox/navigation-day-v1',
    navigationNight: 'mapbox://styles/mapbox/navigation-night-v1',
    
    // Legacy styles (still supported)
    basic: 'mapbox://styles/mapbox/basic-v9',
    bright: 'mapbox://styles/mapbox/bright-v9'
};

console.log('📚 Available Mapbox styles:', Object.keys(mapboxStyles));

// Function to switch styles easily
function switchMapStyle(styleName) {
    if (mapboxStyles[styleName]) {
        console.log(`🎨 Switching to ${styleName} style...`);
        map.changeBasemap(mapboxStyles[styleName]);
        console.log(`✅ Successfully switched to ${styleName}`);
    } else {
        console.error(`❌ Style '${styleName}' not found`);
        console.log('Available styles:', Object.keys(mapboxStyles));
    }
}

// Usage examples
switchMapStyle('satellite');
switchMapStyle('dark');
switchMapStyle('outdoors');
```

### Using Custom Mapbox Studio Styles

If you've created custom styles in Mapbox Studio, you can use them too:

```javascript
// Custom style created in Mapbox Studio
const customStyleUrl = 'mapbox://styles/your-username/your-custom-style-id';

console.log('🎨 Switching to custom style...');
map.changeBasemap(customStyleUrl);
console.log('✅ Custom style applied');

// You can also use the full style URL format
const fullCustomUrl = 'https://api.mapbox.com/styles/v1/your-username/your-style-id?access_token=your-token';
map.changeBasemap(fullCustomUrl);
console.log('🔗 Custom style applied via full URL');
```

### Dynamic Style Switching Based on Conditions

You can create dynamic style switching based on various conditions:

```javascript
// Switch style based on time of day
function setStyleByTimeOfDay() {
    const hour = new Date().getHours();
    
    if (hour >= 6 && hour < 18) {
        // Daytime: use light style
        console.log('☀️  Daytime detected, switching to light style');
        map.changeBasemap('mapbox://styles/mapbox/light-v11');
    } else {
        // Nighttime: use dark style
        console.log('🌙 Nighttime detected, switching to dark style');
        map.changeBasemap('mapbox://styles/mapbox/dark-v11');
    }
}

// Switch style based on zoom level
function setStyleByZoomLevel() {
    const zoom = map.zoomLevel;
    
    if (zoom > 15) {
        // High zoom: detailed streets
        console.log('🔍 High zoom detected, switching to streets style');
        map.changeBasemap('mapbox://styles/mapbox/streets-v12');
    } else if (zoom < 8) {
        // Low zoom: satellite for overview
        console.log('🛰️  Low zoom detected, switching to satellite style');
        map.changeBasemap('mapbox://styles/mapbox/satellite-v9');
    } else {
        // Medium zoom: standard style
        console.log('📍 Medium zoom detected, switching to standard style');
        map.changeBasemap('mapbox://styles/mapbox/standard');
    }
}

// Switch style based on map center (geographic region)
function setStyleByRegion() {
    const center = map.geoCenter;
    const lat = center.latitude;
    const lng = center.longitude;
    
    // Example: Use outdoors style for mountainous regions
    if ((lat > 35 && lat < 50 && lng > -125 && lng < -100) || // Western US mountains
        (lat > 45 && lat < 60 && lng > 5 && lng < 15)) {      // European Alps
        console.log('🏔️  Mountainous region detected, switching to outdoors style');
        map.changeBasemap('mapbox://styles/mapbox/outdoors-v12');
    } else {
        console.log('🏙️  Urban/general region, using standard style');
        map.changeBasemap('mapbox://styles/mapbox/standard');
    }
}

// Apply time-based styling
setStyleByTimeOfDay();

// Listen for zoom changes and adjust style accordingly
map.zoomEnded.add({
    handler: () => {
        setStyleByZoomLevel();
    }
});

// Listen for map movements and adjust style by region
map.moveEnded.add({
    handler: () => {
        setStyleByRegion();
    }
});
```

### Style Switching with User Controls

Create interactive controls for users to switch map styles:

```javascript
// Create a style switcher UI (assuming you have HTML elements)
function createStyleSwitcher() {
    const styles = [
        { name: 'Standard', id: 'standard', url: 'mapbox://styles/mapbox/standard' },
        { name: 'Satellite', id: 'satellite', url: 'mapbox://styles/mapbox/satellite-v9' },
        { name: 'Streets', id: 'streets', url: 'mapbox://styles/mapbox/streets-v12' },
        { name: 'Outdoors', id: 'outdoors', url: 'mapbox://styles/mapbox/outdoors-v12' },
        { name: 'Dark', id: 'dark', url: 'mapbox://styles/mapbox/dark-v11' },
        { name: 'Light', id: 'light', url: 'mapbox://styles/mapbox/light-v11' }
    ];
    
    console.log('🎛️  Creating style switcher controls...');
    
    styles.forEach(style => {
        // Assuming you have buttons with IDs like 'style-standard', 'style-satellite', etc.
        const button = document.getElementById(`style-${style.id}`);
        if (button) {
            button.addEventListener('click', () => {
                console.log(`🎨 User selected ${style.name} style`);
                map.changeBasemap(style.url);
                
                // Update UI to show active style
                document.querySelectorAll('.style-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                button.classList.add('active');
                
                console.log(`✅ Switched to ${style.name} style`);
            });
        }
    });
    
    console.log('✅ Style switcher controls configured');
}

// Initialize the style switcher
createStyleSwitcher();
```

### Monitoring Style Changes

You can listen for style change events to react when the basemap changes:

```javascript
console.log('🎧 Setting up style change monitoring...');

// Listen for style changes
map.styleChanged.add({
    handler: (event) => {
        console.log('🎨 Map style changed:', {
            newStyle: event.style || map.basemap,
            timestamp: new Date().toISOString()
        });
    }
});

// Listen for style load events
map.styleLoaded.add({
    handler: () => {
        console.log('✅ New style loaded successfully');
        console.log('📊 Current map state:', {
            style: map.basemap,
            center: `${map.geoCenter.latitude.toFixed(4)}, ${map.geoCenter.longitude.toFixed(4)}`,
            zoom: map.zoomLevel.toFixed(2)
        });
    }
});

// Note: Event names may vary depending on the Pangea version
// Check the MapboxViewport documentation for exact event names
console.log('ℹ️  Style change events configured');
```

### Error Handling for Style Changes

Always handle potential errors when changing styles:

```javascript
function safeStyleChange(newStyle, fallbackStyle = 'mapbox://styles/mapbox/standard') {
    try {
        console.log(`🎨 Attempting to change style to: ${newStyle}`);
        
        // Store current style as backup
        const currentStyle = map.basemap;
        
        // Attempt to change style
        map.changeBasemap(newStyle);
        
        // Set up a timeout to check if style loaded successfully
        const timeoutId = setTimeout(() => {
            console.warn('⚠️  Style change timeout, reverting to fallback');
            map.changeBasemap(fallbackStyle);
        }, 5000); // 5 second timeout
        
        // Clear timeout when style loads successfully
        const styleLoadHandler = () => {
            clearTimeout(timeoutId);
            console.log('✅ Style changed successfully');
            map.styleLoaded.remove(styleLoadHandler); // Remove this specific handler
        };
        
        map.styleLoaded.add({ handler: styleLoadHandler });
        
    } catch (error) {
        console.error('❌ Error changing map style:', error);
        console.log(`🔄 Reverting to fallback style: ${fallbackStyle}`);
        map.changeBasemap(fallbackStyle);
    }
}

// Usage examples
safeStyleChange('mapbox://styles/mapbox/satellite-v9');
safeStyleChange('mapbox://styles/invalid/style', 'mapbox://styles/mapbox/streets-v12');
```

### Complete Style Management Example

Here's a comprehensive example that demonstrates style management:

```javascript
class MapStyleManager {
    constructor(map) {
        this.map = map;
        this.currentStyle = map.basemap;
        this.styleHistory = [this.currentStyle];
        this.maxHistorySize = 10;
        
        this.setupEventListeners();
        console.log('🎨 MapStyleManager initialized');
    }
    
    setupEventListeners() {
        this.map.styleLoaded.add({
            handler: () => {
                this.currentStyle = this.map.basemap;
                this.addToHistory(this.currentStyle);
                console.log(`✅ Style loaded: ${this.currentStyle}`);
            }
        });
    }
    
    addToHistory(style) {
        if (this.styleHistory[this.styleHistory.length - 1] !== style) {
            this.styleHistory.push(style);
            if (this.styleHistory.length > this.maxHistorySize) {
                this.styleHistory.shift();
            }
        }
    }
    
    changeStyle(newStyle) {
        console.log(`🎨 Changing style from ${this.currentStyle} to ${newStyle}`);
        this.map.changeBasemap(newStyle);
    }
    
    revertToPreviousStyle() {
        if (this.styleHistory.length > 1) {
            const previousStyle = this.styleHistory[this.styleHistory.length - 2];
            console.log(`⏪ Reverting to previous style: ${previousStyle}`);
            this.changeStyle(previousStyle);
        } else {
            console.log('ℹ️  No previous style to revert to');
        }
    }
    
    getStyleHistory() {
        return [...this.styleHistory];
    }
    
    presetStyles = {
        standard: 'mapbox://styles/mapbox/standard',
        satellite: 'mapbox://styles/mapbox/satellite-v9',
        streets: 'mapbox://styles/mapbox/streets-v12',
        outdoors: 'mapbox://styles/mapbox/outdoors-v12',
        dark: 'mapbox://styles/mapbox/dark-v11',
        light: 'mapbox://styles/mapbox/light-v11'
    };
    
    applyPreset(presetName) {
        if (this.presetStyles[presetName]) {
            console.log(`🎯 Applying preset: ${presetName}`);
            this.changeStyle(this.presetStyles[presetName]);
        } else {
            console.error(`❌ Preset '${presetName}' not found`);
            console.log('Available presets:', Object.keys(this.presetStyles));
        }
    }
}

// Usage
const styleManager = new MapStyleManager(map);

// Change styles using the manager
styleManager.applyPreset('satellite');
styleManager.applyPreset('dark');
styleManager.revertToPreviousStyle();

// Check style history
console.log('📚 Style history:', styleManager.getStyleHistory());
```

## Resizing the Map (`map.resize()`)

If the map's container element changes size, you need to tell the map to resize itself:

```javascript
// If you manually resize the map's div:
const mapDiv = document.getElementById('map');
mapDiv.style.width = '800px';
mapDiv.style.height = '600px';

console.log('📏 Resizing map container to 800x600px...');

// Tell Pangea to update the map's size
map.resize([800, 600]); // [width, height]
// or
map.resize({ width: 800, height: 600 });

console.log('✅ Map resized successfully');

// Listen for resize events
map.resized.add({
    handler: (event) => {
        console.log(`📐 Map resized to: ${event.size.width}x${event.size.height}px`);
    }
});

// Note: MapboxViewport constructor has an autoResize option (default true)
// which should handle window resize events automatically
console.log('ℹ️  Auto-resize is enabled by default for window resize events');
```

## Complete Working Example

Here's a complete example demonstrating various map operations:

```javascript
import pangea from '@twc/pangea-web/mapbox';

const MAPBOX_ACCESS_TOKEN = 'your-mapbox-access-token-here';

async function demonstrateMapOperations() {
    try {
        console.log('🚀 Creating map for operations demo...');
        
        const map = await pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, {
            geoCenter: [-74.006, 40.7128], // New York City
            zoomLevel: 10,
            bearing: 0,
            pitch: 0,
            basemap: 'mapbox://styles/mapbox/standard'
        });
        
        console.log('✅ Map created successfully');
        
        // Set up comprehensive event logging
        setupEventLogging(map);
        
        // Wait for map to load
        await new Promise(resolve => {
            map.loaded.add({ handler: resolve });
        });
        
        console.log('🎬 Starting map operations demonstration...');
        
        // Demonstrate various operations with delays
        setTimeout(() => {
            console.log('📍 Step 1: Moving to San Francisco...');
            map.center([-122.4194, 37.7749], { duration: 2000 });
        }, 1000);
        
        setTimeout(() => {
            console.log('🔍 Step 2: Zooming in...');
            map.zoom(14, { duration: 1500 });
        }, 4000);
        
        setTimeout(() => {
            console.log('🧭 Step 3: Rotating map...');
            map.rotate(45, { duration: 1000 });
        }, 6000);
        
        setTimeout(() => {
            console.log('📐 Step 4: Tilting map...');
            map.tilt(60, { duration: 1000 });
        }, 8000);
        
        setTimeout(() => {
            console.log('🗼 Step 5: Flying to Paris...');
            map.move({
                geoCenter: [2.3522, 48.8566],
                zoomLevel: 12,
                bearing: 0,
                pitch: 0
            }, { duration: 3000, flying: true });
        }, 10000);
        
        setTimeout(() => {
            console.log('🗽 Step 6: Fitting to New York bounds...');
            const nycBounds = [-74.047285, 40.683921, -73.926072, 40.879038];
            map.fit(nycBounds, 50, { duration: 2000 });
        }, 14000);
        
        console.log('🎭 Map operations demo started - watch the console and map!');
        
    } catch (error) {
        console.error('❌ Error in map operations demo:', error);
    }
}

function setupEventLogging(map) {
    // Movement events
    map.moveStarted.add({ handler: () => console.log('🚀 Movement started') });
    map.moveEnded.add({ handler: () => console.log('🛑 Movement ended') });
    
    // Zoom events
    map.zoomStarted.add({ handler: () => console.log('🔍 Zoom started') });
    map.zoomEnded.add({ handler: () => console.log(`🎯 Zoom ended: ${map.zoomLevel.toFixed(2)}`) });
    
    // Rotation events
    map.rotateStarted.add({ handler: () => console.log('🔄 Rotation started') });
    map.rotateEnded.add({ handler: () => console.log(`🧭 Rotation ended: ${map.bearing.toFixed(1)}°`) });
    
    // Tilt events
    map.tiltStarted.add({ handler: () => console.log('📐 Tilt started') });
    map.tiltEnded.add({ handler: () => console.log(`📐 Tilt ended: ${map.pitch.toFixed(1)}°`) });
}

// Run the demonstration
demonstrateMapOperations();
```

This covers the fundamental ways to control and interact with the map's viewport. The comprehensive logging helps you understand exactly what's happening during each operation, making it easier to debug and learn the API.

## Summary

Key map operations available in Pangea:

- **`map.center()`** - Change map center
- **`map.zoom()`**, **`map.zoomIn()`**, **`map.zoomOut()`** - Control zoom level
- **`map.rotate()`** - Change bearing/rotation
- **`map.tilt()`** - Change pitch/tilt
- **`map.move()`** - Change multiple properties at once
- **`map.pan()`** - Move by pixel offset
- **`map.fit()`** - Fit to geographic bounds
- **`map.changeBasemap()`** - Change map style and type (satellite, streets, dark, etc.)
- **`map.resize()`** - Update map size

All operations support optional animation parameters and emit detailed events for monitoring and debugging. Map style changes can be done dynamically using predefined Mapbox styles or custom styles created in Mapbox Studio.
