# 7. Error Handling and Debugging

Robust error handling and effective debugging are crucial for building reliable applications with the Pangea Web SDK. This section covers common error scenarios, debugging techniques, performance monitoring, and best practices for maintaining stable map applications.

## Understanding Pangea Error Types

Pangea provides several specific error types to help you identify and handle different failure scenarios.

### Core Error Types

```typescript
console.log('🚨 Understanding Pangea error types...');

// Import error types from Pangea core
import { ArgumentError, ArgumentRangeError, NotImplementedError, NotSupportedError } from '@twc/pangea-web/mapbox';

// Example of handling different error types
function demonstrateErrorTypes() {
    console.log('📚 Pangea Error Types Reference:');
    console.log('  ArgumentError: Invalid arguments passed to methods');
    console.log('  ArgumentRangeError: Arguments outside valid ranges');
    console.log('  NotImplementedError: Features not yet implemented');
    console.log('  NotSupportedError: Features not supported by current engine');
    
    // Example: Catching specific error types
    try {
        // This might throw an ArgumentRangeError
        map.zoom(-5); // Invalid zoom level
    } catch (error) {
        if (error instanceof ArgumentRangeError) {
            console.error('🎯 Zoom level out of range:', error.message);
            console.log('📊 Valid zoom range:', map.zoomRange);
            // Handle gracefully by setting to valid range
            map.zoom(map.zoomRange.start);
        } else {
            console.error('❌ Unexpected error:', error);
        }
    }
}

demonstrateErrorTypes();
```

### Map Creation Errors

```typescript
console.log('🗺️  Handling map creation errors...');

async function createMapWithErrorHandling() {
    try {
        console.log('🔄 Attempting to create map...');
        
        const map = await pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, {
            geoCenter: [-74.006, 40.7128],
            zoomLevel: 10,
            basemap: 'mapbox://styles/mapbox/standard'
        });
        
        console.log('✅ Map created successfully');
        return map;
        
    } catch (error) {
        console.error('💥 Map creation failed:', error);
        
        // Handle specific error scenarios
        if (error.message.includes('access token')) {
            console.error('🔑 Mapbox access token error:');
            console.log('  - Check if token is valid and not expired');
            console.log('  - Verify token has necessary permissions');
            console.log('  - Ensure token is properly formatted');
            
            // Show user-friendly error
            showUserError('Invalid map access token. Please check your configuration.');
            
        } else if (error.message.includes('container')) {
            console.error('📦 Map container error:');
            console.log('  - Verify container element exists in DOM');
            console.log('  - Check container has valid dimensions');
            console.log('  - Ensure container is visible when map is created');
            
            showUserError('Map container not found. Please check the page structure.');
            
        } else if (error.message.includes('WebGL')) {
            console.error('🎮 WebGL error:');
            console.log('  - Browser may not support WebGL');
            console.log('  - WebGL may be disabled');
            console.log('  - Graphics drivers may need updating');
            
            showUserError('Your browser does not support the required graphics features.');
            
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            console.error('🌐 Network error:');
            console.log('  - Check internet connection');
            console.log('  - Verify firewall/proxy settings');
            console.log('  - Check if Mapbox services are accessible');
            
            showUserError('Unable to connect to map services. Please check your internet connection.');
            
        } else {
            console.error('🚨 Unknown map creation error:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });
            
            showUserError('An unexpected error occurred while loading the map.');
        }
        
        throw error; // Re-throw for upstream handling
    }
}

function showUserError(message) {
    // Example user notification
    console.log(`🔔 User notification: ${message}`);
    
    // In a real application, you might show a toast, modal, or inline error
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    errorDiv.textContent = message;
    document.body.appendChild(errorDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// Usage with retry logic
async function createMapWithRetry(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔄 Map creation attempt ${attempt}/${maxRetries}`);
            return await createMapWithErrorHandling();
        } catch (error) {
            console.warn(`⚠️  Attempt ${attempt} failed:`, error.message);
            
            if (attempt === maxRetries) {
                console.error('💥 All map creation attempts failed');
                throw new Error(`Failed to create map after ${maxRetries} attempts: ${error.message}`);
            }
            
            // Wait before retry (exponential backoff)
            const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s...
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

## Mobile/WebView Integration Errors

### DataCloneError: postMessage Serialization

When integrating Pangea maps in mobile apps or WebViews, you may encounter `DataCloneError` when trying to send complex Pangea objects through `postMessage`. This happens because Pangea SDK objects contain functions and complex references that cannot be serialized.

```typescript
console.log('📱 Handling mobile integration errors...');

// ❌ WRONG: This will cause DataCloneError
layer.changed.add({
    handler: (changes) => {
        console.log('Layer changed:', changes);
        
        // This will fail - complex Pangea objects can't be cloned
        window.parent.postMessage({ 
            type: 'layerChanged', 
            data: { changes } // Contains _SunGridSource and other complex objects
        }, '*');
    }
});

// ✅ CORRECT: Sanitize data before postMessage
layer.changed.add({
    handler: (changes) => {
        console.log('Layer changed:', changes);
        
        // Sanitize complex objects for postMessage
        const sanitizedChanges = {};
        if (changes && typeof changes === 'object') {
            Object.keys(changes).forEach(key => {
                const value = changes[key];
                if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                    sanitizedChanges[key] = value;
                } else if (value && typeof value.toString === 'function') {
                    sanitizedChanges[key] = `[${typeof value}]`;
                }
            });
        }
        
        // Safe to send via postMessage
        window.parent.postMessage({ 
            type: 'layerChanged', 
            data: { layerId, changes: sanitizedChanges }
        }, '*');
    }
});

// Generic function to sanitize objects for postMessage
function sanitizeForPostMessage(obj, maxDepth = 3, currentDepth = 0) {
    if (currentDepth >= maxDepth) {
        return `[Object: max depth reached]`;
    }
    
    if (obj === null || obj === undefined) {
        return obj;
    }
    
    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return obj.toISOString();
    }
    
    if (Array.isArray(obj)) {
        return obj.map(item => sanitizeForPostMessage(item, maxDepth, currentDepth + 1));
    }
    
    if (typeof obj === 'object') {
        const sanitized = {};
        Object.keys(obj).forEach(key => {
            try {
                const value = obj[key];
                if (typeof value === 'function') {
                    sanitized[key] = '[Function]';
                } else if (value && typeof value === 'object' && value.constructor && value.constructor.name) {
                    // Pangea SDK objects
                    sanitized[key] = `[${value.constructor.name}]`;
                } else {
                    sanitized[key] = sanitizeForPostMessage(value, maxDepth, currentDepth + 1);
                }
            } catch (error) {
                sanitized[key] = '[Unserializable]';
            }
        });
        return sanitized;
    }
    
    return `[${typeof obj}]`;
}

// Usage example
function safePostMessage(type, data) {
    try {
        const sanitizedData = sanitizeForPostMessage(data);
        window.parent.postMessage({ type, data: sanitizedData }, '*');
    } catch (error) {
        console.error('Failed to send message:', error);
        // Send minimal error info instead
        window.parent.postMessage({ 
            type: 'error', 
            data: { message: 'Failed to serialize data', error: error.message }
        }, '*');
    }
}
```

### Event Handler Pattern for Mobile

For mobile/WebView integration, use the correct EventSource pattern:

```typescript
// ✅ CORRECT: Use { handler: function } pattern
map.moveEnded.add({
    handler: () => {
        const center = map.geoCenter;
        safePostMessage('mapMoved', {
            latitude: center.latitude,
            longitude: center.longitude,
            zoom: map.zoomLevel
        });
    }
});

// ❌ WRONG: Direct function (may not work consistently)
map.moveEnded.add(() => {
    // This pattern is less reliable
});
```

## Layer Error Handling

```typescript
console.log('🗂️  Handling layer-related errors...');

// Comprehensive layer error handling
function addLayerWithErrorHandling(layerConfig, sourceConfig) {
    try {
        console.log(`🔄 Adding layer: ${layerConfig.options.id}`);
        
        // Validate layer configuration
        validateLayerConfig(layerConfig);
        validateSourceConfig(sourceConfig);
        
        const layer = map.addLayer(layerConfig, sourceConfig);
        
        // Set up layer error monitoring
        setupLayerErrorMonitoring(layer);
        
        console.log(`✅ Layer added successfully: ${layer.id}`);
        return layer;
        
    } catch (error) {
        console.error(`💥 Failed to add layer ${layerConfig.options.id}:`, error);
        
        // Handle specific layer errors
        if (error.message.includes('source')) {
            console.error('📦 Data source error:');
            console.log('  - Check if data URL is accessible');
            console.log('  - Verify data format is correct');
            console.log('  - Ensure source configuration is valid');
            
        } else if (error.message.includes('style')) {
            console.error('🎨 Layer styling error:');
            console.log('  - Check styleFeature function for errors');
            console.log('  - Verify overlay creation is valid');
            console.log('  - Ensure all required properties are present');
            
        } else if (error.message.includes('slot')) {
            console.error('📚 Layer slot error:');
            console.log('  - Verify slot value is valid LayerSlot enum');
            console.log('  - Check for slot conflicts');
            
        } else {
            console.error('🚨 Unknown layer error:', error);
        }
        
        // Attempt fallback or cleanup
        handleLayerFailure(layerConfig, error);
        
        throw error;
    }
}

function validateLayerConfig(layerConfig) {
    console.log('🔍 Validating layer configuration...');
    
    if (!layerConfig || !layerConfig.options) {
        throw new ArgumentError('Layer configuration is required');
    }
    
    if (!layerConfig.options.id) {
        throw new ArgumentError('Layer ID is required');
    }
    
    if (!layerConfig.type) {
        throw new ArgumentError('Layer type is required');
    }
    
    // Validate layer type
    const validLayerTypes = ['RasterLayer', 'FeatureLayer', 'OverlayLayer', 'HeatmapLayer'];
    if (!validLayerTypes.includes(layerConfig.type)) {
        throw new ArgumentError(`Invalid layer type: ${layerConfig.type}`);
    }
    
    console.log('✅ Layer configuration is valid');
}

function validateSourceConfig(sourceConfig) {
    console.log('🔍 Validating source configuration...');
    
    if (!sourceConfig || !sourceConfig.options) {
        throw new ArgumentError('Source configuration is required');
    }
    
    if (!sourceConfig.options.id) {
        throw new ArgumentError('Source ID is required');
    }
    
    if (!sourceConfig.type) {
        throw new ArgumentError('Source type is required');
    }
    
    console.log('✅ Source configuration is valid');
}

function setupLayerErrorMonitoring(layer) {
    console.log(`🎧 Setting up error monitoring for layer: ${layer.id}`);
    
    // Monitor loading failures
    layer.loading.add(() => {
        console.log(`⏳ Layer ${layer.id} started loading...`);
        
        // Set up timeout for loading
        const loadTimeout = setTimeout(() => {
            console.warn(`⚠️  Layer ${layer.id} loading timeout (30s)`);
            // Could trigger retry or fallback here
        }, 30000);
        
        // Clear timeout when loaded
        const loadedHandler = () => {
            clearTimeout(loadTimeout);
            layer.loaded.remove(loadedHandler);
            console.log(`✅ Layer ${layer.id} loaded successfully`);
        };
        layer.loaded.add(loadedHandler);
    });
    
    // Monitor for layer errors (if available)
    if (layer.error) {
        layer.error.add((errorEvent) => {
            console.error(`💥 Layer ${layer.id} error:`, errorEvent);
            handleLayerRuntimeError(layer, errorEvent);
        });
    }
    
    // Monitor visibility issues
    layer.changed.add((changes) => {
        if ('isVisible' in changes && !layer.isVisible && layer.opacity > 0) {
            console.warn(`⚠️  Layer ${layer.id} is not visible despite opacity > 0`);
            console.log('🔍 Possible causes:');
            console.log('  - Layer is outside current zoom range');
            console.log('  - Layer is outside current time range');
            console.log('  - Layer data failed to load');
            console.log('  - Layer is hidden programmatically');
        }
    });
}

function handleLayerFailure(layerConfig, error) {
    console.log(`🔧 Handling layer failure for: ${layerConfig.options.id}`);
    
    // Attempt to create a fallback layer
    try {
        if (layerConfig.type === 'RasterLayer') {
            console.log('🔄 Attempting fallback to basic tile layer...');
            // Could create a simpler tile layer as fallback
        } else if (layerConfig.type === 'FeatureLayer') {
            console.log('🔄 Attempting fallback to static overlay...');
            // Could create static overlays as fallback
        }
    } catch (fallbackError) {
        console.error('💥 Fallback layer creation also failed:', fallbackError);
    }
}

function handleLayerRuntimeError(layer, errorEvent) {
    console.error(`🚨 Runtime error in layer ${layer.id}:`, errorEvent);
    
    // Attempt recovery strategies
    if (errorEvent.type === 'network') {
        console.log('🔄 Attempting to reload layer data...');
        // Could trigger layer refresh
        if (layer.refresh) {
            layer.refresh();
        }
    } else if (errorEvent.type === 'rendering') {
        console.log('🎨 Attempting to reset layer styling...');
        // Could reset layer style or reduce complexity
    }
}
```

## Data Loading and Network Error Handling

```typescript
console.log('🌐 Handling data loading and network errors...');

// Robust data fetching with error handling
async function fetchDataWithErrorHandling(url, options = {}) {
    const maxRetries = options.maxRetries || 3;
    const timeout = options.timeout || 10000;
    const retryDelay = options.retryDelay || 1000;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔄 Fetching data (attempt ${attempt}/${maxRetries}): ${url}`);
            
            // Create abort controller for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);
            
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log(`✅ Data fetched successfully (${data.features?.length || 'unknown'} features)`);
            
            return data;
            
        } catch (error) {
            console.warn(`⚠️  Fetch attempt ${attempt} failed:`, error.message);
            
            // Handle specific error types
            if (error.name === 'AbortError') {
                console.error('⏰ Request timeout');
            } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                console.error('🌐 Network connectivity issue');
            } else if (error.message.includes('404')) {
                console.error('📂 Data not found');
                break; // Don't retry for 404s
            } else if (error.message.includes('403') || error.message.includes('401')) {
                console.error('🔒 Authentication/authorization error');
                break; // Don't retry for auth errors
            }
            
            if (attempt === maxRetries) {
                console.error('💥 All fetch attempts failed');
                throw new Error(`Failed to fetch data after ${maxRetries} attempts: ${error.message}`);
            }
            
            // Wait before retry with exponential backoff
            const delay = retryDelay * Math.pow(2, attempt - 1);
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

// Example: Loading GeoJSON data with error handling
async function loadGeoJSONLayer(url, layerOptions) {
    try {
        console.log(`📊 Loading GeoJSON layer from: ${url}`);
        
        // Fetch data with error handling
        const geoJsonData = await fetchDataWithErrorHandling(url, {
            maxRetries: 3,
            timeout: 15000
        });
        
        // Validate GeoJSON structure
        validateGeoJSONData(geoJsonData);
        
        // Create source and layer
        const source = new pangea.sources.GeoJSONFeatureSource(
            geoJsonData,
            { id: `${layerOptions.id}-source` }
        );
        
        const layer = new pangea.layers.FeatureLayer(
            source,
            layerOptions
        );
        
        // Add to map with error handling
        map.layers.add(layer);
        
        // Monitor for loading completion
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Layer loading timeout'));
            }, 30000);
            
            layer.loaded.add(() => {
                clearTimeout(timeout);
                console.log(`✅ GeoJSON layer loaded: ${layer.id}`);
                resolve(layer);
            });
            
            if (layer.error) {
                layer.error.add((error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            }
        });
        
    } catch (error) {
        console.error('💥 Failed to load GeoJSON layer:', error);
        
        // Create fallback layer with error message
        createErrorLayer(layerOptions.id, error.message);
        
        throw error;
    }
}

function validateGeoJSONData(data) {
    console.log('🔍 Validating GeoJSON data...');
    
    if (!data || typeof data !== 'object') {
        throw new Error('Invalid GeoJSON: Data is not an object');
    }
    
    if (data.type !== 'FeatureCollection' && data.type !== 'Feature') {
        throw new Error(`Invalid GeoJSON: Expected FeatureCollection or Feature, got ${data.type}`);
    }
    
    if (data.type === 'FeatureCollection') {
        if (!Array.isArray(data.features)) {
            throw new Error('Invalid GeoJSON: FeatureCollection must have features array');
        }
        
        if (data.features.length === 0) {
            console.warn('⚠️  GeoJSON FeatureCollection is empty');
        }
        
        // Validate first few features
        for (let i = 0; i < Math.min(5, data.features.length); i++) {
            const feature = data.features[i];
            if (!feature.geometry || !feature.properties) {
                throw new Error(`Invalid GeoJSON: Feature ${i} missing geometry or properties`);
            }
        }
    }
    
    console.log('✅ GeoJSON data is valid');
}

function createErrorLayer(layerId, errorMessage) {
    console.log(`🚨 Creating error layer for: ${layerId}`);
    
    // Create a simple overlay showing the error
    const errorOverlay = new pangea.overlays.TextMarker(
        `❌ ${errorMessage}`,
        map.geoCenter,
        {
            color: pangea.visuals.Color.RED,
            size: 14,
            halo: { color: pangea.visuals.Color.WHITE, width: 2 }
        },
        { id: `${layerId}-error` }
    );
    
    const errorSource = new pangea.sources.StaticOverlaySource(
        [errorOverlay],
        { id: `${layerId}-error-source` }
    );
    
    const errorLayer = new pangea.layers.OverlayLayer(
        errorSource,
        {
            id: `${layerId}-error-layer`,
            slot: pangea.layers.LayerSlot.TOP
        }
    );
    
    map.layers.add(errorLayer);
    
    // Auto-remove error layer after 10 seconds
    setTimeout(() => {
        map.layers.remove(errorLayer);
    }, 10000);
}
```

## Performance Monitoring and Debugging

```typescript
console.log('📊 Setting up performance monitoring...');

// Performance monitoring class
class PangeaPerformanceMonitor {
    constructor(map) {
        this.map = map;
        this.metrics = {
            frameRate: [],
            layerLoadTimes: new Map(),
            memoryUsage: [],
            renderTimes: []
        };
        this.isMonitoring = false;
        
        this.setupMonitoring();
    }
    
    setupMonitoring() {
        console.log('🎧 Setting up performance monitoring...');
        
        // Monitor frame rate
        this.startFrameRateMonitoring();
        
        // Monitor layer performance
        this.setupLayerMonitoring();
        
        // Monitor memory usage
        this.startMemoryMonitoring();
        
        // Monitor map events
        this.setupMapEventMonitoring();
    }
    
    startFrameRateMonitoring() {
        let lastTime = performance.now();
        let frameCount = 0;
        
        const measureFrameRate = () => {
            if (!this.isMonitoring) return;
            
            const currentTime = performance.now();
            frameCount++;
            
            // Calculate FPS every second
            if (currentTime - lastTime >= 1000) {
                const fps = frameCount / ((currentTime - lastTime) / 1000);
                this.metrics.frameRate.push({
                    timestamp: currentTime,
                    fps: fps
                });
                
                // Keep only last 60 seconds of data
                if (this.metrics.frameRate.length > 60) {
                    this.metrics.frameRate.shift();
                }
                
                // Log performance warnings
                if (fps < 30) {
                    console.warn(`⚠️  Low frame rate detected: ${fps.toFixed(1)} FPS`);
                    this.diagnosePerformanceIssues();
                }
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFrameRate);
        };
        
        requestAnimationFrame(measureFrameRate);
    }
    
    setupLayerMonitoring() {
        // Monitor existing layers
        this.map.layers.items.forEach(layer => {
            this.monitorLayer(layer);
        });
        
        // Monitor new layers
        this.map.layers.added.add((event) => {
            const layer = event.items[0].item;
            this.monitorLayer(layer);
        });
    }
    
    monitorLayer(layer) {
        const layerId = layer.id;
        let loadStartTime;
        
        layer.loading.add(() => {
            loadStartTime = performance.now();
            console.log(`⏳ Layer ${layerId} started loading...`);
        });
        
        layer.loaded.add(() => {
            if (loadStartTime) {
                const loadTime = performance.now() - loadStartTime;
                this.metrics.layerLoadTimes.set(layerId, loadTime);
                
                console.log(`✅ Layer ${layerId} loaded in ${loadTime.toFixed(2)}ms`);
                
                // Warn about slow loading layers
                if (loadTime > 5000) {
                    console.warn(`⚠️  Slow layer loading: ${layerId} took ${loadTime.toFixed(2)}ms`);
                }
            }
        });
        
        // Monitor layer visibility changes
        layer.changed.add((changes) => {
            if ('isVisible' in changes) {
                console.log(`👁️  Layer ${layerId} visibility: ${layer.isVisible}`);
            }
            
            if ('opacity' in changes) {
                console.log(`🎨 Layer ${layerId} opacity: ${layer.opacity}`);
            }
        });
    }
    
    startMemoryMonitoring() {
        if (!performance.memory) {
            console.warn('⚠️  Memory monitoring not available in this browser');
            return;
        }
        
        setInterval(() => {
            if (!this.isMonitoring) return;
            
            const memory = {
                timestamp: performance.now(),
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
            
            this.metrics.memoryUsage.push(memory);
            
            // Keep only last 5 minutes of data
            if (this.metrics.memoryUsage.length > 300) {
                this.metrics.memoryUsage.shift();
            }
            
            // Warn about high memory usage
            const usagePercent = (memory.used / memory.limit) * 100;
            if (usagePercent > 80) {
                console.warn(`⚠️  High memory usage: ${usagePercent.toFixed(1)}% (${(memory.used / 1024 / 1024).toFixed(1)}MB)`);
            }
            
        }, 1000);
    }
    
    setupMapEventMonitoring() {
        // Monitor map movements
        let moveStartTime;
        
        this.map.moveStarted.add(() => {
            moveStartTime = performance.now();
        });
        
        this.map.moveEnded.add(() => {
            if (moveStartTime) {
                const moveTime = performance.now() - moveStartTime;
                console.log(`🗺️  Map movement completed in ${moveTime.toFixed(2)}ms`);
                
                if (moveTime > 1000) {
                    console.warn(`⚠️  Slow map movement: ${moveTime.toFixed(2)}ms`);
                }
            }
        });
        
        // Monitor zoom events
        let zoomStartTime;
        
        this.map.zoomStarted.add(() => {
            zoomStartTime = performance.now();
        });
        
        this.map.zoomEnded.add(() => {
            if (zoomStartTime) {
                const zoomTime = performance.now() - zoomStartTime;
                console.log(`🔍 Zoom completed in ${zoomTime.toFixed(2)}ms`);
            }
        });
    }
    
    diagnosePerformanceIssues() {
        console.log('🔍 Diagnosing performance issues...');
        
        const layerCount = this.map.layers.length;
        const visibleLayers = this.map.layers.items.filter(layer => layer.isVisible).length;
        
        console.log('📊 Performance Diagnosis:', {
            totalLayers: layerCount,
            visibleLayers: visibleLayers,
            currentZoom: this.map.zoomLevel.toFixed(2),
            mapSize: `${this.map.size.width}x${this.map.size.height}`
        });
        
        // Check for common performance issues
        if (layerCount > 10) {
            console.warn('⚠️  High layer count may impact performance');
        }
        
        if (visibleLayers > 5) {
            console.warn('⚠️  Many visible layers may impact rendering');
        }
        
        // Check for memory leaks
        if (this.metrics.memoryUsage.length > 10) {
            const recent = this.metrics.memoryUsage.slice(-10);
            const trend = recent[recent.length - 1].used - recent[0].used;
            
            if (trend > 10 * 1024 * 1024) { // 10MB increase
                console.warn('⚠️  Potential memory leak detected');
            }
        }
    }
    
    start() {
        this.isMonitoring = true;
        console.log('▶️  Performance monitoring started');
    }
    
    stop() {
        this.isMonitoring = false;
        console.log('⏹️  Performance monitoring stopped');
    }
    
    getReport() {
        const avgFps = this.metrics.frameRate.length > 0 
            ? this.metrics.frameRate.reduce((sum, m) => sum + m.fps, 0) / this.metrics.frameRate.length
            : 0;
        
        const avgLoadTime = this.metrics.layerLoadTimes.size > 0
            ? Array.from(this.metrics.layerLoadTimes.values()).reduce((sum, time) => sum + time, 0) / this.metrics.layerLoadTimes.size
            : 0;
        
        const currentMemory = this.metrics.memoryUsage.length > 0
            ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
            : null;
        
        return {
            averageFPS: avgFps.toFixed(1),
            averageLayerLoadTime: avgLoadTime.toFixed(2) + 'ms',
            currentMemoryUsage: currentMemory 
                ? `${(currentMemory.used / 1024 / 1024).toFixed(1)}MB`
                : 'Unknown',
            totalLayers: this.map.layers.length,
            visibleLayers: this.map.layers.items.filter(layer => layer.isVisible).length
        };
    }
}

// Usage
const performanceMonitor = new PangeaPerformanceMonitor(map);
performanceMonitor.start();

// Log performance report every 30 seconds
setInterval(() => {
    const report = performanceMonitor.getReport();
    console.log('📊 Performance Report:', report);
}, 30000);
```

## Memory Management and Cleanup

```typescript
console.log('🧹 Setting up memory management and cleanup...');

// Memory management utilities
class PangeaMemoryManager {
    constructor(map) {
        this.map = map;
        this.disposables = new Set();
        this.eventListeners = new Map();
        this.timers = new Set();
        
        this.setupCleanupHandlers();
    }
    
    setupCleanupHandlers() {
        // Clean up when page unloads
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // Clean up when map is destroyed
        if (this.map.disposed) {
            this.map.disposed.add(() => {
                this.cleanup();
            });
        }
    }
    
    // Register disposable resources
    registerDisposable(resource) {
        if (resource && typeof resource.dispose === 'function') {
            this.disposables.add(resource);
            console.log(`📝 Registered disposable resource: ${resource.constructor.name}`);
        }
    }
    
    // Register event listeners for cleanup
    registerEventListener(target, event, handler) {
        if (!this.eventListeners.has(target)) {
            this.eventListeners.set(target, []);
        }
        
        this.eventListeners.get(target).push({ event, handler });
        target.addEventListener(event, handler);
        
        console.log(`📝 Registered event listener: ${event} on ${target.constructor.name}`);
    }
    
    // Register timers for cleanup
    registerTimer(timerId) {
        this.timers.add(timerId);
        console.log(`📝 Registered timer: ${timerId}`);
    }
    
    // Clean up all resources
    cleanup() {
        console.log('🧹 Starting memory cleanup...');
        
        // Dispose of registered resources
        this.disposables.forEach(resource => {
            try {
                resource.dispose();
                console.log(`✅ Disposed: ${resource.constructor.name}`);
            } catch (error) {
                console.error(`❌ Error disposing resource:`, error);
            }
        });
        this.disposables.clear();
        
        // Remove event listeners
        this.eventListeners.forEach((listeners, target) => {
            listeners.forEach(({ event, handler }) => {
                try {
                    target.removeEventListener(event, handler);
                    console.log(`✅ Removed event listener: ${event}`);
                } catch (error) {
                    console.error(`❌ Error removing event listener:`, error);
                }
            });
        });
        this.eventListeners.clear();
        
        // Clear timers
        this.timers.forEach(timerId => {
            try {
                clearTimeout(timerId);
                clearInterval(timerId);
                console.log(`✅ Cleared timer: ${timerId}`);
            } catch (error) {
                console.error(`❌ Error clearing timer:`, error);
            }
        });
        this.timers.clear();
        
        console.log('✅ Memory cleanup completed');
    }
    
    // Force garbage collection (if available)
    forceGarbageCollection() {
        if (window.gc) {
            console.log('🗑️  Forcing garbage collection...');
            window.gc();
            console.log('✅ Garbage collection completed');
        } else {
            console.warn('⚠️  Garbage collection not available (requires --expose-gc flag)');
        }
    }
}

// Usage
const memoryManager = new PangeaMemoryManager(map);

// Example: Register resources for cleanup
memoryManager.registerDisposable(someLayer);
memoryManager.registerEventListener(window, 'resize', handleResize);
const timerId = setInterval(updateData, 1000);
memoryManager.registerTimer(timerId);
```

## Debugging Tools and Techniques

```typescript
console.log('🔧 Setting up debugging tools...');

// Comprehensive debugging utility
class PangeaDebugger {
    constructor(map) {
        this.map = map;
        this.isEnabled = false;
        this.logLevel = 'info'; // 'debug', 'info', 'warn', 'error'
        this.eventHistory = [];
        this.maxHistorySize = 1000;
        
        this.setupDebugTools();
    }
    
    enable() {
        this.isEnabled = true;
        console.log('🔍 Pangea debugging enabled');
        this.attachDebugListeners();
        this.createDebugPanel();
    }
    
    disable() {
        this.isEnabled = false;
        console.log('🔍 Pangea debugging disabled');
        this.removeDebugPanel();
    }
    
    setupDebugTools() {
        // Add debug methods to window for console access
        window.pangeaDebug = {
            map: this.map,
            debugger: this,
            inspectLayer: (layerId) => this.inspectLayer(layerId),
            inspectSource: (sourceId) => this.inspectSource(sourceId),
            getPerformanceReport: () => this.getPerformanceReport(),
            dumpState: () => this.dumpMapState(),
            clearHistory: () => this.clearEventHistory()
        };
        
        console.log('🔧 Debug tools available at window.pangeaDebug');
    }
    
    attachDebugListeners() {
        console.log('🎧 Attaching debug event listeners...');
        
        // Map events
        this.map.loaded.add(() => this.logEvent('map', 'loaded'));
        this.map.moveStarted.add(() => this.logEvent('map', 'moveStarted'));
        this.map.moveEnded.add(() => this.logEvent('map', 'moveEnded'));
        this.map.zoomStarted.add(() => this.logEvent('map', 'zoomStarted'));
        this.map.zoomEnded.add(() => this.logEvent('map', 'zoomEnded'));
        
        // Layer events
        this.map.layers.added.add((event) => {
            const layer = event.items[0].item;
            this.logEvent('layers', 'added', { layerId: layer.id, type: layer.constructor.name });
            this.attachLayerDebugListeners(layer);
        });
        
        this.map.layers.removed.add((event) => {
            const layer = event.items[0].item;
            this.logEvent('layers', 'removed', { layerId: layer.id });
        });
        
        // Mouse events
        this.map.mouse.click.add((event) => {
            this.logEvent('mouse', 'click', {
                screenPoint: event.screenPoint,
                geoPoint: [event.geoPoint.longitude, event.geoPoint.latitude]
            });
        });
        
        this.map.mouse.move.add((event) => {
            // Only log every 10th move event to avoid spam
            if (Math.random() < 0.1) {
                this.logEvent('mouse', 'move', {
                    screenPoint: event.screenPoint,
                    geoPoint: [event.geoPoint.longitude, event.geoPoint.latitude]
                });
            }
        });
    }
    
    attachLayerDebugListeners(layer) {
        layer.loading.add(() => this.logEvent('layer', 'loading', { layerId: layer.id }));
        layer.loaded.add(() => this.logEvent('layer', 'loaded', { layerId: layer.id }));
        layer.shown.add(() => this.logEvent('layer', 'shown', { layerId: layer.id }));
        layer.hidden.add(() => this.logEvent('layer', 'hidden', { layerId: layer.id }));
        
        layer.changed.add((changes) => {
            this.logEvent('layer', 'changed', { 
                layerId: layer.id, 
                changes: Object.keys(changes) 
            });
        });
    }
    
    logEvent(category, event, data = {}) {
        if (!this.isEnabled) return;
        
        const eventRecord = {
            timestamp: new Date().toISOString(),
            category,
            event,
            data
        };
        
        this.eventHistory.push(eventRecord);
        
        // Trim history if too large
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
        
        // Log to console based on level
        const message = `🔍 [${category}] ${event}`;
        if (data && Object.keys(data).length > 0) {
            console.log(message, data);
        } else {
            console.log(message);
        }
    }
    
    inspectLayer(layerId) {
        const layer = this.map.layers.find(layerId);
        if (!layer) {
            console.error(`❌ Layer not found: ${layerId}`);
            return null;
        }
        
        const inspection = {
            id: layer.id,
            type: layer.constructor.name,
            isVisible: layer.isVisible,
            isHidden: layer.isHidden,
            isLoading: layer.isLoading,
            opacity: layer.opacity,
            effectiveOpacity: layer.effectiveOpacity,
            slot: layer.slot,
            zoomRange: layer.zoomRange,
            timeRange: layer.timeRange
        };
        
        console.log(`🔍 Layer Inspection: ${layerId}`, inspection);
        return inspection;
    }
    
    inspectSource(sourceId) {
        const source = this.map.sources.find(sourceId);
        if (!source) {
            console.error(`❌ Source not found: ${sourceId}`);
            return null;
        }
        
        const inspection = {
            id: source.id,
            type: source.constructor.name,
            isLoading: source.isLoading || false,
            // Add more source-specific properties as needed
        };
        
        console.log(`🔍 Source Inspection: ${sourceId}`, inspection);
        return inspection;
    }
    
    dumpMapState() {
        const state = {
            map: {
                center: [this.map.geoCenter.longitude, this.map.geoCenter.latitude],
                zoom: this.map.zoomLevel,
                bearing: this.map.bearing,
                pitch: this.map.pitch,
                size: this.map.size,
                projection: this.map.projection
            },
            layers: this.map.layers.items.map(layer => ({
                id: layer.id,
                type: layer.constructor.name,
                isVisible: layer.isVisible,
                opacity: layer.opacity,
                slot: layer.slot
            })),
            sources: this.map.sources.items.map(source => ({
                id: source.id,
                type: source.constructor.name
            })),
            animator: {
                isPlaying: this.map.animator.isPlaying,
                currentFrame: this.map.animator.playhead.frame,
                totalFrames: this.map.animator.length,
                currentTime: this.map.animator.time.toISOString()
            }
        };
        
        console.log('🗺️  Map State Dump:', state);
        return state;
    }
    
    getPerformanceReport() {
        // This would integrate with the performance monitor
        const report = {
            timestamp: new Date().toISOString(),
            layerCount: this.map.layers.length,
            visibleLayers: this.map.layers.items.filter(layer => layer.isVisible).length,
            mapSize: this.map.size,
            currentZoom: this.map.zoomLevel
        };
        
        console.log('📊 Performance Report:', report);
        return report;
    }
    
    clearEventHistory() {
        this.eventHistory = [];
        console.log('🧹 Event history cleared');
    }
    
    createDebugPanel() {
        // Create a simple debug panel
        const panel = document.createElement('div');
        panel.id = 'pangea-debug-panel';
        panel.style.cssText = `
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        `;
        
        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">🔍 Pangea Debug Panel</div>
            <div id="debug-content">
                <div>Layers: ${this.map.layers.length}</div>
                <div>Zoom: ${this.map.zoomLevel.toFixed(2)}</div>
                <div>Center: ${this.map.geoCenter.latitude.toFixed(4)}, ${this.map.geoCenter.longitude.toFixed(4)}</div>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="window.pangeaDebug.dumpState()" style="margin-right: 5px; padding: 2px 6px; font-size: 10px;">Dump State</button>
                <button onclick="window.pangeaDebug.clearHistory()" style="padding: 2px 6px; font-size: 10px;">Clear History</button>
            </div>
        `;
        
        document.body.appendChild(panel);
        
        // Update panel periodically
        this.debugPanelInterval = setInterval(() => {
            const content = document.getElementById('debug-content');
            if (content) {
                content.innerHTML = `
                    <div>Layers: ${this.map.layers.length}</div>
                    <div>Zoom: ${this.map.zoomLevel.toFixed(2)}</div>
                    <div>Center: ${this.map.geoCenter.latitude.toFixed(4)}, ${this.map.geoCenter.longitude.toFixed(4)}</div>
                    <div>Events: ${this.eventHistory.length}</div>
                `;
            }
        }, 1000);
    }
    
    removeDebugPanel() {
        const panel = document.getElementById('pangea-debug-panel');
        if (panel) {
            panel.remove();
        }
        
        if (this.debugPanelInterval) {
            clearInterval(this.debugPanelInterval);
        }
    }
}

// Usage
const debugger = new PangeaDebugger(map);

// Enable debugging in development
if (process.env.NODE_ENV === 'development') {
    debugger.enable();
}

// Or enable manually
// debugger.enable();
```

## Complete Working Example

```typescript
import pangea from '@twc/pangea-web/mapbox';

const MAPBOX_ACCESS_TOKEN = 'your-mapbox-access-token-here';

async function createRobustMapApplication() {
    console.log('🚀 Creating robust map application with comprehensive error handling...');
    
    let map;
    let performanceMonitor;
    let memoryManager;
    let debugger;
    
    try {
        // Create map with retry logic
        map = await createMapWithRetry(3);
        
        // Set up monitoring and debugging
        performanceMonitor = new PangeaPerformanceMonitor(map);
        memoryManager = new PangeaMemoryManager(map);
        debugger = new PangeaDebugger(map);
        
        // Start monitoring
        performanceMonitor.start();
        
        // Enable debugging in development
        if (window.location.hostname === 'localhost') {
            debugger.enable();
        }
        
        // Add layers with error handling
        await addLayersWithErrorHandling(map);
        
        // Set up global error handlers
        setupGlobalErrorHandlers();
        
        console.log('✅ Robust map application created successfully');
        
        return {
            map,
            performanceMonitor,
            memoryManager,
            debugger
        };
        
    } catch (error) {
        console.error('💥 Failed to create map application:', error);
        
        // Show fallback UI
        showFallbackUI(error);
        
        throw error;
    }
}

async function addLayersWithErrorHandling(map) {
    console.log('🗂️  Adding layers with comprehensive error handling...');
    
    const layerConfigs = [
        {
            id: 'satellite-layer',
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            type: 'raster'
        },
        {
            id: 'cities-layer',
            url: 'https://example.com/api/cities.geojson',
            type: 'geojson'
        }
    ];
    
    const results = await Promise.allSettled(
        layerConfigs.map(config => addSingleLayerWithErrorHandling(map, config))
    );
    
    // Log results
    results.forEach((result, index) => {
        const config = layerConfigs[index];
        if (result.status === 'fulfilled') {
            console.log(`✅ Layer ${config.id} added successfully`);
        } else {
            console.error(`❌ Layer ${config.id} failed:`, result.reason.message);
        }
    });
    
    const successfulLayers = results.filter(r => r.status === 'fulfilled').length;
    console.log(`📊 Layer loading summary: ${successfulLayers}/${layerConfigs.length} successful`);
}

async function addSingleLayerWithErrorHandling(map, config) {
    try {
        if (config.type === 'geojson') {
            return await loadGeoJSONLayer(config.url, {
                id: config.id,
                slot: pangea.layers.LayerSlot.MIDDLE
            });
        } else if (config.type === 'raster') {
            // Add raster layer logic here
            console.log(`🖼️  Adding raster layer: ${config.id}`);
        }
    } catch (error) {
        console.error(`💥 Failed to add layer ${config.id}:`, error);
        throw error;
    }
}

function setupGlobalErrorHandlers() {
    console.log('🌐 Setting up global error handlers...');
    
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        console.error('🚨 Unhandled promise rejection:', event.reason);
        
        // Prevent default browser behavior
        event.preventDefault();
        
        // Show user notification
        showUserError('An unexpected error occurred. The application will continue to function.');
    });
    
    // Handle general JavaScript errors
    window.addEventListener('error', (event) => {
        console.error('🚨 JavaScript error:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        });
        
        // Don't show user notification for every JS error
        // Only for critical ones that affect map functionality
    });
    
    console.log('✅ Global error handlers configured');
}

function showFallbackUI(error) {
    console.log('🔄 Showing fallback UI...');
    
    const fallbackDiv = document.createElement('div');
    fallbackDiv.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-family: Arial, sans-serif;
        text-align: center;
        padding: 20px;
    `;
    
    fallbackDiv.innerHTML = `
        <div style="max-width: 400px;">
            <h2 style="color: #666; margin-bottom: 20px;">🗺️ Map Unavailable</h2>
            <p style="color: #888; margin-bottom: 20px;">
                We're having trouble loading the map. This could be due to:
            </p>
            <ul style="text-align: left; color: #888; margin-bottom: 20px;">
                <li>Network connectivity issues</li>
                <li>Browser compatibility problems</li>
                <li>Service temporarily unavailable</li>
            </ul>
            <button onclick="location.reload()" style="
                background: #007cbf;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            ">
                Try Again
            </button>
        </div>
    `;
    
    // Replace map container with fallback
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        mapContainer.appendChild(fallbackDiv);
    }
}

// Initialize the application
createRobustMapApplication()
    .then((app) => {
        console.log('🎉 Map application ready!');
        
        // Make app available globally for debugging
        window.mapApp = app;
    })
    .catch((error) => {
        console.error('💥 Map application failed to initialize:', error);
    });
```

## Summary

This comprehensive error handling and debugging section provides:

### Error Handling Strategies:
- **Specific Error Types**: Understanding and handling Pangea's built-in error types
- **Map Creation Errors**: Robust map initialization with retry logic
- **Layer Errors**: Comprehensive layer loading and runtime error handling
- **Network Errors**: Resilient data fetching with exponential backoff
- **Graceful Degradation**: Fallback strategies when components fail

### Performance Monitoring:
- **Frame Rate Monitoring**: Real-time FPS tracking and performance warnings
- **Memory Usage Tracking**: Memory leak detection and usage monitoring
- **Layer Performance**: Load time tracking and optimization suggestions
- **Event Timing**: Map operation performance measurement

### Debugging Tools:
- **Debug Panel**: Visual debugging interface with real-time information
- **Event History**: Comprehensive event logging and inspection
- **State Inspection**: Tools to examine map, layer, and source states
- **Console Integration**: Debug utilities accessible from browser console

### Memory Management:
- **Resource Cleanup**: Automatic disposal of resources and event listeners
- **Memory Leak Prevention**: Proactive memory management strategies
- **Timer Management**: Proper cleanup of intervals and timeouts

### Best Practices:
- **Comprehensive Logging**: Beautiful, informative console output for debugging
- **User-Friendly Errors**: Meaningful error messages for end users
- **Fallback Strategies**: Graceful degradation when features fail
- **Development vs Production**: Different error handling strategies for different environments

This section ensures that AI agents can build robust, production-ready applications with the Pangea Web SDK that handle errors gracefully and provide excellent debugging capabilities.
