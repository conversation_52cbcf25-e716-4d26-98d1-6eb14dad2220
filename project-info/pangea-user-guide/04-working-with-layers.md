# 4. Working with Layers

Layers are fundamental to displaying data on your Pangea map. They represent different types of geographical information, such as satellite imagery, vector features, heatmaps, or animated particles. Pangea provides a variety of layer types, each with its own specific configuration and purpose.

## Core Layer Concepts

All layers in Pangea share a set of common properties and behaviors, defined by the base `Layer` class.

### Common Layer Options (`LayerOptions`)

When creating a layer, you'll typically pass an options object. Common options include:

*   `id`: (Optional) A unique identifier for the layer. If not provided, Pangea generates one.
*   `opacity`: A number between 0 (fully transparent) and 1 (fully opaque). Default is `1`.
*   `zoomRange`: A `NumberRangeLike` object (e.g., `{ start: 5, end: 12 }` or `[5, 12]`) defining the zoom levels at which the layer is visible.
*   `timeRange`: A `TimeRangeLike` object defining the time window during which the layer is visible, used with the map's animator.
*   `fadeIn`: A `NumberRangeLike` object defining a zoom range over which the layer fades in.
*   `fadeOut`: A `NumberRangeLike` object defining a zoom range over which the layer fades out.
*   `slot`: A `pangea.layers.LayerSlot` enum (`TOP`, `MIDDLE`, `BOTTOM`) specifying the rendering order group. Default is `pangea.layers.LayerSlot.TOP`.
*   `slidingEnabled`: Boolean, if true, the `timeRange` slides with the system clock when the animator is not playing. Default is `true`.
*   `tweeningEnabled`: Boolean, if true, the layer responds to animator tweened events. Default is `false`.
*   `meta`: An arbitrary object for storing custom metadata associated with the layer.

### Key Layer Properties

Once a layer is created, you can access and sometimes modify its properties:

*   `layer.id`: The unique ID of the layer.
*   `layer.opacity`: Get or set the layer's opacity.
*   `layer.isVisible`: Read-only boolean indicating if the layer is currently visible (considering opacity, zoom, time, `isHidden` status, etc.).
*   `layer.isHidden`: Read-only boolean indicating if `hide()` has been called and the layer is in a hidden state.
*   `layer.isLoading`: Read-only boolean indicating if the layer is currently loading data.
*   `layer.isAnimate`: Boolean indicating if the layer animates with the map's `Animator`. Default `true`.
*   `layer.zoomRange`: Get or set the visibility zoom range.
*   `layer.timeRange`: Get or set the visibility time range.
*   `layer.slot`: Get or set the layer's rendering slot (`pangea.layers.LayerSlot`).
*   `layer.effectiveOpacity`: Read-only number indicating the current computed opacity of the layer.

### Key Layer Methods

*   `layer.show(duration?: TimeSpanLike)`: Makes the layer visible, optionally with an animated fade-in. Returns a Promise.
*   `layer.hide(duration?: TimeSpanLike)`: Hides the layer, optionally with an animated fade-out. Returns a Promise.

## Adding Layers to the Map

You add layers to a `MapViewport` instance using the `addLayer()` method.

```typescript
import { HttpRasterSource, RasterLayer, LayerSlot } from '@twc/pangea-web/mapbox';

// Assuming 'map' is your Pangea MapboxViewport instance
console.log('🗺️  Adding layers to the map...');

// Example: Adding a RasterLayer using HttpRasterSource for OpenStreetMap tiles.

// 1. Create the source directly using the SDK classes
const osmSource = new HttpRasterSource(
    'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    {
        id: 'osm-source',
        subdomains: ['a', 'b', 'c'],
        tileSize: 256,
        attribution: '© OpenStreetMap contributors'
    }
);

// 2. Create the layer using the source
const osmLayer = new RasterLayer(
    osmSource,
    {
        id: 'osm-layer',
        opacity: 0.8,
        zoomRange: [0, 18],
        slot: LayerSlot.BOTTOM // Place as basemap
    }
);

// 3. Add the layer to the map
try {
    map.layers.add(osmLayer);
    console.log('✅ OSM layer added successfully:', osmLayer.id);
    
    // Set up layer event listeners with beautiful logging
    osmLayer.loading.add(() => {
        console.log('🔄 OSM layer is loading tiles...');
    });
    
    osmLayer.loaded.add(() => {
        console.log('✨ OSM layer finished loading tiles');
    });
    
    osmLayer.shown.add(() => {
        console.log('👁️  OSM layer is now visible');
    });
    
    osmLayer.hidden.add(() => {
        console.log('🙈 OSM layer is now hidden');
    });
    
    osmLayer.changed.add((changes: any) => {
        console.log('🔧 OSM layer properties changed:', changes);
    });
    
} catch (error: any) {
    console.error('💥 Error adding OSM layer:', error);
}
```

## Removing Layers

```typescript
console.log('🗑️  Removing layers from the map...');

// Remove a layer by its ID
const removed: boolean = map.layers.remove('osm-layer');
if (removed) {
    console.log('✅ Layer removed successfully');
} else {
    console.log('⚠️  Layer not found or already removed');
}

// Or remove by the layer instance itself (if you have a reference)
// map.layers.remove(osmLayer);

// To remove all layers:
const layerCount: number = map.layers.length;
map.layers.clear();
console.log(`🧹 Cleared all ${layerCount} layers from the map`);
```

## Managing Layer Order (Slots)

Pangea uses `LayerSlot`s to manage the general rendering order of layers:

*   **`LayerSlot.BOTTOM`**: For layers that should appear underneath most other content (e.g., base satellite imagery, foundational rasters).
*   **`LayerSlot.MIDDLE`**: For layers that sit in between (e.g., thematic data layers, polygons).
*   **`LayerSlot.TOP`**: For layers that should appear on top of most other content.

```typescript
import { LayerSlot, RasterLayer, FeatureLayer, OverlayLayer } from '@twc/pangea-web/mapbox';

console.log('📚 Demonstrating layer slots and ordering...');

// Create layers for different slots using proper TypeScript classes
// Note: You would need to create the corresponding sources first

// Example of changing a layer's slot dynamically
// if (dataLayer) {
//     console.log('🔄 Moving data layer to TOP slot...');
//     dataLayer.slot = LayerSlot.TOP;
//     console.log('✅ Layer slot changed');
// }

console.log('📋 Layer slots explained:');
console.log('  BOTTOM: Base imagery, foundational layers');
console.log('  MIDDLE: Data layers, thematic content');
console.log('  TOP: Overlays, labels, interactive elements');
```

## Common Layer Types

### RasterLayer - Displaying Tile-Based Imagery

```javascript
console.log('🖼️  Creating a RasterLayer for satellite imagery...');

// Define a satellite imagery source
const satelliteSourceOptions = {
    type: 'HttpRasterSource',
    options: {
        id: 'satellite-source',
        urlTemplate: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        tileSize: 256,
        attribution: 'Esri, Maxar, GeoEye, Earthstar Geographics'
    }
};

const satelliteLayerOptions = {
    type: 'RasterLayer',
    options: {
        id: 'satellite-layer',
        source: 'satellite-source',
        opacity: 0.9,
        zoomRange: [0, 18],
        slot: pangea.layers.LayerSlot.BOTTOM
    }
};

try {
    const satelliteLayer = map.addLayer(satelliteLayerOptions, satelliteSourceOptions);
    
    console.log('🛰️  Satellite layer added successfully');
    
    // Set up detailed event logging
    satelliteLayer.loading.add(() => {
        console.log('📡 Satellite imagery loading...');
    });
    
    satelliteLayer.loaded.add(() => {
        console.log('✨ Satellite imagery loaded successfully');
    });
    
} catch (error) {
    console.error('❌ Error adding satellite layer:', error);
}
```

### FeatureLayer - Displaying Vector Data

```javascript
console.log('📍 Creating a FeatureLayer for GeoJSON data...');

// 1. Create a GeoJSON source with sample data
const geoJsonSourceOptions = {
    type: 'GeoJSONFeatureSource',
    options: {
        id: 'sample-features',
        data: {
            type: 'FeatureCollection',
            features: [
                {
                    type: 'Feature',
                    geometry: { type: 'Point', coordinates: [-74.00, 40.70] },
                    properties: { name: 'New York', type: 'city', population: 8000000 }
                },
                {
                    type: 'Feature',
                    geometry: { type: 'Point', coordinates: [-122.42, 37.77] },
                    properties: { name: 'San Francisco', type: 'city', population: 875000 }
                },
                {
                    type: 'Feature',
                    geometry: {
                        type: 'LineString',
                        coordinates: [[-74.00, 40.70], [-122.42, 37.77]]
                    },
                    properties: { name: 'Flight Path', type: 'route' }
                }
            ]
        }
    }
};

// 2. Create the FeatureLayer with styling
const featureLayerOptions = {
    type: 'FeatureLayer',
    options: {
        id: 'sample-features-layer',
        source: 'sample-features',
        slot: pangea.layers.LayerSlot.MIDDLE,
        
        // Style function to convert features to overlays
        styleFeature: (feature) => {
            const overlays = [];
            const props = feature.properties;
            
            if (feature.geometry instanceof pangea.geography.GeoPoint) {
                // Style point features as circles
                let radius = 8;
                let color = '#007cbf';
                
                if (props.type === 'city') {
                    radius = Math.max(6, Math.min(20, props.population / 500000));
                    color = props.population > 1000000 ? '#ff4444' : '#4444ff';
                }
                
                overlays.push(new pangea.overlays.CircleMarker(
                    radius,
                    feature.geometry,
                    {
                        fill: { color: color, opacity: 0.8 },
                        stroke: { color: '#ffffff', width: 2 }
                    },
                    { id: `circle-${feature.id}` }
                ));
                
                // Add text label
                if (props.name) {
                    overlays.push(new pangea.overlays.TextMarker(
                        props.name,
                        feature.geometry,
                        {
                            color: pangea.visuals.Color.BLACK,
                            size: 12,
                            halo: { color: pangea.visuals.Color.WHITE, width: 2 },
                            offset: [0, -radius - 8]
                        },
                        { id: `text-${feature.id}` }
                    ));
                }
            } else if (feature.geometry instanceof pangea.geography.GeoLine) {
                // Style line features
                overlays.push(new pangea.overlays.LinePath(
                    feature.geometry,
                    {
                        stroke: { color: '#ff8800', width: 3, opacity: 0.8 }
                    },
                    { id: `line-${feature.id}` }
                ));
            }
            
            return overlays;
        }
    }
};

try {
    const featureLayer = map.addLayer(featureLayerOptions, geoJsonSourceOptions);
    
    console.log('🎯 Feature layer added successfully');
    
    // Set up event logging
    featureLayer.loading.add(() => {
        console.log('📊 Feature data loading...');
    });
    
    featureLayer.loaded.add(() => {
        console.log('✨ Feature data loaded and styled');
        console.log(`📈 Features rendered: ${featureLayer.features?.length || 'unknown'}`);
    });
    
    // Log when features are clicked
    map.mouse.click.add((mouseEvent) => {
        const clickedFeatures = featureLayer.inspect(mouseEvent.screenPoint);
        if (clickedFeatures && clickedFeatures.length > 0) {
            const overlay = clickedFeatures[0];
            const feature = overlay.feature;
            console.log('🎯 Feature clicked:', {
                name: feature.properties.name,
                type: feature.properties.type,
                geometry: feature.geometry.constructor.name
            });
        }
    });
    
} catch (error) {
    console.error('❌ Error adding feature layer:', error);
}
```

### HeatmapLayer - Visualizing Point Density

```javascript
console.log('🔥 Creating a HeatmapLayer for point density visualization...');

// Create sample point data for heatmap
const heatmapSourceOptions = {
    type: 'StaticFeatureSource',
    options: {
        id: 'heatmap-points',
        features: [
            // Generate sample points around New York
            ...Array.from({ length: 100 }, (_, i) => ({
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [
                        -74.0 + (Math.random() - 0.5) * 0.1,
                        40.7 + (Math.random() - 0.5) * 0.1
                    ]
                },
                properties: { intensity: Math.random() * 100 }
            }))
        ]
    }
};

const heatmapLayerOptions = {
    type: 'HeatmapLayer',
    options: {
        id: 'density-heatmap',
        source: 'heatmap-points',
        slot: pangea.layers.LayerSlot.MIDDLE,
        radius: 20,
        intensity: 1.0,
        palette: pangea.visuals.Palette.fromColors([
            '#0000ff', '#00ffff', '#00ff00', '#ffff00', '#ff0000'
        ]),
        convertFeature: (feature) => {
            return feature.properties.intensity || 1;
        }
    }
};

try {
    const heatmapLayer = map.addLayer(heatmapLayerOptions, heatmapSourceOptions);
    
    console.log('🔥 Heatmap layer added successfully');
    
    heatmapLayer.loaded.add(() => {
        console.log('✨ Heatmap visualization rendered');
        console.log('🎨 Color palette: Blue → Cyan → Green → Yellow → Red');
    });
    
} catch (error) {
    console.error('❌ Error adding heatmap layer:', error);
}
```

### OverlayLayer - Manual Graphics Management

```javascript
console.log('🎨 Creating an OverlayLayer for manual graphics...');

// Create overlays manually
const overlays = [
    new pangea.overlays.ImageMarker(
        [-74.006, 40.7128], // New York
        {
            url: 'https://example.com/marker-icon.png',
            size: [32, 32],
            anchor: pangea.visuals.Anchor.BOTTOM_CENTER
        },
        { id: 'nyc-marker' }
    ),
    
    new pangea.overlays.CircleMarker(
        15,
        [-122.4194, 37.7749], // San Francisco
        {
            fill: { color: '#ff4444', opacity: 0.7 },
            stroke: { color: '#ffffff', width: 2 }
        },
        { id: 'sf-circle' }
    ),
    
    new pangea.overlays.TextMarker(
        'Hello World!',
        [0, 0], // Null Island
        {
            color: pangea.visuals.Color.RED,
            size: 16,
            halo: { color: pangea.visuals.Color.WHITE, width: 2 }
        },
        { id: 'hello-text' }
    )
];

// Create source with overlays
const overlaySourceOptions = {
    type: 'StaticOverlaySource',
    options: {
        id: 'manual-overlays',
        overlays: overlays
    }
};

const overlayLayerOptions = {
    type: 'OverlayLayer',
    options: {
        id: 'manual-overlay-layer',
        source: 'manual-overlays',
        slot: pangea.layers.LayerSlot.TOP
    }
};

try {
    const overlayLayer = map.addLayer(overlayLayerOptions, overlaySourceOptions);
    
    console.log('🎨 Overlay layer added successfully');
    console.log(`📍 Added ${overlays.length} manual overlays`);
    
    overlayLayer.loaded.add(() => {
        console.log('✨ Manual overlays rendered');
    });
    
    // Demonstrate adding overlays dynamically
    setTimeout(() => {
        const source = map.sources.find('manual-overlays');
        if (source) {
            const newOverlay = new pangea.overlays.CircleMarker(
                10,
                [2.3522, 48.8566], // Paris
                {
                    fill: { color: '#00ff00', opacity: 0.8 },
                    stroke: { color: '#000000', width: 1 }
                },
                { id: 'paris-circle' }
            );
            
            source.add(newOverlay);
            console.log('➕ Added new overlay to Paris');
        }
    }, 3000);
    
} catch (error) {
    console.error('❌ Error adding overlay layer:', error);
}
```

## Layer Events and Interaction

```javascript
console.log('🎧 Setting up comprehensive layer event handling...');

// Function to set up events for any layer
function setupLayerEvents(layer, layerName) {
    console.log(`🔧 Setting up events for ${layerName}...`);
    
    layer.loading.add(() => {
        console.log(`⏳ ${layerName} started loading`);
    });
    
    layer.loaded.add(() => {
        console.log(`✅ ${layerName} finished loading`);
    });
    
    layer.shown.add(() => {
        console.log(`👁️  ${layerName} is now visible`);
    });
    
    layer.hidden.add(() => {
        console.log(`🙈 ${layerName} is now hidden`);
    });
    
    layer.changed.add((changes) => {
        console.log(`🔧 ${layerName} properties changed:`, Object.keys(changes));
    });
}

// Example: Set up events for all layers
map.layers.added.add((event) => {
    const layer = event.items[0].item;
    setupLayerEvents(layer, layer.id);
    console.log(`📚 Layer added to map: ${layer.id} (slot: ${layer.slot})`);
});

map.layers.removed.add((event) => {
    const layer = event.items[0].item;
    console.log(`🗑️  Layer removed from map: ${layer.id}`);
});

// Mouse interaction with layers
map.mouse.click.add((mouseEvent) => {
    console.log('🖱️  Mouse clicked, checking for layer interactions...');
    
    // Check each layer for interactions
    for (const layer of map.layers.items) {
        if (layer.inspect && layer.isVisible) {
            try {
                const results = layer.inspect(mouseEvent.screenPoint);
                if (results && results.length > 0) {
                    console.log(`🎯 Interaction found on layer: ${layer.id}`, {
                        resultCount: results.length,
                        layerType: layer.constructor.name
                    });
                }
            } catch (error) {
                // Some layers might not support inspection
            }
        }
    }
});
```

## Layer Visibility and Animation

```javascript
console.log('👁️  Demonstrating layer visibility and animation...');

// Function to demonstrate layer visibility controls
async function demonstrateLayerVisibility(layer) {
    if (!layer) return;
    
    console.log(`🎭 Starting visibility demo for layer: ${layer.id}`);
    
    // Current state
    console.log(`📊 Initial state: visible=${layer.isVisible}, opacity=${layer.opacity}`);
    
    // Hide with animation
    console.log('🙈 Hiding layer with 1-second fade...');
    await layer.hide(1000);
    console.log('✅ Layer hidden');
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Show with animation
    console.log('👁️  Showing layer with 1-second fade...');
    await layer.show(1000);
    console.log('✅ Layer shown');
    
    // Change opacity
    console.log('🔧 Changing opacity to 0.5...');
    layer.opacity = 0.5;
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Restore opacity
    console.log('🔧 Restoring opacity to 1.0...');
    layer.opacity = 1.0;
    
    console.log('🎉 Visibility demo completed');
}

// Example usage (uncomment when you have a layer):
// demonstrateLayerVisibility(someLayer);
```

## Complete Working Example

Here's a complete example that demonstrates multiple layer types:

```javascript
import pangea from '@twc/pangea-web/mapbox';

const MAPBOX_ACCESS_TOKEN = 'your-mapbox-access-token-here';

async function createLayeredMap() {
    try {
        console.log('🚀 Creating a map with multiple layers...');
        
        const map = await pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, {
            geoCenter: [-74.006, 40.7128],
            zoomLevel: 10,
            basemap: 'mapbox://styles/mapbox/light-v10'
        });
        
        console.log('✅ Base map created');
        
        // Wait for map to load
        await new Promise(resolve => map.loaded.add(resolve));
        
        // 1. Add a satellite base layer
        console.log('🛰️  Adding satellite base layer...');
        const satelliteLayer = map.addLayer({
            type: 'RasterLayer',
            options: {
                id: 'satellite-base',
                source: 'satellite-source',
                slot: pangea.layers.LayerSlot.BOTTOM,
                opacity: 0.7
            }
        }, {
            type: 'HttpRasterSource',
            options: {
                id: 'satellite-source',
                urlTemplate: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
                tileSize: 256
            }
        });
        
        // 2. Add feature data
        console.log('📍 Adding feature layer...');
        const featureLayer = map.addLayer({
            type: 'FeatureLayer',
            options: {
                id: 'poi-features',
                source: 'poi-source',
                slot: pangea.layers.LayerSlot.MIDDLE,
                styleFeature: (feature) => {
                    return [new pangea.overlays.CircleMarker(
                        8,
                        feature.geometry,
                        {
                            fill: { color: '#ff4444', opacity: 0.8 },
                            stroke: { color: '#ffffff', width: 2 }
                        }
                    )];
                }
            }
        }, {
            type: 'GeoJSONFeatureSource',
            options: {
                id: 'poi-source',
                data: {
                    type: 'FeatureCollection',
                    features: [
                        {
                            type: 'Feature',
                            geometry: { type: 'Point', coordinates: [-74.006, 40.7128] },
                            properties: { name: 'Times Square' }
                        }
                    ]
                }
            }
        });
        
        // 3. Add overlay graphics
        console.log('🎨 Adding overlay layer...');
        const overlayLayer = map.addLayer({
            type: 'OverlayLayer',
            options: {
                id: 'custom-overlays',
                source: 'overlay-source',
                slot: pangea.layers.LayerSlot.TOP
            }
        }, {
            type: 'StaticOverlaySource',
            options: {
                id: 'overlay-source',
                overlays: [
                    new pangea.overlays.TextMarker(
                        'New York City',
                        [-74.006, 40.7128],
                        {
                            color: pangea.visuals.Color.BLACK,
                            size: 16,
                            halo: { color: pangea.visuals.Color.WHITE, width: 2 }
                        }
                    )
                ]
            }
        });
        
        // Set up comprehensive logging
        [satelliteLayer, featureLayer, overlayLayer].forEach((layer, index) => {
            const layerNames = ['Satellite', 'Features', 'Overlays'];
            const layerName = layerNames[index];
            
            layer.loading.add(() => console.log(`⏳ ${layerName} loading...`));
            layer.loaded.add(() => console.log(`✅ ${layerName} loaded`));
        });
        
        // Demonstrate layer controls
        setTimeout(() => {
            console.log('🎭 Starting layer visibility demonstration...');
            
            // Toggle satellite layer
            setTimeout(() => {
                console.log('🙈 Hiding satellite layer...');
                satelliteLayer.hide(1000);
            }, 1000);
            
            setTimeout(() => {
                console.log('👁️  Showing satellite layer...');
                satelliteLayer.show(1000);
            }, 3000);
            
            // Change opacity
            setTimeout(() => {
                console.log('🔧 Reducing satellite opacity...');
                satelliteLayer.opacity = 0.3;
            }, 5000);
            
        }, 2000);
        
        console.log('🎉 Layered map setup complete!');
        console.log('📊 Layer summary:', {
            total: map.layers.length,
            bottom: map.layers.bottom.length,
            middle: map.layers.middle.length,
            top: map.layers.top.length
        });
        
    } catch (error) {
        console.error('❌ Error creating layered map:', error);
    }
}

// Run the example
createLayeredMap();
```

## Summary

Working with layers in Pangea involves:

1. **Understanding layer types**: RasterLayer, FeatureLayer, HeatmapLayer, OverlayLayer, etc.
2. **Managing sources**: Each layer needs a data source (HttpRasterSource, GeoJSONFeatureSource, etc.)
3. **Controlling visibility**: Using slots, opacity, show/hide methods, and zoom ranges
4. **Handling events**: Loading, loaded, shown, hidden, and changed events
5. **Styling data**: Converting features to visual overlays with custom styling

The comprehensive logging in these examples helps you understand exactly what's happening at each step, making it easier to debug and learn the layer system.
