# 2. Getting Started

This section will guide you through setting up the Pangea Web SDK in your project and creating your first interactive map.

## Installation

The Pangea Web SDK is available as an npm package. You can install it using npm or yarn:

```bash
npm install @twc/pangea-web
```

or

```bash
yarn add @twc/pangea-web
```

## Basic HTML Setup

Create a basic HTML file with a container element for your map:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Pangea Map</title>
    <style>
        #map {
            width: 100%;
            height: 500px;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="your-script.js"></script>
</body>
</html>
```

## Importing Pangea

### ES6 Modules (Recommended)

```typescript
import pangea from '@twc/pangea-web/mapbox';
```

### CommonJS

```typescript
const pangea = require('@twc/pangea-web/mapbox');
```

### Script Tag (Browser)

Include the Pangea Web SDK UMD bundle for Mapbox in your HTML. The SDK will be available as a global `pangea` variable.

```html
<!-- Replace with the actual path to your Pangea SDK file -->
<script src="path/to/pangea-mapbox-sdk.js"></script>
<script>
    // Pangea is now available as a global variable
    console.log('Pangea SDK loaded:', pangea);
</script>
```

## Creating Your First Map

### Basic Map with Mapbox Engine

Here's how to create a simple map using the Mapbox engine:

```typescript
import pangea from '@twc/pangea-web/mapbox';
import { MapboxViewportOptions } from '@twc/pangea-web/mapbox';

// Your Mapbox access token (required for Mapbox-based maps)
const MAPBOX_ACCESS_TOKEN = 'your-mapbox-access-token-here';

// Create map options with proper TypeScript typing
const mapOptions: MapboxViewportOptions = {
    geoCenter: [-74.006, 40.7128], // New York City [longitude, latitude]
    zoomLevel: 10,
    bearing: 0,
    pitch: 0,
    basemap: 'mapbox://styles/mapbox/standard'
};

// Create the map using Promise
pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, mapOptions)
    .then((map: pangea.mapbox.MapboxViewport) => {
        console.log('Map created successfully!', map);
        
        // Set up event listeners with beautiful logging
        map.loaded.add(() => {
            console.log('🗺️  Map has finished loading and is ready for interaction');
        });
        
        map.moveEnded.add(() => {
            const center: pangea.geography.GeoPoint = map.geoCenter;
            const zoom: number = map.zoomLevel;
            console.log(`📍 Map moved to: ${center.latitude.toFixed(4)}, ${center.longitude.toFixed(4)} at zoom ${zoom.toFixed(2)}`);
        });
        
        map.zoomEnded.add(() => {
            console.log(`🔍 Map zoom changed to level: ${map.zoomLevel.toFixed(2)}`);
        });
        
        // Map is ready for interaction
        // You can now add layers, markers, etc.
    })
    .catch((error: Error) => {
        console.error('❌ Error creating map:', error);
    });
```

### Using Callback Style

If you prefer callbacks over promises:

```typescript
import { MapCallback } from '@twc/pangea-web/mapbox';

const mapCallback: MapCallback<pangea.mapbox.MapboxViewport> = (map: pangea.mapbox.MapboxViewport) => {
    console.log('✅ Map created successfully with callback!', map);
    
    // Set up beautiful event logging
    map.loaded.add(() => {
        console.log('🎉 Map is fully loaded and interactive!');
    });
    
    map.moved.add((region: pangea.maps.MapRegion) => {
        console.log(`🌍 Map viewport changed:`, {
            center: `${region.geoCenter.latitude.toFixed(4)}, ${region.geoCenter.longitude.toFixed(4)}`,
            zoom: region.zoomLevel.toFixed(2),
            bearing: region.bearing.toFixed(1) + '°',
            pitch: region.pitch.toFixed(1) + '°'
        });
    });
    
    // Your map initialization code here
};

pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, mapOptions, mapCallback);
```

## Map Configuration Options

The `MapboxViewportOptions` interface provides many configuration options:

```typescript
import { MapboxViewportOptions, MapboxBehaviorsLike, MapboxAttributionLike } from '@twc/pangea-web/mapbox';

const advancedMapOptions: MapboxViewportOptions = {
    // Geographic settings
    geoCenter: [-74.006, 40.7128],
    zoomLevel: 12,
    bearing: 45,        // Rotation in degrees
    pitch: 30,          // Tilt in degrees (0-85)
    
    // Basemap
    basemap: 'mapbox://styles/mapbox/standard',
    
    // Zoom constraints
    zoomRange: [1, 20],
    pitchRange: [0, 60],
    
    // Behavior settings
    behaviors: {
        dragging: true,
        scrollZoom: true,
        doubleClickZoom: true,
        rotating: true,
        tilting: true,
        boxZoom: true,
        keyboardNavigation: true
    } as MapboxBehaviorsLike,
    
    // Timeouts (in milliseconds)
    slidingTimeout: 600000,    // 10 minutes of inactivity
    absoluteTimeout: 3600000,  // 1 hour maximum
    
    // Performance settings
    preserveDrawingBuffer: false,  // Set to true if you need to export map images
    
    // Attribution
    attribution: {
        content: 'Custom attribution text',
        isCompact: false,
        position: pangea.visuals.Position.BOTTOM_RIGHT 
    } as MapboxAttributionLike
};

pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, advancedMapOptions)
    .then((map: pangea.mapbox.MapboxViewport) => {
        console.log('🚀 Advanced map configuration loaded successfully!');
        
        // Log configuration details
        console.log('⚙️  Map Configuration:', {
            center: `${map.geoCenter.latitude.toFixed(4)}, ${map.geoCenter.longitude.toFixed(4)}`,
            zoom: `${map.zoomLevel} (range: ${map.zoomRange.start}-${map.zoomRange.end})`,
            pitch: `${map.pitch}° (range: ${map.pitchRange.start}-${map.pitchRange.end}°)`,
            bearing: `${map.bearing}°`,
            size: `${map.size.width}x${map.size.height}px`
        });
    });
```

## Understanding Map Engines

Pangea supports multiple map engines. Each engine has its own strengths:

### Mapbox Engine
- **Best for**: Modern web applications, vector tiles, smooth animations
- **Import**: `import pangea from '@twc/pangea-web/mapbox'`
- **Requires**: Mapbox access token

### Leaflet Engine
- **Best for**: Lightweight applications, simple tile layers
- **Import**: `import pangea from '@twc/pangea-web/leaflet'`
- **Requires**: No access token needed

### Cesium Engine
- **Best for**: 3D visualization, globe view, terrain
- **Import**: `import pangea from '@twc/pangea-web/cesium'`
- **Requires**: Cesium Ion access token (optional)

### ArcGIS Engine
- **Best for**: Enterprise GIS applications, ArcGIS integration
- **Import**: `import pangea from '@twc/pangea-web/arcgis'`
- **Requires**: ArcGIS API key (optional)

## Error Handling

Always include proper error handling when creating maps:

```typescript
async function createMap(): Promise<pangea.mapbox.MapboxViewport> {
    try {
        console.log('🔄 Initializing Pangea map...');
        
        const map: pangea.mapbox.MapboxViewport = await pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, mapOptions);
        
        // Set up comprehensive event listeners
        map.loaded.add(() => {
            console.log('✅ Map loaded successfully');
            console.log('📊 Map Stats:', {
                engine: 'Mapbox GL JS',
                version: map.version,
                container: map.element.id,
                projection: map.projection
            });
        });
        
        map.moveEnded.add(() => {
            const bounds: pangea.geography.GeoBounds = map.bounds;
            console.log('🗺️  Map bounds updated:', {
                north: bounds.north.toFixed(4),
                south: bounds.south.toFixed(4),
                east: bounds.east.toFixed(4),
                west: bounds.west.toFixed(4)
            });
        });
        
        map.timedOut.add((event: { source: string }) => {
            console.warn('⏰ Map timeout occurred:', event.source);
        });
        
        return map;
    } catch (error: any) {
        console.error('❌ Failed to create map:', error);
        
        // Handle specific error types
        if (error.message && error.message.includes('access token')) {
            console.error('🔑 Please check your Mapbox access token');
            alert('Please check your Mapbox access token');
        } else if (error.message && error.message.includes('container')) {
            console.error('📦 Map container element not found');
            alert('Map container element not found');
        } else {
            console.error('🚨 Unknown error occurred:', error.message || error);
        }
        
        throw error;
    }
}

// Usage
createMap().then((map: pangea.mapbox.MapboxViewport) => {
    console.log('🎯 Map ready for use!', map);
}).catch((error: Error) => {
    console.error('💥 Map creation failed:', error);
});
```

## Complete Working Example

Here's a complete working example that you can copy and run:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pangea Map Example</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #map {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        .info {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Pangea Web SDK - Getting Started</h1>
    <div id="map"></div>
    <div class="info">
        <p>Check the browser console for detailed logging of map events!</p>
    </div>

    <script type="module">
        import pangea from '@twc/pangea-web/mapbox';
        
        // Replace with your actual Mapbox access token
        const MAPBOX_ACCESS_TOKEN = 'pk.your-token-here';
        
        const mapOptions = {
            geoCenter: [-74.006, 40.7128], // New York City
            zoomLevel: 10,
            bearing: 0,
            pitch: 0,
            basemap: 'mapbox://styles/mapbox/standard'
        };
        
        console.log('🚀 Starting Pangea map initialization...');
        
        pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, mapOptions)
            .then(map => {
                console.log('🎉 Map created successfully!');
                
                // Beautiful event logging
                map.loaded.add(() => {
                    console.log('✨ Map is fully loaded and ready!');
                });
                
                map.moveStarted.add(() => {
                    console.log('🏃 Map movement started...');
                });
                
                map.moved.add((region) => {
                    console.log('📍 Map position:', {
                        lat: region.geoCenter.latitude.toFixed(4),
                        lng: region.geoCenter.longitude.toFixed(4),
                        zoom: region.zoomLevel.toFixed(2)
                    });
                });
                
                map.moveEnded.add(() => {
                    console.log('🛑 Map movement ended');
                });
                
                map.zoomStarted.add(() => {
                    console.log('🔍 Zoom started...');
                });
                
                map.zoomEnded.add(() => {
                    console.log('🎯 Zoom ended at level:', map.zoomLevel.toFixed(2));
                });
                
                // Log initial state
                console.log('📊 Initial map state:', {
                    center: `${map.geoCenter.latitude.toFixed(4)}, ${map.geoCenter.longitude.toFixed(4)}`,
                    zoom: map.zoomLevel,
                    size: `${map.size.width}x${map.size.height}px`,
                    projection: map.projection
                });
            })
            .catch(error => {
                console.error('❌ Error creating map:', error);
            });
    </script>
</body>
</html>
```

## Next Steps

Now that you have a basic map running, you can:

1. **Control the map** - Programmatically move, zoom, and rotate (covered in [Basic Map Operations](03-basic-map-operations.md))
2. **Add layers** - Display data on your map (covered in [Working with Layers](04-working-with-layers.md))
3. **Add graphics** - Place markers, lines, and polygons (covered in [Graphics and Markers](05-graphics-and-markers.md))
4. **Create animations** - Animate your visualizations over time (covered in [Animations](06-animations.md))

## Common Issues

### Map Container Not Found
```javascript
// Make sure the DOM element exists before creating the map
document.addEventListener('DOMContentLoaded', () => {
    // Create your map here
    console.log('🔄 DOM loaded, creating map...');
});
```

### Access Token Issues
- Ensure your Mapbox access token is valid and has the necessary permissions
- Check that the token is correctly set in your environment variables or configuration
- Verify the token hasn't expired

### Styling Issues
- Make sure the map container has explicit width and height CSS properties
- The container should be visible (not `display: none`) when the map is created
- Check for CSS conflicts that might affect the map container

### Console Logging
The examples above include comprehensive console logging to help you understand what's happening:
- 🚀 Initialization messages
- ✅ Success confirmations  
- 📍 Position updates
- 🔍 Zoom changes
- ❌ Error messages
- 📊 State information

This logging will help you debug issues and understand the map's behavior during development.
