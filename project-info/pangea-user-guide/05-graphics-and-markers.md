# 5. Graphics and Markers

Pangea Web SDK provides multiple ways to add visual graphics, markers, and informational popups to your map. This section covers the main approaches: using Popups for information windows, OverlayLayers for manually managed graphics, and FeatureLayers for data-driven visualizations.

## Adding Popups

Popups are useful for displaying information when a user interacts with a map feature or a specific location.

### Creating a Popup

```javascript
// Assuming 'map' is your Pangea MapboxViewport instance
// and 'pangea' is the imported Pangea library

console.log('💬 Creating popups for map interaction...');

const popupOptions = {
    origin: [-74.0060, 40.7128], // Geographic point [lng, lat] where the popup anchors
    content: '<div style="padding: 10px;"><h3>🗽 New York City</h3><p>The city that never sleeps!</p></div>',
    anchor: pangea.visuals.Anchor.BOTTOM, // Anchor point on the popup relative to the origin
    offset: [0, -10], // Offset in pixels [x, y] from the anchor point
    showCloseButton: true,
    closeOnClick: true, // Close if map is clicked
    closeOnMove: false, // Don't close if map moves
    className: 'custom-popup'
};

const popup = new pangea.maps.Popup(popupOptions.content, popupOptions.origin, popupOptions);

// Add the popup to the map's popups collection
map.popups.add(popup);
console.log('✅ Popup added to map');

// Set up popup event listeners with beautiful logging
popup.opened.add(() => {
    console.log('👁️  Popup opened and visible');
});

popup.closed.add(() => {
    console.log('🙈 Popup closed and hidden');
});

popup.changed.add((changes) => {
    console.log('🔧 Popup properties changed:', Object.keys(changes));
});

// Open the popup
popup.open();
console.log('🎉 Popup opened successfully');

// Example: Close it after 5 seconds
setTimeout(() => {
    console.log('⏰ Auto-closing popup after 5 seconds...');
    popup.close();
}, 5000);
```

### Advanced Popup Usage

```javascript
console.log('🎨 Creating advanced popup with rich content...');

// Create a popup with rich HTML content
const advancedPopupContent = `
    <div style="font-family: Arial, sans-serif; max-width: 300px;">
        <div style="background: linear-gradient(45deg, #007cbf, #0099ff); color: white; padding: 15px; margin: -10px -10px 10px -10px; border-radius: 8px 8px 0 0;">
            <h3 style="margin: 0; font-size: 18px;">📍 Location Details</h3>
        </div>
        <div style="padding: 0 5px;">
            <p><strong>Coordinates:</strong> 40.7128°N, 74.0060°W</p>
            <p><strong>Elevation:</strong> 10 meters</p>
            <p><strong>Population:</strong> 8.3 million</p>
            <div style="margin-top: 15px;">
                <button onclick="console.log('🔍 More info clicked')" style="background: #007cbf; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    More Info
                </button>
            </div>
        </div>
    </div>
`;

const advancedPopup = new pangea.maps.Popup(
    advancedPopupContent,
    [-74.0060, 40.7128],
    {
        anchor: pangea.visuals.Anchor.BOTTOM_LEFT,
        offset: [10, -15],
        showCloseButton: true,
        closeOnClick: false, // Keep open when clicking elsewhere
        closeOnMove: true,   // Close when map moves
        className: 'advanced-popup',
        maximumWidth: 350,
        minimumWidth: 250
    }
);

map.popups.add(advancedPopup);

// Demonstrate popup management
setTimeout(() => {
    console.log('🎭 Opening advanced popup...');
    advancedPopup.open();
}, 2000);

// Example: Update popup content dynamically
setTimeout(() => {
    console.log('🔄 Updating popup content...');
    advancedPopup.content = `
        <div style="padding: 15px; text-align: center;">
            <h3>🔄 Updated Content</h3>
            <p>This popup content was updated dynamically!</p>
            <p>Current time: ${new Date().toLocaleTimeString()}</p>
        </div>
    `;
}, 7000);
```

### Popup Options Reference

```javascript
console.log('📋 Popup options reference:');

const allPopupOptions = {
    // Position and anchoring
    origin: [-74.0060, 40.7128],           // Geographic anchor point
    anchor: pangea.visuals.Anchor.BOTTOM,  // Which part of popup connects to origin
    offset: [0, -10],                      // Pixel offset from anchor point
    
    // Content and appearance
    content: '<h3>Hello World</h3>',       // HTML content
    className: 'my-popup-class',           // CSS class for styling
    
    // Size constraints
    size: { width: 300, height: 200 },     // Explicit size (optional)
    minimumWidth: 200,                      // Minimum width in pixels
    maximumWidth: 400,                      // Maximum width in pixels
    minimumHeight: 100,                     // Minimum height in pixels
    maximumHeight: 300,                     // Maximum height in pixels
    
    // Behavior
    showCloseButton: true,                  // Show X button
    closeOnClick: true,                     // Close when map clicked
    closeOnMove: false,                     // Close when map moves
    
    // Styling
    zIndex: 1000                           // Z-index for layering
};

console.log('Available anchor positions:', {
    TOP_LEFT: 'pangea.visuals.Anchor.TOP_LEFT',
    TOP_CENTER: 'pangea.visuals.Anchor.TOP_CENTER', 
    TOP_RIGHT: 'pangea.visuals.Anchor.TOP_RIGHT',
    BOTTOM_LEFT: 'pangea.visuals.Anchor.BOTTOM_LEFT',
    BOTTOM_CENTER: 'pangea.visuals.Anchor.BOTTOM_CENTER',
    BOTTOM_RIGHT: 'pangea.visuals.Anchor.BOTTOM_RIGHT'
});
```

## Adding Graphics with OverlayLayer

OverlayLayer is perfect for manually managed graphics where you want precise control over individual visual elements.

### Creating Basic Overlays

```javascript
console.log('🎨 Creating basic overlays with OverlayLayer...');

// 1. Create individual overlay objects
const overlays = [
    // Image marker
    new pangea.overlays.ImageMarker(
        [-74.00, 40.71], // New York
        {
            url: 'https://cdn-icons-png.flaticon.com/32/684/684908.png', // Pin icon
            size: [32, 32],
            anchor: pangea.visuals.Anchor.BOTTOM_CENTER
        },
        { id: 'nyc-pin' }
    ),
    
    // Circle marker
    new pangea.overlays.CircleMarker(
        12, // radius
        [-122.42, 37.77], // San Francisco
        {
            fill: { color: '#ff4444', opacity: 0.8 },
            stroke: { color: '#ffffff', width: 2 }
        },
        { id: 'sf-circle' }
    ),
    
    // Text marker
    new pangea.overlays.TextMarker(
        'Chicago',
        [-87.63, 41.88],
        {
            color: pangea.visuals.Color.BLACK,
            size: 14,
            halo: { color: pangea.visuals.Color.WHITE, width: 2 },
            offset: [0, -5]
        },
        { id: 'chicago-label' }
    ),
    
    // Line path
    new pangea.overlays.LinePath(
        new pangea.geography.GeoLine([
            [-74.00, 40.71], // NYC
            [-122.42, 37.77] // SF
        ]),
        {
            stroke: { color: '#007cbf', width: 3, opacity: 0.8 }
        },
        { id: 'flight-path' }
    ),
    
    // Polygon path
    new pangea.overlays.PolygonPath(
        new pangea.geography.GeoPolygon([
            [-87.7, 41.8],
            [-87.6, 41.8], 
            [-87.6, 41.9],
            [-87.7, 41.9],
            [-87.7, 41.8]
        ]),
        {
            fill: { color: '#00ff00', opacity: 0.3 },
            stroke: { color: '#008800', width: 2 }
        },
        { id: 'chicago-area' }
    )
];

console.log(`📍 Created ${overlays.length} overlay objects`);

// 2. Create a StaticOverlaySource with these overlays
const overlaySource = new pangea.sources.StaticOverlaySource(
    overlays,
    { id: 'manual-graphics-source' }
);

console.log('📦 Created overlay source');

// 3. Create an OverlayLayer using the source
const overlayLayer = new pangea.layers.OverlayLayer(
    overlaySource,
    {
        id: 'manual-graphics-layer',
        slot: pangea.layers.LayerSlot.TOP,
        opacity: 1.0
    }
);

console.log('🗂️  Created overlay layer');

// 4. Add the OverlayLayer to the map
map.layers.add(overlayLayer);

console.log('✅ Overlay layer added to map');

// Set up event logging
overlayLayer.loading.add(() => {
    console.log('⏳ Overlay layer loading...');
});

overlayLayer.loaded.add(() => {
    console.log('✨ Overlay layer loaded successfully');
    console.log(`📊 Overlays rendered: ${overlays.length}`);
});

// Log overlay details
overlays.forEach((overlay, index) => {
    console.log(`📍 Overlay ${index + 1}: ${overlay.constructor.name} (${overlay.id})`);
});
```

### Dynamic Overlay Management

```javascript
console.log('🔄 Demonstrating dynamic overlay management...');

// Function to add overlays dynamically
function addDynamicOverlay(location, type, properties) {
    console.log(`➕ Adding ${type} overlay at ${location[1].toFixed(4)}, ${location[0].toFixed(4)}`);
    
    let newOverlay;
    
    switch (type) {
        case 'circle':
            newOverlay = new pangea.overlays.CircleMarker(
                properties.radius || 8,
                location,
                {
                    fill: { color: properties.color || '#ff0000', opacity: 0.7 },
                    stroke: { color: '#ffffff', width: 2 }
                },
                { id: `dynamic-circle-${Date.now()}` }
            );
            break;
            
        case 'text':
            newOverlay = new pangea.overlays.TextMarker(
                properties.text || 'Dynamic Text',
                location,
                {
                    color: pangea.visuals.Color.fromHex(properties.color || '#000000'),
                    size: properties.size || 12,
                    halo: { color: pangea.visuals.Color.WHITE, width: 1 }
                },
                { id: `dynamic-text-${Date.now()}` }
            );
            break;
            
        case 'image':
            newOverlay = new pangea.overlays.ImageMarker(
                location,
                {
                    url: properties.url || 'https://cdn-icons-png.flaticon.com/16/684/684908.png',
                    size: properties.size || [16, 16],
                    anchor: pangea.visuals.Anchor.CENTER
                },
                { id: `dynamic-image-${Date.now()}` }
            );
            break;
    }
    
    if (newOverlay && overlaySource) {
        overlaySource.add(newOverlay);
        console.log(`✅ Added ${type} overlay: ${newOverlay.id}`);
        return newOverlay;
    }
}

// Function to remove overlays
function removeDynamicOverlay(overlay) {
    if (overlay && overlaySource) {
        overlaySource.remove(overlay);
        console.log(`🗑️  Removed overlay: ${overlay.id}`);
    }
}

// Demonstrate adding overlays over time
setTimeout(() => {
    addDynamicOverlay([-95.37, 29.76], 'circle', { radius: 15, color: '#ff8800' }); // Houston
}, 2000);

setTimeout(() => {
    addDynamicOverlay([-80.19, 25.76], 'text', { text: 'Miami', color: '#0088ff', size: 16 }); // Miami
}, 4000);

setTimeout(() => {
    addDynamicOverlay([-122.33, 47.61], 'image', { 
        url: 'https://cdn-icons-png.flaticon.com/24/684/684908.png',
        size: [24, 24]
    }); // Seattle
}, 6000);

// Example: Remove overlays after some time
setTimeout(() => {
    console.log('🧹 Cleaning up some overlays...');
    const allOverlays = overlaySource.overlays.items;
    if (allOverlays.length > 3) {
        // Remove the first overlay
        const firstOverlay = allOverlays[Object.keys(allOverlays)[0]];
        removeDynamicOverlay(firstOverlay);
    }
}, 10000);
```

## Data-Driven Graphics with FeatureLayer

FeatureLayer is ideal for visualizing datasets where appearance is driven by data attributes.

### Creating a Data-Driven FeatureLayer

```javascript
console.log('📊 Creating data-driven graphics with FeatureLayer...');

// 1. Create sample GeoJSON data
const cityData = {
    type: 'FeatureCollection',
    features: [
        {
            type: 'Feature',
            geometry: { type: 'Point', coordinates: [-74.006, 40.7128] },
            properties: { 
                name: 'New York', 
                population: 8336817, 
                type: 'megacity',
                founded: 1624,
                area: 783.8
            }
        },
        {
            type: 'Feature',
            geometry: { type: 'Point', coordinates: [-118.2437, 34.0522] },
            properties: { 
                name: 'Los Angeles', 
                population: 3979576, 
                type: 'major',
                founded: 1781,
                area: 1302.15
            }
        },
        {
            type: 'Feature',
            geometry: { type: 'Point', coordinates: [-87.6298, 41.8781] },
            properties: { 
                name: 'Chicago', 
                population: 2693976, 
                type: 'major',
                founded: 1837,
                area: 606.1
            }
        },
        {
            type: 'Feature',
            geometry: { type: 'Point', coordinates: [-95.3698, 29.7604] },
            properties: { 
                name: 'Houston', 
                population: 2320268, 
                type: 'major',
                founded: 1836,
                area: 1651.76
            }
        },
        {
            type: 'Feature',
            geometry: { type: 'Point', coordinates: [-112.0740, 33.4484] },
            properties: { 
                name: 'Phoenix', 
                population: 1680992, 
                type: 'large',
                founded: 1868,
                area: 1341.95
            }
        }
    ]
};

console.log(`📈 Created dataset with ${cityData.features.length} cities`);

// 2. Create GeoJSONFeatureSource
const citySource = new pangea.sources.GeoJSONFeatureSource(
    cityData,
    { id: 'us-cities-source' }
);

console.log('📦 Created GeoJSON feature source');

// 3. Create FeatureLayer with data-driven styling
const cityLayer = new pangea.layers.FeatureLayer(
    citySource,
    {
        id: 'us-cities-layer',
        slot: pangea.layers.LayerSlot.MIDDLE,
        
        // Style function that converts features to overlays based on data
        styleFeature: (feature) => {
            const overlays = [];
            const props = feature.properties;
            const geometry = feature.geometry;
            
            if (geometry instanceof pangea.geography.GeoPoint) {
                // Calculate circle size based on population
                const baseRadius = 8;
                const populationRadius = Math.max(baseRadius, Math.min(25, props.population / 200000));
                
                // Determine color based on city type
                let fillColor = '#cccccc';
                let strokeColor = '#666666';
                
                switch (props.type) {
                    case 'megacity':
                        fillColor = '#ff4444';
                        strokeColor = '#cc0000';
                        break;
                    case 'major':
                        fillColor = '#ff8800';
                        strokeColor = '#cc6600';
                        break;
                    case 'large':
                        fillColor = '#ffcc00';
                        strokeColor = '#cc9900';
                        break;
                }
                
                // Create circle marker
                overlays.push(new pangea.overlays.CircleMarker(
                    populationRadius,
                    geometry,
                    {
                        fill: { color: fillColor, opacity: 0.8 },
                        stroke: { color: strokeColor, width: 2 }
                    },
                    { id: `city-circle-${props.name.toLowerCase().replace(/\s+/g, '-')}` }
                ));
                
                // Create text label
                overlays.push(new pangea.overlays.TextMarker(
                    props.name,
                    geometry,
                    {
                        color: pangea.visuals.Color.BLACK,
                        size: Math.max(10, Math.min(16, populationRadius * 0.8)),
                        halo: { color: pangea.visuals.Color.WHITE, width: 2 },
                        offset: [0, -populationRadius - 8]
                    },
                    { id: `city-label-${props.name.toLowerCase().replace(/\s+/g, '-')}` }
                ));
                
                // Add population label
                const populationText = `${(props.population / 1000000).toFixed(1)}M`;
                overlays.push(new pangea.overlays.TextMarker(
                    populationText,
                    geometry,
                    {
                        color: pangea.visuals.Color.fromHex('#666666'),
                        size: 10,
                        halo: { color: pangea.visuals.Color.WHITE, width: 1 },
                        offset: [0, populationRadius + 12]
                    },
                    { id: `city-pop-${props.name.toLowerCase().replace(/\s+/g, '-')}` }
                ));
            }
            
            return overlays;
        }
    }
);

console.log('🗂️  Created feature layer with data-driven styling');

// 4. Add the FeatureLayer to the map
map.layers.add(cityLayer);

console.log('✅ Feature layer added to map');

// Set up comprehensive event logging
cityLayer.loading.add(() => {
    console.log('⏳ City data loading...');
});

cityLayer.loaded.add(() => {
    console.log('✨ City data loaded and styled');
    console.log('🎨 Styling rules applied:');
    console.log('  🔴 Megacity (8M+): Red circles');
    console.log('  🟠 Major city (2M+): Orange circles');
    console.log('  🟡 Large city (1M+): Yellow circles');
    console.log('  📏 Circle size: Based on population');
});

// Log feature details
cityData.features.forEach((feature, index) => {
    const props = feature.properties;
    console.log(`🏙️  City ${index + 1}: ${props.name} (${(props.population/1000000).toFixed(1)}M people, ${props.type})`);
});
```

## Interactive Graphics

### Click Interaction with Graphics

```javascript
console.log('🖱️  Setting up interactive graphics...');

// Enhanced mouse interaction handler
map.mouse.click.add((mouseEvent) => {
    console.log('🎯 Mouse clicked, checking for graphic interactions...');
    
    let foundInteraction = false;
    
    // Check OverlayLayer for interactions
    if (overlayLayer && overlayLayer.isVisible) {
        const clickedOverlays = overlayLayer.inspect(mouseEvent.screenPoint);
        if (clickedOverlays && clickedOverlays.length > 0) {
            const overlay = clickedOverlays[0];
            console.log('🎨 Clicked overlay:', {
                id: overlay.id,
                type: overlay.constructor.name,
                geometry: overlay.geometry.constructor.name
            });
            
            // Show popup for clicked overlay
            const popupContent = `
                <div style="padding: 10px;">
                    <h3>🎨 Overlay Details</h3>
                    <p><strong>ID:</strong> ${overlay.id}</p>
                    <p><strong>Type:</strong> ${overlay.constructor.name}</p>
                    <p><strong>Geometry:</strong> ${overlay.geometry.constructor.name}</p>
                </div>
            `;
            
            const overlayPopup = new pangea.maps.Popup(
                popupContent,
                mouseEvent.geoPoint,
                {
                    anchor: pangea.visuals.Anchor.BOTTOM,
                    closeOnClick: true,
                    showCloseButton: true
                }
            );
            
            map.popups.add(overlayPopup);
            overlayPopup.open();
            
            foundInteraction = true;
        }
    }
    
    // Check FeatureLayer for interactions
    if (!foundInteraction && cityLayer && cityLayer.isVisible) {
        const clickedFeatures = cityLayer.inspect(mouseEvent.screenPoint);
        if (clickedFeatures && clickedFeatures.length > 0) {
            const overlay = clickedFeatures[0];
            const feature = overlay.feature;
            
            if (feature) {
                console.log('🏙️  Clicked city feature:', {
                    name: feature.properties.name,
                    population: feature.properties.population,
                    type: feature.properties.type
                });
                
                // Show detailed popup for city
                const cityPopupContent = `
                    <div style="font-family: Arial, sans-serif; max-width: 280px;">
                        <div style="background: linear-gradient(45deg, #007cbf, #0099ff); color: white; padding: 12px; margin: -10px -10px 10px -10px; border-radius: 6px 6px 0 0;">
                            <h3 style="margin: 0; font-size: 16px;">🏙️ ${feature.properties.name}</h3>
                        </div>
                        <div style="padding: 5px;">
                            <p><strong>👥 Population:</strong> ${feature.properties.population.toLocaleString()}</p>
                            <p><strong>📊 Category:</strong> ${feature.properties.type}</p>
                            <p><strong>📅 Founded:</strong> ${feature.properties.founded}</p>
                            <p><strong>📐 Area:</strong> ${feature.properties.area} km²</p>
                            <p><strong>🌍 Coordinates:</strong> ${feature.geometry.latitude.toFixed(4)}°N, ${Math.abs(feature.geometry.longitude).toFixed(4)}°W</p>
                        </div>
                    </div>
                `;
                
                const cityPopup = new pangea.maps.Popup(
                    cityPopupContent,
                    feature.geometry,
                    {
                        anchor: pangea.visuals.Anchor.BOTTOM,
                        offset: [0, -20],
                        closeOnClick: true,
                        showCloseButton: true,
                        className: 'city-info-popup'
                    }
                );
                
                map.popups.add(cityPopup);
                cityPopup.open();
                
                foundInteraction = true;
            }
        }
    }
    
    if (!foundInteraction) {
        console.log('🚫 No graphics found at click location');
    }
});

// Hover effects
map.mouse.move.add((mouseEvent) => {
    let foundHover = false;
    
    // Check for hover over overlays
    if (overlayLayer && overlayLayer.isVisible) {
        const hoveredOverlays = overlayLayer.inspect(mouseEvent.screenPoint);
        foundHover = hoveredOverlays && hoveredOverlays.length > 0;
    }
    
    // Check for hover over features
    if (!foundHover && cityLayer && cityLayer.isVisible) {
        const hoveredFeatures = cityLayer.inspect(mouseEvent.screenPoint);
        foundHover = hoveredFeatures && hoveredFeatures.length > 0;
    }
    
    // Change cursor based on hover state
    map.element.style.cursor = foundHover ? 'pointer' : '';
});

console.log('✅ Interactive graphics setup complete');
```

## Complete Working Example

Here's a comprehensive example that demonstrates all graphics concepts:

```javascript
import pangea from '@twc/pangea-web/mapbox';

const MAPBOX_ACCESS_TOKEN = 'your-mapbox-access-token-here';

async function createInteractiveGraphicsMap() {
    try {
        console.log('🚀 Creating interactive graphics map...');
        
        const map = await pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, {
            geoCenter: [-98.5795, 39.8283], // Center of USA
            zoomLevel: 4,
            basemap: 'mapbox://styles/mapbox/light-v10'
        });
        
        console.log('✅ Base map created');
        
        // Wait for map to load
        await new Promise(resolve => map.loaded.add(resolve));
        
        // 1. Create manual overlays
        console.log('🎨 Adding manual overlay graphics...');
        
        const manualOverlays = [
            new pangea.overlays.CircleMarker(
                20,
                [-122.4194, 37.7749], // San Francisco
                {
                    fill: { color: '#ff4444', opacity: 0.7 },
                    stroke: { color: '#ffffff', width: 3 }
                },
                { id: 'sf-marker' }
            ),
            
            new pangea.overlays.TextMarker(
                'West Coast',
                [-120, 36],
                {
                    color: pangea.visuals.Color.BLUE,
                    size: 18,
                    halo: { color: pangea.visuals.Color.WHITE, width: 2 }
                },
                { id: 'west-coast-label' }
            ),
            
            new pangea.overlays.LinePath(
                new pangea.geography.GeoLine([
                    [-122.4194, 37.7749], // SF
                    [-74.006, 40.7128]    // NYC
                ]),
                {
                    stroke: { color: '#007cbf', width: 4, opacity: 0.8 }
                },
                { id: 'coast-to-coast' }
            )
        ];
        
        const overlaySource = new pangea.sources.StaticOverlaySource(
            manualOverlays,
            { id: 'manual-graphics' }
        );
        
        const overlayLayer = new pangea.layers.OverlayLayer(
            overlaySource,
            {
                id: 'manual-overlay-layer',
                slot: pangea.layers.LayerSlot.TOP
            }
        );
        
        map.layers.add(overlayLayer);
        
        // 2. Create data-driven city visualization
        console.log('📊 Adding data-driven city visualization...');
        
        const cityData = {
            type: 'FeatureCollection',
            features: [
                { type: 'Feature', geometry: { type: 'Point', coordinates: [-74.006, 40.7128] }, properties: { name: 'New York', pop: 8.3, type: 'mega' }},
                { type: 'Feature', geometry: { type: 'Point', coordinates: [-118.2437, 34.0522] }, properties: { name: 'Los Angeles', pop: 4.0, type: 'major' }},
                { type: 'Feature', geometry: { type: 'Point', coordinates: [-87.6298, 41.8781] }, properties: { name: 'Chicago', pop: 2.7, type: 'major' }},
                { type: 'Feature', geometry: { type: 'Point', coordinates: [-95.3698, 29.7604] }, properties: { name: 'Houston', pop: 2.3, type: 'major' }},
                { type: 'Feature', geometry: { type: 'Point', coordinates: [-112.0740, 33.4484] }, properties: { name: 'Phoenix', pop: 1.7, type: 'large' }}
            ]
        };
        
        const citySource = new pangea.sources.GeoJSONFeatureSource(
            cityData,
            { id: 'cities-data' }
        );
        
        const cityLayer = new pangea.layers.FeatureLayer(
            citySource,
            {
                id: 'cities-layer',
                slot: pangea.layers.LayerSlot.MIDDLE,
                styleFeature: (feature) => {
                    const props = feature.properties;
                    const radius = Math.max(8, props.pop * 3);
                    const color = props.type === 'mega' ? '#ff0000' : 
                                 props.type === 'major' ? '#ff8800' : '#ffcc00';
                    
                    return [
                        new pangea.overlays.CircleMarker(
                            radius,
                            feature.geometry,
                            {
                                fill: { color: color, opacity: 0.8 },
                                stroke: { color: '#ffffff', width: 2 }
                            }
                        ),
                        new pangea.overlays.TextMarker(
                            props.name,
                            feature.geometry,
                            {
                                color: pangea.visuals.Color.BLACK,
                                size: 12,
                                halo: { color: pangea.visuals.Color.WHITE, width: 2 },
                                offset: [0, -radius - 8]
                            }
                        )
                    ];
                }
            }
        );
        
        map.layers.add(cityLayer);
        
        // 3. Set up comprehensive interaction
        console.log('🖱️  Setting up interactive features...');
        
        map.mouse.click.add((mouseEvent) => {
            console.log('🎯 Checking for interactions...');
            
            // Check overlay layer
            const overlayResults = overlayLayer.inspect(mouseEvent.screenPoint);
            if (overlayResults && overlayResults.length > 0) {
                const overlay = overlayResults[0];
                console.log('🎨 Clicked overlay:', overlay.id);
                
                const popup = new pangea.maps.Popup(
                    `<div style="padding: 10px;"><h3>Manual Overlay</h3><p>ID: ${overlay.id}</p></div>`,
                    mouseEvent.geoPoint,
                    { anchor: pangea.visuals.Anchor.BOTTOM, closeOnClick: true }
                );
                map.popups.add(popup);
                popup.open();
                return;
            }
            
            // Check feature layer
            const featureResults = cityLayer.inspect(mouseEvent.screenPoint);
            if (featureResults && featureResults.length > 0) {
                const overlay = featureResults[0];
                const feature = overlay.feature;
                console.log('🏙️  Clicked city:', feature.properties.name);
                
                const popup = new pangea.maps.Popup(
                    `<div style="padding: 10px;">
                        <h3>🏙️ ${feature.properties.name}</h3>
                        <p>Population: ${feature.properties.pop}M</p>
                        <p>Type: ${feature.properties.type}</p>
                    </div>`,
                    feature.geometry,
                    { anchor: pangea.visuals.Anchor.BOTTOM, closeOnClick: true }
                );
                map.popups.add(popup);
                popup.open();
            }
        });
        
        // Set up hover effects
        map.mouse.move.add((mouseEvent) => {
            const hasOverlay = overlayLayer.inspect(mouseEvent.screenPoint)?.length > 0;
            const hasFeature = cityLayer.inspect(mouseEvent.screenPoint)?.length > 0;
            map.element.style.cursor = (hasOverlay || hasFeature) ? 'pointer' : '';
        });
        
        // Set up event logging
        [overlayLayer, cityLayer].forEach((layer, index) => {
            const names = ['Manual Overlays', 'City Features'];
            layer.loaded.add(() => console.log(`✅ ${names[index]} loaded`));
        });
        
        console.log('🎉 Interactive graphics map complete!');
        console.log('📊 Summary:', {
            manualOverlays: manualOverlays.length,
            cityFeatures: cityData.features.length,
            totalLayers: 2
        });
        
    } catch (error) {
        console.error('❌ Error creating interactive graphics map:', error);
    }
}

// Run the example
createInteractiveGraphicsMap();
```

## Summary

Graphics and markers in Pangea can be implemented through three main approaches:

### 1. **Popups** - Information Windows
- Use `pangea.maps.Popup` for displaying rich HTML content
- Anchor to geographic coordinates with flexible positioning
- Support for interactive content and dynamic updates
- Automatic management through map's popup collection

### 2. **OverlayLayer** - Manual Graphics Management
- Use `StaticOverlaySource` with individual `Overlay` objects
- Perfect for precise control over individual graphics
- Support for all overlay types: `ImageMarker`, `CircleMarker`, `TextMarker`, `LinePath`, `PolygonPath`
- Dynamic add/remove capabilities
- Ideal for small to medium numbers of graphics

### 3. **FeatureLayer** - Data-Driven Visualizations
- Use `GeoJSONFeatureSource` with feature data
- Implement `styleFeature` function to convert data to visual overlays
- Automatic styling based on feature properties
- Efficient for large datasets
- Built-in interaction support through `inspect()` method

### Key Benefits of Each Approach:

**Popups:**
- Rich HTML content support
- Easy positioning and anchoring
- Built-in close/open behavior
- Perfect for information display

**OverlayLayer:**
- Direct control over individual graphics
- Easy dynamic management
- Precise styling control
- Good performance for moderate numbers of graphics

**FeatureLayer:**
- Data-driven styling
- Efficient for large datasets
- Automatic feature-to-visual conversion
- Built-in interaction capabilities

All approaches support comprehensive event handling and beautiful logging to help you understand what's happening during development and debugging.
