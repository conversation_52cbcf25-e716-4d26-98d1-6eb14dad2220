# 6. Animations

Pangea Web SDK includes a powerful `Animator` class to control time-based animations on the map. This is crucial for visualizing dynamic data, such as weather patterns, time-series data, or creating story-driven map experiences. The animator works with time-aware layers to create smooth, synchronized animations.

## The Animator (`pangea.time.Animator`)

The `Animator` manages a timeline defined by a `storyboard`. It controls playback, looping, and the current animation `time`. Layers that are time-aware will synchronize with the map's animator to display data for the current animation time.

A `MapViewport` instance has an `animator` property (`map.animator`) that you can configure and control.

### Understanding Animation Concepts

```javascript
console.log('🎬 Understanding Pangea animation concepts...');

// The animator works with several key concepts:
console.log('📚 Key Animation Concepts:');
console.log('  🎞️  Storyboard: Defines the timeline structure');
console.log('  ⏱️  Frames: Discrete time points in the animation');
console.log('  🕐 Time: Real-world time each frame represents');
console.log('  ▶️  Playback: Controls how frames advance');
console.log('  🔄 Looping: Whether animation repeats');
console.log('  📈 Sliding: Whether time advances with real-time');

// Access the map's animator
const animator = map.animator;
console.log('🎭 Map animator available:', {
    isPlaying: animator.isPlaying,
    currentFrame: animator.playhead.frame,
    totalFrames: animator.length,
    currentTime: animator.time.toISOString(),
    duration: animator.duration + 'ms'
});
```

## ⚠️ Critical: Animation Setup Order

**IMPORTANT**: The animator must be configured BEFORE adding animated layers to the map. The correct pattern is:

1. Create source
2. Wait for source.ready
3. Configure animator with source data
4. Connect source to animator
5. Create and add layer to map

```javascript
console.log('⚙️  Correct animation setup pattern...');

// Create a SUN API source for satellite/radar data
const sunApiToken = 'your-sun-api-token';
const satradSource = new pangea.sources.SunRasterSource("satradFcst", sunApiToken, {});

// CRITICAL: Wait for source to be ready, then configure animator
satradSource.ready.once({
    handler: e => {
        console.log('🛰️ Satellite source ready with data');
        
        // Get sequence data from the source
        const framesLength = satradSource.currentSequence.times.length;
        const startTime = satradSource.currentSequence.times[framesLength - 1].instant;
        
        console.log(`📊 Configuring animator: ${framesLength} frames starting at ${startTime.toISOString()}`);
        
        // Configure animator BEFORE adding layer
        map.animator.startTime = startTime;
        map.animator.storyboard = [{
            frames: framesLength,
            span: framesLength * 900000 // 15 minutes per frame (900000ms)
        }];
        map.animator.playhead.move(framesLength); // Start at latest frame
        
        // CRITICAL: Connect source to animator BEFORE adding layer
        satradSource.animator = map.animator;
        console.log('🔗 Source connected to animator');
        
        // Now create and add the layer
        const layer = new pangea.layers.RasterLayer(satradSource, { id: "SatRadLayer" });
        map.layers.add(layer);
        
        console.log('✅ Animated layer added to map');
        
        // Set up event listeners for UI updates
        setupAnimationEventListeners();
        
        // Start animation
        map.animator.play();
        console.log('▶️ Animation started');
    }
});

// Handle source errors
if (satradSource.error) {
    satradSource.error.once({
        handler: (error) => {
            console.error('❌ Failed to load satellite data:', error);
        }
    });
}
```

## Event System: Using Pangea EventSource Pattern

Pangea uses a special EventSource pattern, not standard DOM events. Use `.once()` with recursive re-adding for continuous updates:

```javascript
console.log('🎧 Setting up Pangea event listeners...');

function setupAnimationEventListeners() {
    console.log('📡 Configuring animation event listeners...');
    
    // Use .once() pattern for frame changes (not .add() or .addEventListener())
    function addFrameListener() {
        map.animator.playhead.frameChanged.once({
            handler: function(e) {
                console.log(`🎞️ Frame changed to: ${e.frame}`);
                
                // Update your UI here
                updateAnimationUI(e.frame);
                
                // Re-add listener for next frame
                addFrameListener();
            }
        });
    }
    
    // Start listening for frame changes
    addFrameListener();
    
    // Listen for animator start/stop events
    if (map.animator.started) {
        map.animator.started.once({
            handler: () => {
                console.log('▶️ Animation started');
                // Re-add if you want to listen for future starts
            }
        });
    }
    
    if (map.animator.stopped) {
        map.animator.stopped.once({
            handler: () => {
                console.log('⏹️ Animation stopped');
                // Re-add if you want to listen for future stops
            }
        });
    }
    
    console.log('✅ Event listeners configured');
}

function updateAnimationUI(currentFrame) {
    // Update your animation controls UI
    const totalFrames = map.animator.length;
    const currentTime = map.animator.time;
    const isPlaying = map.animator.isPlaying;
    
    console.log(`📊 UI Update: Frame ${currentFrame}/${totalFrames}, Time: ${currentTime.toISOString()}, Playing: ${isPlaying}`);
    
    // Example: Update HTML elements
    if (typeof document !== 'undefined') {
        const frameDisplay = document.getElementById('frame-display');
        const timeDisplay = document.getElementById('time-display');
        const slider = document.getElementById('frame-slider');
        
        if (frameDisplay) frameDisplay.textContent = `${currentFrame} / ${totalFrames}`;
        if (timeDisplay) timeDisplay.textContent = currentTime.toLocaleTimeString();
        if (slider) slider.value = currentFrame;
    }
}
```

## Animation Controls Implementation

```javascript
console.log('🎛️ Creating animation control interface...');

function createAnimationControls() {
    // Create control panel HTML
    const controlPanel = document.createElement('div');
    controlPanel.id = 'animation-controls';
    controlPanel.style.cssText = `
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(255, 255, 255, 0.95);
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        z-index: 1000;
        min-width: 280px;
    `;
    
    controlPanel.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <h3 style="margin: 0; font-size: 16px; flex: 1;">🎬 Animation Controls</h3>
            <span id="status-indicator" style="font-size: 12px; color: #666;">⏸️ Stopped</span>
        </div>
        
        <div style="display: flex; gap: 8px; margin-bottom: 12px;">
            <button id="play-btn" style="flex: 1; padding: 8px; border: none; border-radius: 4px; background: #4CAF50; color: white; cursor: pointer;">▶️ Play</button>
            <button id="pause-btn" style="flex: 1; padding: 8px; border: none; border-radius: 4px; background: #FF9800; color: white; cursor: pointer;">⏸️ Pause</button>
        </div>
        
        <div style="margin-bottom: 12px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                <label style="font-size: 12px; color: #666;">Frame</label>
                <span style="font-size: 12px; color: #666;">
                    <span id="frame-display">1</span> / <span id="total-frames">1</span>
                </span>
            </div>
            <input type="range" id="frame-slider" min="1" max="1" value="1" 
                   style="width: 100%; margin-bottom: 4px;">
        </div>
        
        <div style="font-size: 12px; color: #666; line-height: 1.4;">
            <div>📅 <span id="current-time">--:--:--</span></div>
            <div id="animation-info" style="margin-top: 4px; font-size: 10px; opacity: 0.7;"></div>
        </div>
    `;
    
    // Find map container and add controls
    const mapContainer = document.getElementById('map') || document.querySelector('[id*="map"]');
    if (mapContainer && mapContainer.parentElement) {
        mapContainer.parentElement.style.position = 'relative';
        mapContainer.parentElement.appendChild(controlPanel);
    }
    
    // Set up control handlers
    setupControlHandlers();
    
    console.log('✅ Animation controls created');
}

function setupControlHandlers() {
    const playBtn = document.getElementById('play-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const frameSlider = document.getElementById('frame-slider');
    
    // Play button
    playBtn?.addEventListener('click', () => {
        console.log('🎮 Play button clicked');
        if (map.animator && typeof map.animator.play === 'function') {
            map.animator.play();
            updateStatusIndicator('▶️ Playing');
        } else {
            console.warn('⚠️ Animator not ready or play method not available');
        }
    });
    
    // Pause button  
    pauseBtn?.addEventListener('click', () => {
        console.log('🎮 Pause button clicked');
        if (map.animator && typeof map.animator.stop === 'function') {
            map.animator.stop();
            updateStatusIndicator('⏸️ Paused');
        }
    });
    
    // Frame slider
    frameSlider?.addEventListener('input', (e) => {
        const frame = parseInt(e.target.value);
        console.log(`🎮 Frame slider moved to: ${frame}`);
        if (map.animator && map.animator.playhead && typeof map.animator.playhead.move === 'function') {
            map.animator.playhead.move(frame);
        }
    });
    
    console.log('✅ Control handlers configured');
}

function updateStatusIndicator(status) {
    const indicator = document.getElementById('status-indicator');
    if (indicator) indicator.textContent = status;
}

function updateControlsWithAnimatorData() {
    if (!map.animator) return;
    
    const totalFrames = map.animator.length || 1;
    const currentFrame = map.animator.playhead?.frame || 1;
    const currentTime = map.animator.time;
    
    // Update frame info
    const totalFramesEl = document.getElementById('total-frames');
    const frameSlider = document.getElementById('frame-slider');
    
    if (totalFramesEl) totalFramesEl.textContent = totalFrames;
    if (frameSlider) {
        frameSlider.max = totalFrames;
        frameSlider.value = currentFrame;
    }
    
    // Update time display
    const timeDisplay = document.getElementById('current-time');
    if (timeDisplay && currentTime) {
        timeDisplay.textContent = currentTime.toLocaleTimeString();
    }
    
    // Update info
    const infoEl = document.getElementById('animation-info');
    if (infoEl) {
        infoEl.textContent = `${totalFrames} frames • ${map.animator.isPlaying ? 'Playing' : 'Stopped'}`;
    }
}
```

## Working with Weather Data (SUN API)

```javascript
console.log('🌤️ Setting up weather animation with SUN API...');

async function createWeatherAnimation() {
    try {
        const sunApiToken = 'your-sun-api-token-here';
        
        // Create satellite/radar source
        console.log('📡 Creating satellite radar source...');
        const satradSource = new pangea.sources.SunRasterSource("satradFcst", sunApiToken, {});
        
        // Wait for source to be ready
        satradSource.ready.once({
            handler: e => {
                console.log('✅ Satellite source ready');
                
                const framesLength = satradSource.currentSequence.times.length;
                const startTime = satradSource.currentSequence.times[framesLength - 1].instant;
                
                console.log(`📊 Weather data: ${framesLength} frames, latest: ${startTime.toISOString()}`);
                
                // Configure animator for weather data
                map.animator.startTime = startTime;
                map.animator.storyboard = [{
                    frames: framesLength,
                    span: framesLength * 900000 // 15 minutes per frame
                }];
                map.animator.playhead.move(framesLength); // Start at latest
                
                // Connect and add layer
                satradSource.animator = map.animator;
                const layer = new pangea.layers.RasterLayer(satradSource, { 
                    id: "WeatherRadar",
                    opacity: 0.7
                });
                map.layers.add(layer);
                
                // Set up controls and events
                createAnimationControls();
                setupAnimationEventListeners();
                updateControlsWithAnimatorData();
                
                console.log('🎉 Weather animation ready!');
            }
        });
        
        // Handle errors
        if (satradSource.error) {
            satradSource.error.once({
                handler: (error) => {
                    console.error('❌ Weather data error:', error);
                }
            });
        }
        
        // Also create snow accumulation layer
        console.log('❄️ Creating snow accumulation source...');
        const snowSource = new pangea.sources.SunGridSource("snowAccum", sunApiToken, {});
        
        snowSource.ready.once({
            handler: e => {
                console.log('✅ Snow source ready');
                
                const framesLength = snowSource.currentSequence.times.length;
                const startTime = snowSource.currentSequence.times[0].instant;
                
                // Configure for snow (different timing - hourly instead of 15min)
                if (!map.animator.length) { // Only if not already configured
                    map.animator.startTime = startTime;
                    map.animator.storyboard = [{
                        frames: framesLength,
                        span: framesLength * 3600000 // 1 hour per frame
                    }];
                    map.animator.playhead.move(1); // Start at beginning for snow
                }
                
                // Create snow visualization
                const snowPalette = new pangea.visuals.Palette([
                    { value: 0.00025, color: [8, 193, 230, 1] },
                    { value: 0.03, color: [8, 155, 186, 1] },
                    { value: 0.07, color: [8, 123, 153, 1] },
                    { value: 0.12, color: [161, 145, 255, 1] },
                    { value: 0.20, color: [150, 96, 255, 1] },
                    { value: 0.30, color: [104, 42, 186, 1] },
                    { value: 0.45, color: [255, 73, 145, 1] },
                    { value: 0.60, color: [235, 165, 226, 1] },
                ], { 
                    id: "Snow (M)", 
                    interpolation: "LINEAR", 
                    minimumColorExtended: false, 
                    precision: 5 
                });
                
                const snowGridStyle = new pangea.visuals.GridRasterStyle(snowPalette, {});
                snowSource.animator = map.animator;
                
                const snowLayer = new pangea.layers.GridLayer(snowSource, snowGridStyle, { 
                    id: "SnowAccumulation",
                    opacity: 0.8
                });
                map.layers.add(snowLayer);
                
                console.log('❄️ Snow layer added');
            }
        });
        
    } catch (error) {
        console.error('❌ Error setting up weather animation:', error);
    }
}

// Initialize weather animation
createWeatherAnimation();
```

## Animation Playback Control

```javascript
console.log('🎮 Animation playback control methods...');

// Basic playback controls
function demonstratePlaybackControls() {
    console.log('🎬 Demonstrating playback controls...');
    
    // Check if animator is ready
    if (!map.animator || map.animator.length === 0) {
        console.warn('⚠️ Animator not configured. Add an animated layer first.');
        return;
    }
    
    // Start playing
    console.log('▶️ Starting animation...');
    map.animator.play();
    
    // Pause after 5 seconds
    setTimeout(() => {
        console.log('⏸️ Pausing animation...');
        map.animator.stop();
    }, 5000);
    
    // Resume after 2 seconds
    setTimeout(() => {
        console.log('▶️ Resuming animation...');
        map.animator.play();
    }, 7000);
    
    // Jump to specific frame
    setTimeout(() => {
        const targetFrame = Math.floor(map.animator.length / 2);
        console.log(`⏭️ Jumping to middle frame: ${targetFrame}`);
        map.animator.playhead.move(targetFrame);
    }, 10000);
    
    // Jump to specific time
    setTimeout(() => {
        const currentTime = new Date();
        console.log(`🕐 Jumping to current time: ${currentTime.toISOString()}`);
        // Note: This only works if the current time is within the animation range
        try {
            const frame = map.animator.findFrame(currentTime);
            if (frame) {
                map.animator.playhead.move(frame);
            }
        } catch (error) {
            console.log('⚠️ Current time not in animation range');
        }
    }, 12000);
}

// Manual frame control
function demonstrateFrameControl() {
    if (!map.animator || map.animator.length === 0) return;
    
    console.log('🎞️ Demonstrating manual frame control...');
    
    // Stop animation for manual control
    map.animator.stop();
    
    // Step through frames
    const totalFrames = map.animator.length;
    const framesToShow = Math.min(5, totalFrames);
    
    for (let i = 1; i <= framesToShow; i++) {
        setTimeout(() => {
            console.log(`📍 Moving to frame ${i}/${totalFrames}...`);
            map.animator.playhead.move(i);
            
            const currentTime = map.animator.time;
            const progress = ((i / totalFrames) * 100).toFixed(1);
            
            console.log(`🕐 Time: ${currentTime.toLocaleTimeString()}, Progress: ${progress}%`);
        }, i * 1000);
    }
}

// Auto-run demonstrations (uncomment to use)
// setTimeout(demonstratePlaybackControls, 2000);
// setTimeout(demonstrateFrameControl, 15000);
```

## Complete Working Example

```javascript
import pangea from '@twc/pangea-web/mapbox';

const MAPBOX_ACCESS_TOKEN = 'your-mapbox-access-token-here';
const SUN_API_TOKEN = 'your-sun-api-token-here';

async function createAnimatedWeatherMap() {
    try {
        console.log('🚀 Creating animated weather map...');
        
        // Create map
        const map = await pangea.mapbox.MapboxViewport.create('map', MAPBOX_ACCESS_TOKEN, {
            geoCenter: [-98.5795, 39.8283], // Center of USA
            zoomLevel: 4,
            basemap: 'mapbox://styles/mapbox/dark-v10'
        });
        
        console.log('✅ Base map created');
        
        // Wait for map to load
        await new Promise(resolve => map.loaded.add({ handler: resolve }));
        
        // Create animated satellite layer
        console.log('🛰️ Adding animated satellite layer...');
        const satradSource = new pangea.sources.SunRasterSource("satradFcst", SUN_API_TOKEN, {});
        
        satradSource.ready.once({
            handler: e => {
                console.log('📡 Satellite data loaded');
                
                // Configure animator with actual data
                const framesLength = satradSource.currentSequence.times.length;
                const startTime = satradSource.currentSequence.times[framesLength - 1].instant;
                
                map.animator.startTime = startTime;
                map.animator.storyboard = [{
                    frames: framesLength,
                    span: framesLength * 900000 // 15 minutes per frame
                }];
                map.animator.playhead.move(framesLength);
                
                // CRITICAL: Connect before adding layer
                satradSource.animator = map.animator;
                
                // Add layer
                const layer = new pangea.layers.RasterLayer(satradSource, { 
                    id: "SatelliteRadar",
                    opacity: 0.7
                });
                map.layers.add(layer);
                
                // Set up UI and events
                createAnimationControls();
                setupAnimationEventListeners();
                updateControlsWithAnimatorData();
                
                console.log('🎉 Animated weather map ready!');
                console.log(`📊 Animation: ${framesLength} frames, latest: ${startTime.toISOString()}`);
                
                // Auto-start after 2 seconds
                setTimeout(() => {
                    console.log('▶️ Auto-starting animation...');
                    map.animator.play();
                }, 2000);
            }
        });
        
        // Handle source errors
        if (satradSource.error) {
            satradSource.error.once({
                handler: (error) => {
                    console.error('❌ Failed to load satellite data:', error);
                }
            });
        }
        
    } catch (error) {
        console.error('❌ Error creating animated map:', error);
    }
}

// Initialize the animated map
createAnimatedWeatherMap();
```

## Key Principles Summary

### ✅ Correct Animation Setup:
1. **Create source first** - SunRasterSource, SunGridSource, etc.
2. **Wait for source.ready** - Use `.once()` handler
3. **Configure animator** - Set startTime, storyboard, playhead
4. **Connect source to animator** - `source.animator = map.animator`
5. **Create and add layer** - Layer inherits animation from source
6. **Set up event listeners** - Use `.once()` pattern, not `.add()`

### ❌ Common Mistakes:
- Using `.add()` instead of `.once()` for events
- Configuring animator after adding layer
- Not connecting source to animator
- Using `map.animator.import()` instead of direct property setting
- Forgetting to wait for source.ready

### 🎯 Best Practices:
- Always use comprehensive logging for debugging
- Handle both source.ready and source.error events
- Create UI controls that reflect actual animator state
- Use appropriate timing (15min for radar, 1hr for snow)
- Test with real SUN API data, not mock data
- **Mobile Optimization**: Sanitize complex objects before postMessage transmission
- **Event Pattern**: Use `{ handler: function }` object pattern for EventSource

The animation system is particularly powerful for:
- **Weather visualizations** (radar, satellite, forecasts)
- **Time-series data** (traffic, environmental monitoring)
- **Historical playback** (events over time)
- **Real-time streaming** (live data updates)

All examples include comprehensive logging to help you understand the animation flow and debug any issues during development.
