# Pangea iOS User Guide

## Overview

The Pangea iOS SDK provides a comprehensive mapping solution with native iOS integration. This guide covers the essential features and usage patterns for integrating Pangea into your iOS application using both UIKit and SwiftUI. The SDK handles all mapping functionality internally, so you don't need to configure Mapbox directly.

## Setup and Installation

### Requirements

- iOS 12.0 or later
- Xcode 12.0 or later
- Swift 5.0 or later

### Swift Package Manager

Add Pangea to your project using Swift Package Manager:

1. In Xcode, go to File → Add Package Dependencies
2. Enter the repository URL: `https://github.com/TheWeatherCompany/pangea-mobile-packages.git`
3. Select the version (e.g., `5.8.0` or later)
4. Choose either `PangeaMapbox-Debug` or `PangeaMapbox-Release` product

### Authentication

The Pangea SDK handles authentication internally. You don't need to provide separate Mapbox access tokens when using Pangea - the SDK manages map tiles and authentication automatically through the Pangea service.

### Import Statements

```swift
import PangeaMapView
import PangeaMapbox
import PangeaCore
```

## Basic MapView Setup

### UIKit Integration

#### Storyboard Setup

1. Add a `UIView` to your storyboard
2. Set the class to `com.weather.pangea.map.MapView`
3. Set the module to `PangeaMapView`
4. Configure IBInspectable properties in Interface Builder:
   - `latitude`: Initial latitude (default: 0.0)
   - `longitude`: Initial longitude (default: 0.0)
   - `zoomLevel`: Initial zoom level (default: 3.0)
   - `bearing`: Initial bearing (default: 0.0)
   - `pitch`: Initial pitch (default: 0.0)
   - `basemap`: Basemap style URL (e.g., "mapbox://styles/mapbox/streets-v11")

#### Programmatic Setup

```swift
import UIKit
import PangeaMapView
import PangeaMapbox

class ViewController: UIViewController {
    @IBOutlet weak var mapView: com.weather.pangea.map.MapView!
    private var viewport: com.weather.pangea.map.MapViewport?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupMapView()
    }
    
    private func setupMapView() {
        createViewportFor(view: mapView) { viewport in
            self.viewport = viewport
            self.configureMap(viewport: viewport)
        }
    }
    
    private func configureMap(viewport: com.weather.pangea.map.MapViewport) {
        // Configure your map here
        let geoCenter = com.weather.pangea.geography.GeoPoint(
            longitude: -95.0, 
            latitude: 39.0
        )
        viewport.center(geoPoint: geoCenter)
        viewport.zoom(zoomLevel: 4.0)
    }
}
```

### SwiftUI Integration

#### MapView Wrapper

```swift
import SwiftUI
import PangeaMapView
import PangeaMapbox

struct MapViewContainer: UIViewRepresentable {
    typealias UIViewType = com.weather.pangea.map.MapView
    @ObservedObject var viewportManager: PangeaViewportManager
    
    func makeUIView(context: Context) -> com.weather.pangea.map.MapView {
        let mapView = viewportManager.createMap()
        return mapView
    }
    
    func updateUIView(_ uiView: com.weather.pangea.map.MapView, context: Context) {
        // Handle updates
    }
}
```

#### ViewModel

```swift
import Combine
import PangeaMapView
import PangeaMapbox

class PangeaViewportManager: ObservableObject {
    @Published var viewport: com.weather.pangea.map.MapViewport?
    @Published var mapView: com.weather.pangea.map.MapView?
    
    func createMap() -> com.weather.pangea.map.MapView {
        let mapView = com.weather.pangea.map.MapView(frame: CGRect.zero)
        
        createViewportFor(view: mapView) { viewport in
            DispatchQueue.main.async {
                self.viewport = viewport
            }
        }
        
        self.mapView = mapView
        return mapView
    }
}
```

#### Usage in SwiftUI

```swift
struct ContentView: View {
    @StateObject private var viewportManager = PangeaViewportManager()
    
    var body: some View {
        MapViewContainer(viewportManager: viewportManager)
            .ignoresSafeArea(edges: [.bottom])
    }
}
```

## MapView Configuration

### IBInspectable Properties

Configure these properties in Interface Builder or programmatically:

```swift
// Location and viewport
mapView.latitude = 40.7128
mapView.longitude = -74.0060
mapView.zoomLevel = 12.0
mapView.bearing = 0.0
mapView.pitch = 0.0

// Zoom constraints
mapView.minimumZoom = 1.0
mapView.maximumZoom = 18.0

// Style and localization
mapView.basemap = "mapbox://styles/mapbox/streets-v11"
mapView.locale = "en_US"
mapView.worldView = "US"
mapView.projection = "mercator"

// Bounds and timeouts
mapView.maximumBounds = "-180,-85,180,85"
mapView.loadingTimeoutMs = 5000
```

### Viewport Creation

```swift
func createViewportFor(view: com.weather.pangea.map.MapView, 
                      onComplete: @escaping (com.weather.pangea.map.MapViewport) -> Void) {
    view.basemap = "mapbox://styles/mapbox/streets-v11"
    
    let mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
    
    // Configure fonts if needed
    if let fontMap = getFontMap() {
        mapboxOptions.fonts = mapboxOptions.fonts.merging(fontMap) { (_, new) in new }
    }
    
    com.weather.pangea.mapbox.MapboxViewport.companion.create(
        mapView: view,
        mapboxOptions: mapboxOptions,
        callback: onComplete
    )
}
```

## Camera Controls

### Basic Camera Movement

```swift
class CameraControlViewController: UIViewController {
    private var viewport: com.weather.pangea.map.MapViewport?
    
    @IBAction func moveCameraUp(_ sender: Any) {
        viewport?.moveUp()
    }
    
    @IBAction func zoomIn(_ sender: Any) {
        viewport?.zoomIn()
    }
    
    @IBAction func rotateLeft(_ sender: Any) {
        viewport?.rotateLeft()
    }
    
    @IBAction func tiltUp(_ sender: Any) {
        viewport?.tiltUp()
    }
}
```

### Programmatic Camera Movement

```swift
// Move to specific location
let location = com.weather.pangea.geography.GeoPoint(longitude: -74.0060, latitude: 40.7128)
viewport.center(geoPoint: location)

// Zoom to specific level
viewport.zoom(zoomLevel: 12.0)

// Complex camera movement with animation
let animationOptions = com.weather.pangea.time.AnimationOptions()
animationOptions.duration = com.weather.pangea.time.TimeSpan(seconds: 2.0)
animationOptions.easing = com.weather.pangea.time.AnimationEasing.EASE_IN_OUT

viewport.center(geoPoint: location, animation: animationOptions)
```

### Fit to Bounds

```swift
let bounds = com.weather.pangea.geography.GeoBounds(
    west: -74.1,
    south: 40.6,
    east: -73.9,
    north: 40.8
)

let padding = com.weather.pangea.core.Padding(
    left: 50.0,
    top: 50.0,
    right: 50.0,
    bottom: 50.0
)

let animationOptions = com.weather.pangea.time.AnimationOptions(
    duration: com.weather.pangea.time.TimeSpan(seconds: 1.0)
)

viewport.fit(
    bounds: bounds,
    padding: padding,
    animation: animationOptions
)
```

## Event Handling

### Lifecycle Management

```swift
class ViewportMapViewDelegate: NSObject, PangeaMapViewDelegateProtocol {
    private let viewport: com.weather.pangea.map.MapViewport
    
    init(viewport: com.weather.pangea.map.MapViewport) {
        self.viewport = viewport
    }
    
    func onDestroy() {
        viewport.dispose()
    }
    
    func onHidden() {
        viewport.deactivate()
    }
    
    func onVisible() {
        viewport.activate()
    }
}

// Set delegate
mapView.delegate = ViewportMapViewDelegate(viewport: viewport)
```

### Touch Events

```swift
private func setupTouchEvents() {
    // Tap events
    viewport.touch.tap.subscribe { touch in
        let geoPoint = touch.geoPoint
        let screenPoint = touch.screenPoint
        print("Tap at: \(geoPoint.latitude), \(geoPoint.longitude)")
        print("Screen coordinates: \(screenPoint.x), \(screenPoint.y)")
    }
    
    // Long press events
    viewport.touch.longPress.subscribe { touch in
        let geoPoint = touch.geoPoint
        print("Long press at: \(geoPoint.latitude), \(geoPoint.longitude)")
    }
    
    // Touch down/move/up events
    viewport.touch.down.subscribe { touch in
        print("Touch down: \(touch.touchId)")
    }
    
    viewport.touch.move.subscribe { touch in
        print("Touch move: \(touch.touchId)")
    }
    
    viewport.touch.up.subscribe { touch in
        print("Touch up: \(touch.touchId)")
    }
}
```

### Camera Movement Events

```swift
private func setupCameraEvents() {
    // Subscribe to camera events
    viewport.moved.subscribe { region in
        let geoCenter = region.geoCenter
        print("Camera moved to: \(geoCenter.latitude), \(geoCenter.longitude)")
    }
    
    viewport.zoomed.subscribe { region in
        print("Zoom level: \(region.zoomLevel)")
    }
    
    viewport.rotated.subscribe { region in
        print("Bearing: \(region.bearing)")
    }
    
    viewport.tilted.subscribe { region in
        print("Pitch: \(region.pitch)")
    }
}
```

## Layer Management

### Adding Layers

```swift
class LayerManager {
    private let viewport: com.weather.pangea.map.MapViewport
    
    init(viewport: com.weather.pangea.map.MapViewport) {
        self.viewport = viewport
    }
    
    func addFeatureLayer() {
        // Create feature source
        let features = createSampleFeatures()
        let featureSource = com.weather.pangea.source.feature.StaticFeatureSource(features: features)
        
        // Create feature styler
        let featureStyler = com.weather.pangea.layer.feature.FeatureStyler()
        
        // Create layer
        let featureLayer = com.weather.pangea.layer.feature.FeatureLayer(
            id: "my-features",
            source: featureSource,
            styler: featureStyler
        )
        
        // Add source and layer to viewport
        viewport.sources["feature-source"] = featureSource
        viewport.layers += featureLayer
    }
    
    func addRasterLayer() {
        // Create raster source
        let rasterSource = com.weather.pangea.source.raster.SunRasterSource(
            id: "satellite",
            baseUrl: "https://example.com/tiles",
            tileSize: 256
        )
        
        // Create raster layer
        let rasterLayer = com.weather.pangea.layer.raster.RasterLayer(
            id: "satellite",
            source: rasterSource
        )
        
        // Add to viewport
        viewport.sources["raster-source"] = rasterSource
        viewport.layers += rasterLayer
    }
    
    private func createSampleFeatures() -> [com.weather.pangea.geography.Feature] {
        let point1 = com.weather.pangea.geography.GeoPoint(longitude: -74.0060, latitude: 40.7128)
        let point2 = com.weather.pangea.geography.GeoPoint(longitude: -122.4194, latitude: 37.7749)
        
        let feature1 = com.weather.pangea.geography.Feature(
            id: "1",
            geometry: point1,
            properties: [:]
        )
        
        let feature2 = com.weather.pangea.geography.Feature(
            id: "2",
            geometry: point2,
            properties: [:]
        )
        
        return [feature1, feature2]
    }
}
```

### Layer Properties

```swift
// Layer visibility and opacity
layer.isVisible = true
layer.opacity = 0.8

// Time range for animation
let startTime = com.weather.pangea.time.Instant.now()
let endTime = startTime.plus(duration: com.weather.pangea.time.TimeSpan(hours: 1))
layer.timeRange = com.weather.pangea.time.TimeRange(start: startTime, end: endTime)

// Zoom range
layer.zoomRange = com.weather.pangea.core.DoubleRange(minimum: 5.0, maximum: 15.0)

// Layer slot for ordering
layer.slot = com.weather.pangea.layer.LayerSlot.MIDDLE
```

### Layer List Management

```swift
class LayerListController: UITableViewController {
    private var viewport: com.weather.pangea.map.MapViewport?
    private var layers: [com.weather.pangea.layer.Layer] = []
    
    func addLayer(layer: com.weather.pangea.layer.Layer) {
        viewport?.layers += layer
        layers.append(layer)
        tableView.reloadData()
    }
    
    func removeLayer(at index: Int) {
        let layer = layers[index]
        viewport?.layers.remove(layer: layer)
        layers.remove(at: index)
        tableView.reloadData()
    }
    
    func moveLayer(from: Int, to: Int) {
        let layer = layers.remove(at: from)
        layers.insert(layer, at: to)
        // Update layer order in viewport
        viewport?.layers.reorder(layers)
    }
}
```

## User Interactions

### Behavior Configuration

```swift
// Configure map behaviors
viewport.behaviors.dragging = true
viewport.behaviors.zooming = true
viewport.behaviors.rotating = true
viewport.behaviors.tilting = true

// Disable all interactions
viewport.behaviors.disable()

// Enable specific interactions
viewport.behaviors.enable(
    dragging: true,
    zooming: true,
    rotating: false,
    tilting: false
)
```

### Custom Gesture Handling

```swift
class PangeaTouchGestureRecognizer: UIGestureRecognizer {
    private let touchDelegate: com.weather.pangea.map.PangeaTouchDelegate
    
    init(touchDelegate: com.weather.pangea.map.PangeaTouchDelegate) {
        self.touchDelegate = touchDelegate
        super.init(target: nil, action: nil)
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        for touch in touches {
            let location = touch.location(in: view)
            let screenPoint = com.weather.pangea.core.Point(x: location.x, y: location.y)
            touchDelegate.touchBegan(screenPoint: screenPoint, touchId: touch.hash)
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        for touch in touches {
            let location = touch.location(in: view)
            let screenPoint = com.weather.pangea.core.Point(x: location.x, y: location.y)
            touchDelegate.touchMoved(screenPoint: screenPoint, touchId: touch.hash)
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        for touch in touches {
            let location = touch.location(in: view)
            let screenPoint = com.weather.pangea.core.Point(x: location.x, y: location.y)
            touchDelegate.touchEnded(screenPoint: screenPoint, touchId: touch.hash)
        }
    }
}
```

## Advanced Features

### Map Capture

```swift
// Capture map as image
viewport.capture { image in
    if let image = image {
        // Save to photo library
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        
        // Or share via activity controller
        let activityController = UIActivityViewController(
            activityItems: [image],
            applicationActivities: nil
        )
        present(activityController, animated: true)
    }
}
```

### Basemap Management

```swift
// Change basemap
viewport.changeBasemap(basemap: "mapbox://styles/mapbox/satellite-v9")

// With completion handler
viewport.changeBasemap(basemap: "mapbox://styles/mapbox/dark-v10") {
    print("Basemap changed")
}
```

### Coordinate Conversion

```swift
// Convert between geographic and screen coordinates
let geoPoint = com.weather.pangea.geography.GeoPoint(longitude: -74.0060, latitude: 40.7128)
let screenPoint = viewport.convertToScreen(geoPoint: geoPoint)

let backToGeo = viewport.convertToGeo(screenPoint: screenPoint)

// Get current map state
let currentCenter = viewport.geoCenter
let currentZoom = viewport.zoomLevel
let currentBounds = viewport.bounds
```

### Overlay Management

```swift
// Add image marker
let geoPoint = com.weather.pangea.geography.GeoPoint(longitude: -74.0060, latitude: 40.7128)
let imageMarker = com.weather.pangea.overlay.ImageMarker(
    id: "marker1",
    geoPoint: geoPoint,
    image: UIImage(named: "marker-icon")!
)
viewport.overlays += imageMarker

// Add text marker
let textMarker = com.weather.pangea.overlay.TextMarker(
    id: "text1",
    geoPoint: geoPoint,
    text: "New York City",
    style: com.weather.pangea.visual.TextStyle(
        font: UIFont.systemFont(ofSize: 16),
        color: com.weather.pangea.visual.Color.BLACK
    )
)
viewport.overlays += textMarker

// Add line path
let points = [
    com.weather.pangea.geography.GeoPoint(longitude: -74.0060, latitude: 40.7128),
    com.weather.pangea.geography.GeoPoint(longitude: -122.4194, latitude: 37.7749)
]
let linePath = com.weather.pangea.overlay.LinePath(
    id: "line1",
    points: points,
    style: com.weather.pangea.visual.LineStyle(
        color: com.weather.pangea.visual.Color.BLUE,
        width: 3.0
    )
)
viewport.overlays += linePath
```

## Memory Management

### Disposal

```swift
class MapViewController: UIViewController {
    private var viewport: com.weather.pangea.map.MapViewport?
    private var subscriptions: [com.weather.pangea.core.Subscription] = []
    
    deinit {
        // Dispose of subscriptions
        subscriptions.forEach { $0.dispose() }
        subscriptions.removeAll()
        
        // Dispose of viewport
        viewport?.dispose()
    }
    
    private func setupSubscriptions() {
        // Store subscriptions for proper cleanup
        let subscription = viewport?.moved.subscribe { region in
            // Handle movement
        }
        
        if let subscription = subscription {
            subscriptions.append(subscription)
        }
    }
}
```

### ViewModel Cleanup

```swift
class MapViewModel: ObservableObject {
    @Published var viewport: com.weather.pangea.map.MapViewport?
    private var subscriptions: [com.weather.pangea.core.Subscription] = []
    
    deinit {
        cleanup()
    }
    
    func cleanup() {
        subscriptions.forEach { $0.dispose() }
        subscriptions.removeAll()
        viewport?.dispose()
    }
}
```

## Best Practices

1. **Always dispose**: Call `dispose()` on viewport and subscriptions when done
2. **Use lifecycle delegate**: Implement `PangeaMapViewDelegateProtocol` for proper lifecycle management
3. **Handle async creation**: Viewport creation is asynchronous, handle accordingly
4. **Memory management**: Clean up subscriptions and view models in `deinit`
5. **SwiftUI integration**: Use `@StateObject` for view models and `@ObservedObject` for data binding
6. **Thread safety**: UI updates should be performed on the main thread
7. **Namespace awareness**: Always use full namespaces for better code clarity

## Common Patterns

### Loading States

```swift
class MapViewModel: ObservableObject {
    @Published var isLoading = true
    @Published var viewport: com.weather.pangea.map.MapViewport?
    @Published var error: Error?
    
    func createMap() -> com.weather.pangea.map.MapView {
        let mapView = com.weather.pangea.map.MapView(frame: CGRect.zero)
        
        createViewportFor(view: mapView) { viewport in
            DispatchQueue.main.async {
                self.viewport = viewport
                self.isLoading = false
            }
        }
        
        return mapView
    }
}
```

### Error Handling

```swift
func createViewportFor(view: com.weather.pangea.map.MapView, 
                      onComplete: @escaping (com.weather.pangea.map.MapViewport?) -> Void) {
    let mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
    
    do {
        com.weather.pangea.mapbox.MapboxViewport.companion.create(
            mapView: view,
            mapboxOptions: mapboxOptions
        ) { viewport in
            onComplete(viewport)
        }
    } catch {
        print("Failed to create viewport: \(error)")
        onComplete(nil)
    }
}
```

## Quick Start Example

Here's a complete minimal example to get you started:

```swift
import UIKit
import PangeaMapView
import PangeaMapbox

class ViewController: UIViewController {
    @IBOutlet weak var mapView: com.weather.pangea.map.MapView!
    private var viewport: com.weather.pangea.map.MapViewport?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupMap()
    }
    
    private func setupMap() {
        // Set initial map properties
        mapView.latitude = 37.7749
        mapView.longitude = -122.4194
        mapView.zoomLevel = 10.0
        mapView.basemap = "mapbox://styles/mapbox/streets-v11"
        
        // Create viewport
        let mapboxOptions = com.weather.pangea.mapbox.MapboxViewportOptions()
        
        com.weather.pangea.mapbox.MapboxViewport.companion.create(
            mapView: mapView,
            mapboxOptions: mapboxOptions
        ) { viewport in
            self.viewport = viewport
            self.setupEvents()
            print("Map is ready!")
        }
    }
    
    private func setupEvents() {
        // Set up touch events
        viewport?.touch.tap.subscribe { touch in
            let geoPoint = touch.geoPoint
            print("Tap at: \(geoPoint.latitude), \(geoPoint.longitude)")
        }
        
        // Set up camera events
        viewport?.moved.subscribe { region in
            print("Camera moved to: \(region.geoCenter)")
        }
    }
    
    deinit {
        viewport?.dispose()
    }
}
```

This comprehensive guide provides you with everything needed to get started with the Pangea iOS SDK. All examples include full namespace references to make implementation easier and less error-prone.