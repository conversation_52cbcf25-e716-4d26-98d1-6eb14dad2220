# Tech Specification

## Important Locations
- `app/theme/ThemeContext.tsx` - Contains the theme context and color definitions
- `app/i18n/` - Contains translation files and i18n configuration
- `app/components/` - Reusable components
- `app/(tabs)/` - Screens for the tab-based navigation
- `app/_layout.tsx` - Root layout with splash screen and theme provider
- `app/schemas/` - Contains JSON schemas for data definitions

## Libraries

- use react-query for calling services
- use i18n for translations (all string constants should be in translation files)

## Style
- color schemes and fonts (including gradients) must come from a Context provider for the app and be managed in a single place

## Components

### Home Screen
- Hero component - Shows the current location and current weather conditions
- Recomendation - a text component with a title and a text area that provides an AI summary of recomendations for the day at the current location.
- Resort - a card component that displays the resort name, current snowfall, distance from the user, and a checckout button and a button to view more details (that opens the resort detail screen).
- Hourly - a card component that displays the hourly forecast for the current location.
- Current Radar - a card component that displays a map of the current radar for the current location.

## External APIs

### Resorts API
- Endpoint: `https://api.weather.com/v3/location/near`
- Used to fetch a list of nearby ski resorts for the Today screen (and potentially other screens).
- Requires the following parameters:
  - `geocode`: Latitude,Longitude string (e.g., '33.74,-84.39') for the user's location
  - `product`: Always set to `ski`
  - `format`: Always set to `json`
  - `apiKey`: The value of `SUN_API_TOKEN` from the app's environment variables
- The API returns a list of nearby resorts with fields such as name, id, location, distance, latitude, and longitude.
- The app uses this API to select which resorts to show on the Today screen, based on the user's current or selected location.