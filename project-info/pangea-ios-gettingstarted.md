## Getting Started: iOS

There are a few things you'll need to do to get started:

1. Determine which engine your product is going to be running. (Mapbox currently the only one ready).
2. Acquire any credentials required for your engine.
3. Acquire any credentials required for you data-providers.
4. [Add Pangea](https://pangea.weather.com/pages/integration/ios/setup) to project.

## Mapbox

[**Mapbox**](https://docs.mapbox.com/ios/maps/guides/) is an iOS library for building maps and iOS apps with Mapbox's modern mapping technology. You can use it to display Mapbox maps in a view, add user interactivity, and customize the map experience in your application.

To use the Mapbox engine with pangea, the `PangeaMapbox` Swift Package needs to be added to the app. `MapboxViewport` will then be the viewport to create.

## Create MapView

Interaction with Pangea requires a `MapView` instance be added to your view. This view can be created in a Storyboard or manually for SwiftUI.

### Storyboard

---

The `MapView` can be added to your existing storyboard just like any other view.

#### Create

Add a `UIView` to your view. Change the type to `PangeaMapView` and create IBOutlet in ViewController. Create the `MapViewport` in your `ViewController`.

```swift
class ViewController : UIViewController {
    override func viewDidLoad() {
        MapboxViewport.companion.create(mapView: mapView, accessToken: getSecret(key: "Mapbox Key")) { map in
        }
    }
}
```

#### Configure

Options for the initial state of the map can be configured either inside the Storyboard or in code for dynamic options.

**In Storyboard**:

The view should have a set of properties that can be edited to set the initial value. Not currently working.

**In Code**:

```swift
override func viewDidLoad() {
    mapView.basemap = "mapbox://styles/mapbox/streets-v11"
    mapView.maximumBounds = nil
    mapView.latitude = 0.0
    mapView.longitude = 0.0
    mapView.minimumZoom = 0.0
    mapView.maximumZoom = 25.0
    mapView.zoomLevel = 3.0
    mapView.minimumPitch = 0.0
    mapView.maximumPitch = 85.0
    mapView.pitch = 0.0
    mapView.bearing = 0.0
    mapView.mapMoveIdleTimeout = 64.0
    mapView.loadingTimeout = 5000

    MapboxViewport.companion.create(mapView: mapView, accessToken: getSecret(key: "Mapbox Key")) { map in
    }
}
```

Changes made to the view's options after calling `MapboxViewport.companion.create` will be ignored.

### SwiftUI

---

Pangea can easily be used inside a SwiftUI app by using `UIViewRepresentable`.

#### Create

```swift
class PangeaViewportManager: ObservableObject {
    @Published var map: MapViewport? = nil
    @Published var mapView: MapView? = nil

    func createMap() -> MapView {
        let mapView = MapView(frame: CGRect.zero)

        MapboxViewport.companion.create(mapView: mapView, accessToken: getSecret(key: "Mapbox Key")) { map in
            self.map = map
        }

        self.mapView = mapView
        return mapView
    }
}

struct MapViewContainer: UIViewRepresentable {
    typealias UIViewType = MapView
    @ObservedObject var viewportManager: PangeaViewportManager

    func makeUIView(context: Context) -> MapView {
        let mapView = viewportManager.createMap()
        return mapView
    }

    func updateUIView(_ uiView: MapView, context: Context) {
    }
}
```

#### Configure

Options for the initial state of the map can be configured using properties of `MapView`.

```swift
mapView.basemap = "mapbox://styles/mapbox/streets-v11"
mapView.maximumBounds = nil
mapView.latitude = 0.0
mapView.longitude = 0.0
mapView.minimumZoom = 0.0
mapView.maximumZoom = 25.0
mapView.zoomLevel = 3.0
mapView.minimumPitch = 0.0
mapView.maximumPitch = 85.0
mapView.pitch = 0.0
mapView.bearing = 0.0
mapView.mapMoveIdleTimeout = 64.0
mapView.loadingTimeout = 5000
```

## Show Radar

### Create Source

Data to display on a map is provided by a `Source`. The are specific `Sources` for different types of data as well as the data-provider for the source. When you add a `Source` to a `MapViewport` the source will have the same lifecycle as the `MapView` that was used to create the `MapViewport`.

Most sources require an `HttpClient` to be able to access data. A single instance of this should be created for the lifetime of the app.

```swift
let httpClient = UrlSessionHttpEngineKt.createHttpClient()
```

Then create the source with the `HttpClient`.

```swift
MapboxViewport.compainion.create(mapView, getString(R.string.mapboxToken) { map ->
    let radarSource = SunRasterSource(productKey: "twcRadarMosaic", productSet: "myProductSet", apiKey: getSecret(key: "Mapbox Key"),
                                      httpClient: httpClient)
    // Something got confused with id being generic in Cache and it not recognizing that Source.id (NSString) is compatible the id parameter of type NSString
    // This forces a cast to make the compiler happy
    map.sources.add(id: radarSource.id as NSString, item: radarSource)
}
```

### Create Layer

Layers are a way to visualize the data from a `Source` on a map.

```swift
MapboxViewport.companion.create(mapView, getString(R.string.mapboxToken) { map ->
    val radarLayer = RasterLayer(radarSource)
    // layers is of type Stack that does not have id being generic so no cast is needed
    map.layers.add(radarLayer)
}
```

## Guidelines

The Pangea SDK is written in Kotlin which produces some API oddities

#### Types

Primitive types have Kotlin equivalents. There's `KotlinDouble` and `KotlinInt`. Normally you can just pass normal Swift types to things that take those Kotlin types, but when dealing with nullable versions of them, conversion is often required. For example, `ViewOptions.zoomLevel` is a `Double?`, so to assign a value to it, a conversion is required.

```swift
viewOptions.zoomLevel = KotlinDouble(double: zoom)
```

Kotlin is not able to use Swift ranges and has their own range classes. Pangea provides has three Range classes that get used `DoubleRange`, `IntRange`, and `ComparableRange`.

```swift
self.viewport.zoomRange = DoubleRange(start: zoomRange.lowerBound, endInclusive: zoomRange.upperBound)
```

#### Documentation

Inline documentation is available within Xcode, but this is still an experimental feature and will sometimes render strangely. You may want to consult the online [reference](https://pangea.weather.com/pages/reference/mobile/) instead.

#### Visibility and method choices

There are several classes, functions, and properties that are not intended to be used by client apps. There are conventions to make this obvious when accessed from Kotlin or Java that get lost with Swift. Work will be done in the future to highlight this, but in some cases the online reference guide can indicate if they shouldn't be used. While these are published as part of the public API, most of them will be classes that most apps would have no need to use and would probably only find them by hunting for them. The different recipes and samples provided by Pangea will not be using them.

Kotlin has `protected` visibility which Objective-C lacks. These functions and properties are either only intended to be accessed or overridden by children classes. In example of this is `MapViewport.moveCamera()`. This is a function intended to be called by `MapViewport.move()` and should not be called directly. These functions will have something like `@note This method has protected visibility in Kotlin source and is intended only for use by subclasses.` in their documentation. Unfortunately this is not visible from the quick help.

There are also classes and functions in packages with the name `internal`. Objective-C doesn't have packages, so the only way to know they are internal is to see that they have no documentation online. They should be annotated with `@InternalPangeaApi`. These will have documentation like:

```
/**
 * @note annotations
 *   com.weather.pangea.core.MainThread
 *   com.weather.pangea.core.NotThreadSafe
 *   com.weather.pangea.core.InternalPangeaApi
 */
```

There is also a `@ExperimentalPangeaApi` to indicate an API that can be used but may change in the future. These will have documentation like:

```
/**
 * @note annotations
 *   com.weather.pangea.core.MainThread
 *   com.weather.pangea.core.NotThreadSafe
 *   com.weather.pangea.core.ExperimentalPangeaApi
 */
```

Kotlin has `suspend` functions for asynchronous programming. These can only be called from a Kotlin coroutine. The compiler to make things easier for use in Objective-C replaces these functions with a function that takes a callback and are compatible with Swift's async/await functionality. The problem with this is that you lose the ability to cancel (or stop waiting for) the work that was being done. Pangea has a convention of creating several `suspend` functions that have `await` prefixed to their names. These functions have mirror versions that do not have `await` in the name, take callbacks, and return `Disposables`. You should prefer to use the functions without `await` in the name if you need to support cancellation.

#### Options

The SDK makes use of default arguments for a lot of the API (especially object constructors). These default arguments are not available in Objective-C. To make API easier to consume most of these functions will have an overload that takes a mutable `Options` class. Using this will use the correct defaults with the ability to change only the options that are desired. These `Options` classes can be configured using a fluent interface or just with property setting.

```swift
let layer = RasterLayer(
    source: source,
    options: RasterLayerOptions()
        .withBlendingEnabled(blendingEnabled: true)
        .withId(id: "myRadar")
)

options.blendingEnabled = true
options.id = "myRadar"
let otherLayer = RasterLayer(source: source, options: options)
```

#### Data classes

Kotlin `data` classes autogenerate some functions. Some like `componentX()` are for destructing and will return properties of the class based on declaration order. Since Swift cannot use the Kotlin destruction, there is no reason to call these functions. Using the properties directly will always be cleaner.

The other function of interest is `copy`, which gets renamed `doCopy`. This function relies on default parameters to be useful. It allows for creating a copy of the object with everything keeping the old value except for the ones you specify using named arguments. These functions will only be backwards compatible safe from Kotlin, so they should not be used from Swift.