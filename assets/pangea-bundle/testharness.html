<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pangea Map Test Harness</title>
    <style>
        :root {
            --primary-color: #007bff;
            --primary-hover-color: #0056b3;
            --light-gray: #f0f0f0;
            --medium-gray: #ddd;
            --dark-gray: #333;
            --border-color: #ccc;
            --success-bg: #d4edda;
            --success-color: #155724;
            --error-bg: #f8d7da;
            --error-color: #721c24;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; display: flex; height: 100vh; margin: 0; background-color: #fafafa; }
        #controls { 
            width: 350px; 
            padding: 20px; 
            box-sizing: border-box; 
            overflow-y: auto; 
            background-color: #fff;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            z-index: 10;
        }
        #map-container { flex-grow: 1; }
        iframe { width: 100%; height: 100%; border: none; }
        .control-group { 
            border-bottom: 1px solid var(--medium-gray); 
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .control-group:last-child {
            border-bottom: none;
        }
        h1, h2 { color: var(--dark-gray); }
        h1 { font-size: 1.5em; margin-bottom: 20px; text-align: center;}
        h2.collapsible-header { 
            font-size: 1.2em; 
            cursor: pointer;
            position: relative;
            margin-bottom: 10px;
            padding-bottom: 5px;
            user-select: none;
        }
        h2.collapsible-header::after {
            content: '+';
            font-size: 1.5em;
            color: var(--primary-color);
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s;
        }
        .control-group.active h2.collapsible-header::after {
            content: '−';
            transform: translateY(-50%) rotate(180deg);
        }
        .collapsible-content {
            padding-top: 10px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }
        .control-group.active .collapsible-content {
            max-height: 1500px; /* Adjust as needed */
        }

        label { display: block; margin-bottom: 8px; font-weight: 600; font-size: 0.9em; }
        input[type="text"], input[type="number"], select, textarea { 
            width: 100%; 
            padding: 8px; 
            margin-bottom: 12px; 
            border: 1px solid var(--border-color); 
            border-radius: 4px; 
            box-sizing: border-box;
            background-color: #fff;
        }
        button { 
            width: 100%; 
            padding: 12px; 
            background-color: var(--primary-color); 
            color: white; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 1em;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        button:hover { background-color: var(--primary-hover-color); }
        .status-panel { 
            padding: 12px; 
            margin-bottom: 15px; 
            border-radius: 4px; 
            font-weight: bold;
            text-align: center;
        }
        .status-panel.success {
            background-color: var(--success-bg);
            color: var(--success-color);
        }
        .status-panel.error {
            background-color: var(--error-bg);
            color: var(--error-color);
        }
        #layer-controls div {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        #layer-controls label {
            margin-bottom: 0;
            margin-left: 8px;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <div id="controls">
        <h1>Pangea Map Controls 🗺️</h1>
        <div id="status-panel" class="status-panel" style="display: none;"></div>
        
        <div class="control-group active">
            <h2 class="collapsible-header">API Tokens</h2>
            <div class="collapsible-content">
                <label for="mapboxToken">Mapbox Token:</label>
                <input type="text" id="mapboxToken" placeholder="pk.ey...">
                <label for="sunApiToken">SUN API Token:</label>
                <input type="text" id="sunApiToken" placeholder="Your SUN API token">
                <button id="loadMapBtn">Load/Reload Map</button>
            </div>
        </div>

        <div class="control-group">
            <h2 class="collapsible-header">Set User Location</h2>
            <div class="collapsible-content">
                <label for="latitude">Latitude:</label>
                <input type="number" id="latitude" value="40.7128">
                <label for="longitude">Longitude:</label>
                <input type="number" id="longitude" value="-74.0060">
                <button id="setLocationBtn">Set & Fly to Location</button>
            </div>
        </div>
        
        <div class="control-group">
            <h2 class="collapsible-header">Map Layers</h2>
            <div class="collapsible-content" id="layer-controls">
                <div>
                    <input type="checkbox" id="satradLayer" name="layers" value="satrad">
                    <label for="satradLayer">Satellite/Radar</label>
                </div>
                <div>
                    <input type="checkbox" id="snowLayer" name="layers" value="snow">
                    <label for="snowLayer">Snow Accumulation</label>
                </div>
                <div>
                    <input type="checkbox" id="resortsLayer" name="layers" value="resorts">
                    <label for="resortsLayer">Resort Locations</label>
                </div>
            </div>
        </div>

        <div class="control-group">
            <h2 class="collapsible-header">Map Style</h2>
            <div class="collapsible-content">
                <label for="basemap-select">Choose a map style:</label>
                <select id="basemap-select">
                    <option value="mapbox://styles/mapbox/streets-v12">Streets</option>
                    <option value="mapbox://styles/mapbox/outdoors-v12">Outdoors</option>
                    <option value="mapbox://styles/mapbox/light-v11">Light</option>
                    <option value="mapbox://styles/mapbox/dark-v11">Dark</option>
                    <option value="mapbox://styles/mapbox/satellite-v9">Satellite</option>
                    <option value="mapbox://styles/mapbox/satellite-streets-v12">Satellite Streets</option>
                </select>
            </div>
        </div>

        <div class="control-group">
            <h2 class="collapsible-header">Basic Map Operations</h2>
            <div class="collapsible-content">
                <div>
                    <input type="checkbox" id="animateCheckbox">
                    <label for="animateCheckbox">Animate operations (2s)</label>
                </div>
                <hr>
                
                <strong>Center</strong>
                <label for="centerLon">Longitude:</label>
                <input type="number" id="centerLon" value="-122.4194">
                <label for="centerLat">Latitude:</label>
                <input type="number" id="centerLat" value="37.7749">
                <button id="setCenterBtn">Set Center</button>
                <hr>

                <strong>Zoom</strong>
                <label for="zoomLevel">Zoom Level:</label>
                <input type="number" id="zoomLevel" value="12" step="0.5">
                <button id="setZoomBtn">Set Zoom</button>
                <div style="display: flex; gap: 5px; margin-top: 5px;">
                    <button id="zoomInBtn">Zoom In</button>
                    <button id="zoomOutBtn">Zoom Out</button>
                </div>
                <hr>

                <strong>Bearing (Rotation)</strong>
                <label for="bearing">Bearing (°):</label>
                <input type="number" id="bearing" value="0">
                <button id="setBearingBtn">Set Bearing</button>
                <hr>

                <strong>Pitch (Tilt)</strong>
                <label for="pitch">Pitch (°):</label>
                <input type="number" id="pitch" value="0">
                <button id="setPitchBtn">Set Pitch</button>
                <hr>

                <strong>Pan</strong>
                <label for="panX">X offset (px):</label>
                <input type="number" id="panX" value="100">
                <label for="panY">Y offset (px):</label>
                <input type="number" id="panY" value="50">
                <button id="panBtn">Pan Map</button>
                <hr>

                <strong>Fit to Bounds</strong>
                <label for="bounds">Bounds [W,S,E,N]:</label>
                <input type="text" id="bounds" value="-74.047, 40.683, -73.926, 40.879">
                <label for="padding">Padding (px):</label>
                <input type="number" id="padding" value="20">
                <button id="fitBtn">Fit to Bounds</button>
                <hr>

                <strong>Move</strong>
                <label for="moveOptions">View Options (JSON):</label>
                <textarea id="moveOptions" rows="4" style="width: 100%; font-family: monospace;">{
"geoCenter": [2.3522, 48.8566],
"zoomLevel": 13,
"bearing": 20,
"pitch": 45
}</textarea>
                <button id="moveBtn">Move Map</button>
            </div>
        </div>

        <div class="control-group active">
            <h2 class="collapsible-header">Animation Controls</h2>
            <div class="collapsible-content">
                <div id="animation-controls-container">
                    <p id="animation-placeholder" style="color: #666;">Add an animatable layer (e.g., Sat/Rad) to enable controls.</p>
                    <div id="animation-controls-ui" style="display: none;">
                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <button id="playBtn">Play ▶️</button>
                            <button id="pauseBtn">Pause ⏸️</button>
                        </div>
                        <label for="frame-slider">Frame: <span id="frame-display">N/A</span></label>
                        <input type="range" id="frame-slider" min="1" max="1" value="1" style="width: 100%;">
                        <div style="margin-top: 10px; font-weight: bold;">
                            Animation Time: <span id="time-display">--:--:--</span>
                        </div>
                        <div style="font-size: 0.9em; color: #666;">
                            Status: <span id="animation-status">Inactive</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="control-group active">
            <h2 class="collapsible-header">Event Monitoring 🎧</h2>
            <div class="collapsible-content">
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <input type="checkbox" id="eventsEnabled" checked>
                        <label for="eventsEnabled" style="margin-left: 8px; margin-bottom: 0; font-weight: normal;">Enable Pangea Events</label>
                    </div>
                    <div style="font-size: 0.85em; color: #666; margin-bottom: 10px;">
                        Monitor all map, layer, mouse, and animation events with detailed logging.
                    </div>
                    <button id="clearEventLogBtn" style="width: 100%; padding: 8px; font-size: 0.9em;">Clear Event Log</button>
                </div>
                
                <div id="event-stats" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 10px; font-size: 0.85em;">
                    <div style="font-weight: bold; margin-bottom: 5px;">📊 Event Statistics</div>
                    <div>Map Events: <span id="map-event-count">0</span></div>
                    <div>Layer Events: <span id="layer-event-count">0</span></div>
                    <div>Mouse Events: <span id="mouse-event-count">0</span></div>
                    <div>Animation Events: <span id="animation-event-count">0</span></div>
                </div>
                
                <div id="recent-events" style="background-color: #f1f3f4; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.75em;">
                    <div style="font-weight: bold; margin-bottom: 5px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;">🎯 Recent Events (Last 20)</div>
                    <div id="event-log">
                        <div style="color: #666; font-style: italic;">No events yet...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="map-container">
        <iframe id="map-iframe"></iframe>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const iframe = document.getElementById('map-iframe');
            const pendingRequests = new Map();

            // The wrapper for postMessage to make it async/await-able
            function postMapMessage(type, payload) {
                return new Promise((resolve, reject) => {
                    // Ensure the iframe is loaded before posting a message
                    if (!iframe.contentWindow) {
                        return reject(new Error('Iframe content window is not available.'));
                    }

                    const requestId = `req-${Date.now()}-${Math.random()}`;
                    pendingRequests.set(requestId, { resolve, reject });

                    iframe.contentWindow.postMessage({
                        type,
                        payload,
                        requestId
                    }, '*'); // In a real app, use a specific target origin

                    // Timeout for requests
                    setTimeout(() => {
                        if (pendingRequests.has(requestId)) {
                            pendingRequests.delete(requestId);
                            reject(new Error(`Request to map with type '${type}' timed out.`));
                        }
                    }, 5000); // 5 second timeout
                });
            }

            window.addEventListener('message', (event) => {
                // A basic security check
                if (event.source !== iframe.contentWindow) {
                    return;
                }

                const { requestId, status, data, type } = event.data;

                if (type === 'PangeaMapReady') {
                    console.log('Pangea Map is ready to receive messages.');
                    document.getElementById('setLocationBtn').disabled = false;
                    return;
                }

                if (type === 'animationUpdate') {
                    updateAnimationControls(event.data.data);
                    return;
                }

                if (requestId && pendingRequests.has(requestId)) {
                    const { resolve, reject } = pendingRequests.get(requestId);
                    if (status === 'success') {
                        resolve(data);
                    } else {
                        reject(data);
                    }
                    pendingRequests.delete(requestId);
                }

                // Handle Pangea event messages
                if (type === 'pangeaMapEvent') {
                    addEventToLog('pangeaMapEvent', event.data.event, event.data.data);
                    return;
                }

                if (type === 'pangeaLayerEvent') {
                    addEventToLog('pangeaLayerEvent', event.data.event, event.data.data);
                    return;
                }

                if (type === 'pangeaAnimationEvent') {
                    addEventToLog('pangeaAnimationEvent', event.data.event, event.data.data);
                    return;
                }
            });

            const mapboxTokenInput = document.getElementById('mapboxToken');
            const sunApiTokenInput = document.getElementById('sunApiToken');
            const loadMapBtn = document.getElementById('loadMapBtn');
            const setLocationBtn = document.getElementById('setLocationBtn');
            const layerCheckboxes = document.querySelectorAll('input[name="layers"]');
            const statusPanel = document.getElementById('status-panel');
            const collapsibleHeaders = document.querySelectorAll('.collapsible-header');
            const basemapSelect = document.getElementById('basemap-select');

            const animationControlsUI = document.getElementById('animation-controls-ui');
            const animationPlaceholder = document.getElementById('animation-placeholder');
            const playBtn = document.getElementById('playBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const frameSlider = document.getElementById('frame-slider');
            const frameDisplay = document.getElementById('frame-display');
            const timeDisplay = document.getElementById('time-display');
            const animationStatus = document.getElementById('animation-status');

            function updateAnimationControls(data) {
                if (data.active) {
                    animationControlsUI.style.display = 'block';
                    animationPlaceholder.style.display = 'none';

                    playBtn.disabled = data.isPlaying;
                    pauseBtn.disabled = !data.isPlaying;
                    frameSlider.disabled = false;

                    frameSlider.max = data.totalFrames;
                    frameSlider.value = data.currentFrame;
                    frameDisplay.textContent = `${data.currentFrame} / ${data.totalFrames}`;
                    timeDisplay.textContent = new Date(data.currentTime).toLocaleTimeString();
                    animationStatus.textContent = data.isPlaying ? 'Playing' : 'Paused';
                } else {
                    animationControlsUI.style.display = 'none';
                    animationPlaceholder.style.display = 'block';

                    playBtn.disabled = true;
                    pauseBtn.disabled = true;
                    frameSlider.disabled = true;
                    animationStatus.textContent = 'Inactive';
                    frameDisplay.textContent = 'N/A';
                    timeDisplay.textContent = '--:--:--';
                }
            }

            playBtn.addEventListener('click', () => postMapMessage('playAnimation', {}));
            pauseBtn.addEventListener('click', () => postMapMessage('stopAnimation', {}));
            frameSlider.addEventListener('input', (e) => {
                const frame = parseInt(e.target.value, 10);
                postMapMessage('setAnimationFrame', { frame });
            });

            function showStatus(message, type = 'success') {
                statusPanel.textContent = message;
                statusPanel.className = `status-panel ${type}`;
                statusPanel.style.display = 'block';
                
                // Hide the message after a few seconds
                setTimeout(() => {
                    statusPanel.style.display = 'none';
                    statusPanel.className = 'status-panel';
                }, 5000); // Hide after 5 seconds
            }

            // Generic helper for map operations
            async function performMapOperation(type, payload, successMessage) {
                try {
                    const animate = document.getElementById('animateCheckbox').checked;
                    if (animate) {
                        payload.animationOptions = { duration: 2000 };
                    }
                    console.log(`Sending ${type} request...`, payload);
                    await postMapMessage(type, payload);
                    showStatus(successMessage);
                } catch (error) {
                    console.error(`Failed to ${type}:`, error);
                    showStatus(`Operation failed: ${error.message}`, 'error');
                }
            }

            // Setup collapsible sections
            collapsibleHeaders.forEach(header => {
                header.addEventListener('click', () => {
                    header.parentElement.classList.toggle('active');
                });
            });

            basemapSelect.addEventListener('change', async (event) => {
                const styleUrl = event.target.value;
                await performMapOperation('setBasemap', { styleUrl }, 'Map style updated successfully!');
            });

            // Restore tokens from localStorage if available
            mapboxTokenInput.value = localStorage.getItem('pangea-mapbox-token') || '';
            sunApiTokenInput.value = localStorage.getItem('pangea-sun-api-token') || '';

            loadMapBtn.addEventListener('click', () => {
                const mapboxToken = mapboxTokenInput.value;
                const sunApiToken = sunApiTokenInput.value;

                if (!mapboxToken || !sunApiToken) {
                    showStatus('Please provide both Mapbox and SUN API tokens.', 'error');
                    return;
                }

                // Save tokens to localStorage
                localStorage.setItem('pangea-mapbox-token', mapboxToken);
                localStorage.setItem('pangea-sun-api-token', sunApiToken);
                
                // Disable location button until new map is ready
                setLocationBtn.disabled = true;

                // uncheck all layers
                layerCheckboxes.forEach(cb => cb.checked = false);

                const mapUrl = `/index.html?MAPBOX_TOKEN=${encodeURIComponent(mapboxToken)}&SUN_API_TOKEN=${encodeURIComponent(sunApiToken)}`;
                iframe.src = mapUrl;
            });

            // Disable button until map is ready
            setLocationBtn.disabled = true;

            setLocationBtn.addEventListener('click', async () => {
                const latitude = parseFloat(document.getElementById('latitude').value);
                const longitude = parseFloat(document.getElementById('longitude').value);
                
                if (isNaN(latitude) || isNaN(longitude)) {
                    showStatus('Invalid latitude or longitude', 'error');
                    return;
                }

                try {
                    console.log('Sending setUserLocation request...');
                    const result = await postMapMessage('setUserLocation', { latitude, longitude });
                    console.log('setUserLocation successful:', result);
                    showStatus('Location set successfully!');
                } catch (error) {
                    console.error('Failed to set location:', error);
                    showStatus(`Failed to set location: ${error.message}`, 'error');
                }
            });

            layerCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', async (event) => {
                    const layerId = event.target.value;
                    const isChecked = event.target.checked;
                    
                    if (layerId === 'resorts' && isChecked) {
                        try {
                            // For resorts, fetch the GeoJSON data first
                            console.log('Fetching resorts.geojson...');
                            const response = await fetch('/resorts.geojson');
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            const geojsonData = await response.json();
                            console.log('Resorts data fetched, sending to map...');
                            
                            // Send data with a specific message type
                            await postMapMessage('addLayerWithData', { layerId: 'resorts', data: geojsonData });
                            showStatus(`Layer 'resorts' added with data.`);

                        } catch (error) {
                            console.error(`Failed to load or add resort layer:`, error);
                            showStatus(`Failed to add resort layer: ${error.message}`, 'error');
                            event.target.checked = false; // Rollback
                        }
                    } else {
                        // For other layers, or for removal, use the existing logic
                        const action = isChecked ? 'addLayer' : 'removeLayer';
                        try {
                            console.log(`Sending ${action} request for layer: ${layerId}`);
                            const result = await postMapMessage(action, { layerId });
                            console.log(`${action} for layer ${layerId} successful:`, result);
                            showStatus(`Layer '${layerId}' ${isChecked ? 'added' : 'removed'}.`);
                        } catch (error) {
                            console.error(`Failed to ${action} layer ${layerId}:`, error);
                            showStatus(`Failed to ${action} layer: ${error.message}`, 'error');
                            // Rollback checkbox state on failure
                            event.target.checked = !isChecked;
                        }
                    }
                });
            });

            // --- Basic Map Operations ---
            document.getElementById('setCenterBtn').addEventListener('click', () => {
                const lon = parseFloat(document.getElementById('centerLon').value);
                const lat = parseFloat(document.getElementById('centerLat').value);
                performMapOperation('center', { center: [lon, lat] }, 'Map centered.');
            });

            document.getElementById('setZoomBtn').addEventListener('click', () => {
                const zoomLevel = parseFloat(document.getElementById('zoomLevel').value);
                performMapOperation('zoom', { zoomLevel }, 'Zoom level set.');
            });

            document.getElementById('zoomInBtn').addEventListener('click', () => {
                performMapOperation('zoomIn', {}, 'Zoomed in.');
            });

            document.getElementById('zoomOutBtn').addEventListener('click', () => {
                performMapOperation('zoomOut', {}, 'Zoomed out.');
            });

            document.getElementById('setBearingBtn').addEventListener('click', () => {
                const bearing = parseFloat(document.getElementById('bearing').value);
                performMapOperation('rotate', { bearing }, 'Bearing set.');
            });

            document.getElementById('setPitchBtn').addEventListener('click', () => {
                const pitch = parseFloat(document.getElementById('pitch').value);
                performMapOperation('tilt', { pitch }, 'Pitch set.');
            });

            document.getElementById('panBtn').addEventListener('click', () => {
                const x = parseFloat(document.getElementById('panX').value);
                const y = parseFloat(document.getElementById('panY').value);
                performMapOperation('pan', { offset: [x, y] }, 'Map panned.');
            });

            document.getElementById('fitBtn').addEventListener('click', () => {
                const boundsStr = document.getElementById('bounds').value;
                const bounds = boundsStr.split(',').map(s => parseFloat(s.trim()));
                const padding = parseFloat(document.getElementById('padding').value);
                performMapOperation('fit', { bounds, padding }, 'Map fitted to bounds.');
            });
            
            document.getElementById('moveBtn').addEventListener('click', () => {
                const optionsStr = document.getElementById('moveOptions').value;
                try {
                    const viewOptions = JSON.parse(optionsStr);
                    performMapOperation('move', { viewOptions }, 'Map moved.');
                } catch (e) {
                    showStatus('Invalid JSON in Move options.', 'error');
                }
            });

            // Initially disable animation controls
            updateAnimationControls({ active: false });

            // Automatically load map if tokens are in localStorage
            if (mapboxTokenInput.value && sunApiTokenInput.value) {
                loadMapBtn.click();
            }

            // === EVENT MONITORING SYSTEM ===
            const eventsEnabledCheckbox = document.getElementById('eventsEnabled');
            const clearEventLogBtn = document.getElementById('clearEventLogBtn');
            const eventLog = document.getElementById('event-log');
            const mapEventCount = document.getElementById('map-event-count');
            const layerEventCount = document.getElementById('layer-event-count');
            const mouseEventCount = document.getElementById('mouse-event-count');
            const animationEventCount = document.getElementById('animation-event-count');

            // Event statistics
            let eventStats = {
                map: 0,
                layer: 0,
                mouse: 0,
                animation: 0
            };

            // Event log array (keep last 20 events)
            let recentEvents = [];

            function updateEventStats() {
                mapEventCount.textContent = eventStats.map;
                layerEventCount.textContent = eventStats.layer;
                mouseEventCount.textContent = eventStats.mouse;
                animationEventCount.textContent = eventStats.animation;
            }

            function addEventToLog(type, event, data = {}) {
                const timestamp = new Date().toLocaleTimeString();
                const eventInfo = {
                    timestamp,
                    type,
                    event,
                    data
                };

                // Update statistics
                if (type.includes('Map')) {
                    eventStats.map++;
                } else if (type.includes('Layer')) {
                    eventStats.layer++;
                } else if (type.includes('Mouse') || event.includes('mouse') || event.includes('click')) {
                    eventStats.mouse++;
                } else if (type.includes('Animation')) {
                    eventStats.animation++;
                }

                updateEventStats();

                // Add to recent events
                recentEvents.unshift(eventInfo);
                if (recentEvents.length > 20) {
                    recentEvents.pop();
                }

                // Update display
                updateEventLogDisplay();
            }

            function updateEventLogDisplay() {
                if (recentEvents.length === 0) {
                    eventLog.innerHTML = '<div style="color: #666; font-style: italic;">No events yet...</div>';
                    return;
                }

                const eventHtml = recentEvents.map(event => {
                    let icon = '📍';
                    let color = '#333';
                    
                    if (event.type.includes('Map')) {
                        if (event.event.includes('move')) icon = '🗺️';
                        else if (event.event.includes('zoom')) icon = '🔍';
                        else if (event.event.includes('rotate')) icon = '🧭';
                        else if (event.event.includes('tilt')) icon = '📐';
                        else if (event.event.includes('loaded')) icon = '✨';
                        else if (event.event === 'click') icon = '🎯';
                        else if (event.event === 'mouseMove') icon = '🐭';
                        color = '#007bff';
                    } else if (event.type.includes('Layer')) {
                        if (event.event === 'loaded') icon = '🗂️';
                        else if (event.event === 'error') icon = '❌';
                        else icon = '🔄';
                        color = '#28a745';
                    } else if (event.type.includes('Animation')) {
                        if (event.event === 'started') icon = '▶️';
                        else if (event.event === 'stopped') icon = '⏸️';
                        else if (event.event === 'frameChanged') icon = '🎞️';
                        color = '#fd7e14';
                    }

                    // Format data for display
                    let dataStr = '';
                    if (event.data && Object.keys(event.data).length > 0) {
                        if (event.data.center) {
                            dataStr = ` lat:${event.data.center[1].toFixed(3)}, lng:${event.data.center[0].toFixed(3)}`;
                        } else if (event.data.geoPoint) {
                            dataStr = ` lat:${event.data.geoPoint[1].toFixed(3)}, lng:${event.data.geoPoint[0].toFixed(3)}`;
                        } else if (event.data.zoomLevel !== undefined) {
                            dataStr = ` zoom:${event.data.zoomLevel.toFixed(1)}`;
                        } else if (event.data.bearing !== undefined) {
                            dataStr = ` bearing:${event.data.bearing.toFixed(0)}°`;
                        } else if (event.data.pitch !== undefined) {
                            dataStr = ` pitch:${event.data.pitch.toFixed(0)}°`;
                        } else if (event.data.layerId) {
                            dataStr = ` layer:${event.data.layerId}`;
                        } else if (event.data.currentFrame) {
                            dataStr = ` frame:${event.data.currentFrame}/${event.data.totalFrames}`;
                        }
                    }

                    return `<div style="color: ${color}; margin: 2px 0; line-height: 1.2;">
                        <span style="color: #666;">${event.timestamp}</span> ${icon} <strong>${event.event}</strong>${dataStr}
                    </div>`;
                }).join('');

                eventLog.innerHTML = eventHtml;
            }

            function clearEventLog() {
                recentEvents = [];
                eventStats = { map: 0, layer: 0, mouse: 0, animation: 0 };
                updateEventStats();
                updateEventLogDisplay();
                console.log('[Test Harness] 🧹 Event log cleared');
            }

            // Event monitoring toggle
            eventsEnabledCheckbox.addEventListener('change', async (event) => {
                const enabled = event.target.checked;
                try {
                    console.log(`[Test Harness] ${enabled ? 'Enabling' : 'Disabling'} event monitoring...`);
                    await postMapMessage('toggleEvents', { enabled });
                    showStatus(`Event monitoring ${enabled ? 'enabled' : 'disabled'}!`);
                    
                    if (!enabled) {
                        // Clear the log when disabling
                        setTimeout(clearEventLog, 100);
                    }
                } catch (error) {
                    console.error('[Test Harness] Failed to toggle events:', error);
                    showStatus(`Failed to toggle events: ${error.message}`, 'error');
                    // Revert checkbox state
                    event.target.checked = !enabled;
                }
            });

            // Clear event log button
            clearEventLogBtn.addEventListener('click', clearEventLog);
        });
    </script>
</body>
</html> 