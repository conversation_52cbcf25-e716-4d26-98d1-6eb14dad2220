import 'dotenv/config';

export default {
  expo: {
    name: "twc-snow",
    slug: "twc-snow",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/PD-App-Icon_1024x1024.png",
    scheme: "twcsnow",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.weather.Snow",
      config: {
        usesNonExemptEncryption: false,
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/PD-App-Icon_1024x1024_android_192.png",
        backgroundColor: "#ffffff"
      },
      edgeToEdgeEnabled: true,
      package: "com.boneil.twcsnow"
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png"
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          "image": "./assets/images/splash-icon.png",
          "imageWidth": 200,
          "resizeMode": "contain",
          "backgroundColor": "#ffffff"
        }
      ],
      [
        "expo-location",
        {
          "locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."
        }
      ]
    ],
    experiments: {
      typedRoutes: true
    },
    extra: {
      // Add environment variables here
      weatherApiKey: process.env.WEATHER_API_KEY,
      mapboxToken: process.env.MAPBOX_TOKEN,
      sunApiToken: process.env.SUN_API_TOKEN,
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
      eas: {
        projectId: process.env.EAS_PROJECT_ID
      }
    }
  }
};
