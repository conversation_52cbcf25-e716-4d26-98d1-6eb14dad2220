// types.ts

// Represents a ski resort
export interface Resort {
  id: string;
  name: string;
  description: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  logo?: string; // URL to the resort's logo
  backgroundImage?: string; // URL to a background image for the resort
  website?: string; // Resort's official website
  passId?: string; // ID of the pass it belongs to (e.g., 'ikon', 'epic')
  // Add more resort-specific details as needed
  stats?: {
    verticalDrop?: number; // meters or feet
    skiableAcres?: number;
    lifts?: number;
    runs?: number;
  };
  trailMapUrl?: string;
  location?: {
    city?: string;
    state?: string;
    country?: string;
  };
}

// Represents weather data for a resort
export interface WeatherData {
  id: string; // Unique ID for the weather data entry
  resortId: string; // ID of the resort this weather data belongs to
  condition: string; // e.g., 'sunny', 'partly-cloudy', 'snow', 'cloudy'
  temperature: number; // Celsius or Fahrenheit, define unit consistency
  windSpeed: number; // km/h or mph
  humidity: number; // Percentage
  hourly: HourlyForecast[];
  daily: DailyForecast[];
  // Add more weather details like snow accumulation, chance of precipitation, etc.
  snowfallLast24h?: number; // cm or inches
  snowfallNext24h?: number; // cm or inches
  baseDepth?: number; // cm or inches
}

export interface HourlyForecast {
  time: string; // e.g., '10:00', '1PM'
  condition: string;
  temperature: number;
}

export interface DailyForecast {
    day: string; // e.g., 'Mon', 'Tomorrow'
    condition: string;
    high: number;
    low: number;
    chanceOfSnow?: number; // Percentage
}


// Represents a ski pass (like Ikon, Epic)
export interface ResortPass {
  id: string;
  name: string;
  logo?: string; // URL to the pass logo
  website?: string;
  description?: string;
  // Add more pass-specific details
  participatingResorts?: string[]; // Array of resort IDs
}

// You can add more shared types here as your application grows.
// For example, user preferences, map settings, etc.
