#!/usr/bin/env node

// This script builds the Vite project and copies its output to the app's assets folder for embedding.
// It should be run as part of the prebuild step for the main app.

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const viteProjectDir = path.join(__dirname, '../pangea-bundle/pangea-bundle');
const viteDistDir = path.join(viteProjectDir, 'dist');
const assetsTargetDir = path.join(__dirname, '../assets/pangea-bundle');

function log(msg) {
  console.log(`[build-pangea-bundle] ${msg}`);
}

try {
  log('Building Vite project...');
  execSync('npm run build', { cwd: viteProjectDir, stdio: 'inherit' });

  // Ensure the target directory exists
  if (!fs.existsSync(assetsTargetDir)) {
    fs.mkdirSync(assetsTargetDir, { recursive: true });
    log(`Created assets target directory: ${assetsTargetDir}`);
  }

  // Copy all files from dist to assets/pangea-bundle
  const files = fs.readdirSync(viteDistDir);
  files.forEach(file => {
    const src = path.join(viteDistDir, file);
    const dest = path.join(assetsTargetDir, file);
    fs.copyFileSync(src, dest);
    log(`Copied ${file} to assets.`);
  });

  log('Vite build output successfully copied to assets.');
} catch (err) {
  log('Error during build or copy process:');
  console.error(err);
  process.exit(1);
} 