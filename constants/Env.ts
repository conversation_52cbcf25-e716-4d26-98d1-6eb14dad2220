import Constants from 'expo-constants';

// Access environment variables from app.config.js
const ENV = {
  weatherApiKey: Constants.expoConfig?.extra?.weatherApiKey || '',
  mapboxToken: Constants.expoConfig?.extra?.mapboxToken || '',
  sunApiToken: Constants.expoConfig?.extra?.sunApiToken || '',
  easProjectId: Constants.expoConfig?.extra?.eas?.projectId || '',
  openRouterApiKey: Constants.expoConfig?.extra?.OPENROUTER_API_KEY || '',
};

export default ENV;

// Helper function to check if we have the required environment variables
export function validateEnv() {
  const missingVars = [];
  
  if (!ENV.weatherApiKey) missingVars.push('WEATHER_API_KEY');
  if (!ENV.mapboxToken) missingVars.push('MAPBOX_TOKEN');
  if (!ENV.sunApiToken) missingVars.push('SUN_API_TOKEN');
  if (!ENV.easProjectId) missingVars.push('EAS_PROJECT_ID');
  if (!ENV.openRouterApiKey) missingVars.push('OPENROUTER_API_KEY');
  
  if (missingVars.length > 0) {
    console.warn(
      `Missing environment variables: ${missingVars.join(', ')}. ` +
      'Make sure to set them in your .env file and restart the app.'
    );
    return false;
  }
  
  return true;
}