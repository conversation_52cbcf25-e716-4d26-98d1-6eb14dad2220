import { useQuery } from '@tanstack/react-query';
import { HourlyForecastData, hourlyForecastService } from '../services/hourlyForecastService';

/**
 * React Query hook for hourly weather forecast
 * Uses the HourlyForecastService which follows the external data loading pattern
 * @param location The location string or coordinates ("lat,lon")
 * @param options Optional settings (bypassCache)
 */
export function useHourlyForecastQuery(location: string, options: { bypassCache?: boolean } = {}) {
  return useQuery<HourlyForecastData[], Error>({
    queryKey: ['hourlyForecast', location, options.bypassCache],
    queryFn: () => hourlyForecastService.fetchHourlyForecast(location, options),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    enabled: !!location, // Only run query if location is provided
  });
}

// Re-export the interface for convenience
export type { HourlyForecastData };
