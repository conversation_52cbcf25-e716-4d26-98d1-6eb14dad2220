import { useQuery } from '@tanstack/react-query';
import ENV from '../constants/Env';

// TypeScript interface for the ski conditions API response (partial, expand as needed)
export interface SkiConditionsData {
  ski: number; // API returns 'ski' as the ID
  resortName: string;
  resortNameShort?: string;
  resortOperatingStatus?: string;
  projectedOpenDate?: string;
  projectedClosureDate?: string;
  snowDepthBase?: number;
  snowFall24hours?: number;
  liftsOpen?: number;
  resortLifts?: number;
  resortRuns?: number;
  // Add more fields as needed from the API response
  [key: string]: any;
}

/**
 * Fetches ski conditions for a given resort using the weather.com API.
 * @param skiId The resort's ski ID (as used by the API)
 */
async function fetchSkiConditions(skiId: string | number): Promise<SkiConditionsData | null> {
  const apiKey = ENV.sunApiToken;
  if (!apiKey) throw new Error('Missing SUN_API_TOKEN in environment variables');
  if (!skiId) throw new Error('Missing skiId for ski conditions API');
  // Ensure skiId is a number (API expects numeric ID)
  const skiIdNum = typeof skiId === 'string' ? parseInt(skiId, 10) : skiId;
  const url = `https://api.weather.com/v3/wx/skiconditions?units=e&language=en-US&format=json&apiKey=${apiKey}&ski=${skiIdNum}`;
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Ski Conditions API error: ${response.status}`);
    }
    const data = await response.json();
    // Updated check for correct fields
    if (!data.ski && !data.resortName) {
      console.error('[SkiConditionsAPI] Malformed response:', JSON.stringify(data));
      throw new Error('Malformed ski conditions data');
    }
    return data as SkiConditionsData;
  } catch (err) {
    console.error('[SkiConditionsAPI] Error fetching ski conditions:', err);
    return null;
  }
}

/**
 * React Query hook for ski conditions
 * @param skiId The resort's ski ID
 */
export function useSkiConditionsQuery(skiId: string | number | undefined) {
  return useQuery<SkiConditionsData | null, Error>({
    queryKey: ['skiConditions', skiId],
    queryFn: () => (skiId ? fetchSkiConditions(skiId) : Promise.resolve(null)),
    enabled: !!skiId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
} 