import { useQuery } from '@tanstack/react-query';
import { AIRecommendationRequest, AIRecommendationResponse, AIRecommendationService } from '../services/aiRecommendationService';

/**
 * React Query hook for AI recommendation summary
 * @param req The full AIRecommendationRequest object
 * @param model The AI model to use (optional)
 * @param options Optional settings (bypassCache)
 */
export function useAiRecommendationQuery(
  req: AIRecommendationRequest,
  model?: string,
  options: { bypassCache?: boolean } = {}
) {
  // TODO: Ensure req is fully composed by the caller (RecommendationCard)
  return useQuery<AIRecommendationResponse, Error>({
    queryKey: ['ai-recommendation', req, model, options.bypassCache],
    queryFn: () => AIRecommendationService.getInstance().fetchRecommendation(req, model, options),
    staleTime: 5 * 60 * 1000,
    retry: 1,
    enabled: !!req && !!req.location && Array.isArray(req.resorts), // Allow empty resorts array
  });
} 