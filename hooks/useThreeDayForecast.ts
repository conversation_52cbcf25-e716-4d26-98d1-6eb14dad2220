import { useQuery } from '@tanstack/react-query';
import ENV from '../constants/Env';

interface ThreeDayForecastDay {
  calendarDay: string;
  dayOfWeek: string;
  narrative: string;
  temperatureMax: number;
  temperatureMin: number;
  iconCode: number;
}

interface ThreeDayForecastData {
  forecasts: ThreeDayForecastDay[];
}

export interface UseThreeDayForecastResult {
  data: ThreeDayForecastData | undefined;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Fetches 3-day weather forecast from weather.com API
 * @param geocode - Latitude,Longitude string (e.g., '45.28,-111.40')
 */
async function fetchThreeDayForecast(geocode: string): Promise<ThreeDayForecastData> {
  // Defensive: require a valid geocode string
  if (!geocode || !/^[-\d.]+,[-\d.]+$/.test(geocode)) {
    throw new Error('Invalid geocode format. Expected "lat,lon"');
  }
  
  const apiKey = ENV.sunApiToken;
  if (!apiKey) {
    throw new Error('Missing SUN_API_TOKEN in environment variables');
  }

  const url = `https://api.weather.com/v3/wx/forecast/daily/3day?geocode=${encodeURIComponent(geocode)}&units=e&language=en-US&format=json&apiKey=${apiKey}`;

  try {
    const response = await fetch(url);
    if (!response.ok) {
      const text = await response.text();
      throw new Error(`Three-day forecast API error: ${response.status} - ${text}`);
    }
    
    const json = await response.json();
    
    // Map API response to our interface
    if (!json || !json.calendarDayTemperatureMax || !json.calendarDayTemperatureMin) {
      throw new Error('Malformed three-day forecast API response');
    }
    
    const forecasts: ThreeDayForecastDay[] = (json.narrative || []).map((narrative: string, i: number) => ({
      calendarDay: json.validTimeLocal[i],
      dayOfWeek: json.dayOfWeek[i],
      narrative,
      temperatureMax: json.calendarDayTemperatureMax[i],
      temperatureMin: json.calendarDayTemperatureMin[i],
      iconCode: json.daypart[0]?.iconCode[i] || 30,
    }));
    
    return { forecasts };
  } catch (err) {
    // Log and rethrow for react-query
    console.error('[ThreeDayForecastAPI] Error fetching forecast:', err);
    throw err;
  }
}

/**
 * React Query hook for 3-day weather forecast with location-based caching
 * @param geocode - Latitude,Longitude string (e.g., '45.28,-111.40')
 * @returns { data, isLoading, error }
 */
const useThreeDayForecast = (geocode: string): UseThreeDayForecastResult => {
  const queryResult = useQuery<ThreeDayForecastData, Error>({
    queryKey: ['threeDayForecast', geocode],
    queryFn: () => fetchThreeDayForecast(geocode),
    staleTime: 5 * 60 * 1000, // 5 minutes - data stays fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes - data stays in cache for 10 minutes
    retry: 1,
    enabled: !!geocode, // Only run query if geocode is provided
  });

  return {
    data: queryResult.data,
    isLoading: queryResult.isLoading,
    error: queryResult.error,
  };
};

export default useThreeDayForecast; 