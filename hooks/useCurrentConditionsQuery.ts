import { useQuery } from '@tanstack/react-query';
import { CurrentConditionsData, currentConditionsService } from '../services/currentConditionsService';

export { CurrentConditionsData };

/**
 * React Query hook for fetching current weather conditions
 * @param geocode Location in "lat,lon" format
 * @param options Query options including cache bypass
 * @returns React Query result with current conditions data
 */
export function useCurrentConditionsQuery(
  geocode: string, 
  options: { bypassCache?: boolean } = {}
) {
  return useQuery({
    queryKey: ['current-conditions', geocode],
    queryFn: () => currentConditionsService.fetchCurrentConditions(geocode, options),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    enabled: !!geocode && geocode.includes(','), // Only run if geocode is valid
  });
} 