import { useQuery } from '@tanstack/react-query';
import ENV from '../constants/Env';

// Example type for reverse geocoding data
export interface ReverseGeocodingData {
  city: string;
  state: string;
  fullAddress: string; // e.g., "Cupertino, CA"
}

/**
 * Fetches reverse geocoded data for a given lat/long string using weather.com API.
 * @param latLong The latitude/longitude string (e.g., "37.7749,-122.4194")
 */
async function fetchReverseGeocoding(latLong: string): Promise<ReverseGeocodingData> {
  // Defensive: require a valid latLong string
  if (!latLong || !/^[-\d.]+,[-\d.]+$/.test(latLong)) {
    throw new Error('Invalid latLong format. Expected "lat,lon"');
  }
  const apiKey = ENV.sunApiToken;
  if (!apiKey) {
    throw new Error('Missing SUN_API_TOKEN in environment variables');
  }
  // Build the API URL for weather.com reverse geocoding
  const url = `https://api.weather.com/v3/location/point?geocode=${latLong}&format=json&language=en-US&apiKey=${apiKey}`;
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Reverse Geocoding API error: ${response.status}`);
    }
    const data = await response.json();
    // Defensive: ensure required fields exist
    // The API returns fields like: location.city, location.adminDistrict, location.displayName
    if (!data.location) {
      throw new Error('Malformed reverse geocoding data: missing location');
    }
    // Map the API response to our ReverseGeocodingData interface
    return {
      city: data.location.city ?? '',
      state: data.location.adminDistrict ?? '',
      fullAddress: data.location.displayName ?? `${data.location.city ?? ''}, ${data.location.adminDistrict ?? ''}`,
    };
  } catch (err) {
    // Log and rethrow for react-query
    console.error('[ReverseGeocodingAPI] Error fetching reverse geocoding:', err);
    throw err;
  }
}

/**
 * React Query hook for reverse geocoding data
 * @param latLong The latitude/longitude string (e.g., "37.7749,-122.4194")
 */
export function useReverseGeocodingQuery(latLong: string) {
  return useQuery<ReverseGeocodingData, Error>({
    queryKey: ['reverseGeocoding', latLong],
    queryFn: () => fetchReverseGeocoding(latLong),
    staleTime: Infinity, // Geocoded data for a specific lat/long rarely changes
    gcTime: Infinity, // How long to keep unused data in cache
    enabled: !!latLong, // Only run query if latLong is provided
  });
}
