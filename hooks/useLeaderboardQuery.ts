import { useQuery } from '@tanstack/react-query';
import { leaderboardService } from '../services/leaderboardService';
import { log } from '../services/loggerService';
import { Resort } from '../types';

export interface LeaderboardEntry {
  resortId: string;
  snowfall: number;
  rank: number;
  resortName?: string;
  resortLocation?: string;
  resortLogoUrl?: string;
  // Add other resort details as needed
}

export interface LeaderboardData {
  recentSnowfall: LeaderboardEntry[];
  forecastSnowfall: LeaderboardEntry[];
  seasonTotals: LeaderboardEntry[];
}

// Helper: Find a Resort by ID (string compare for robustness)
function findResortById(resorts: Resort[], id: string | number): Resort | undefined {
  return resorts.find(r => String(r.id) === String(id));
}

// Helper: Map API leaderboard entries to LeaderboardEntry[]
function mapLeaderboardEntries(
  entries: any[], 
  resorts: Resort[], 
  sectionName = '', 
  snowKey = 'snowfall'
): LeaderboardEntry[] {
  log('[LeaderboardQuery]', 'debug', `Mapping entries for section: ${sectionName}`, [entries]);
  
  return entries.map((entry, index) => {
    const resort = findResortById(resorts, entry.resortId || entry.resort?.id);
    
    if (!resort) {
      log('[LeaderboardQuery]', 'warn', 'Resort not found for entry', [entry]);
    }
    
    let locationString = 'N/A';
    if (resort && resort.coordinates) {
      // TODO: Optionally, format location string from resort fields
      locationString = '';
    }
    
    return {
      resortId: String(entry.resortId || entry.resort?.id),
      snowfall: entry[snowKey] ?? 0,
      rank: entry.rank ?? (index + 1), // Use provided rank or calculate from index
      resortName: resort?.name || entry.resortName || `Resort ID: ${entry.resortId || entry.resort?.id}`,
      resortLocation: locationString,
      resortLogoUrl: resort?.logo,
      // Add more fields as needed
    };
  });
}

/**
 * Fetch and transform leaderboard data using the new service
 */
async function fetchLeaderboardData(limit: number = 10, options: { bypassCache?: boolean } = {}): Promise<LeaderboardData> {
  log('[LeaderboardQuery]', 'info', '📊 Fetching leaderboard data with limit:', [limit]);
  
  try {
    // Use the new service which handles caching and error handling
    const { leaderboard, resorts } = await leaderboardService.fetchCombinedData(limit, options);
    
    // log('[LeaderboardQuery]', 'debug', 'Raw leaderboard data:', [leaderboard]);
    log('[LeaderboardQuery]', 'debug', 'Resort list count:', [resorts.length]);

    // Map API data to the expected format, slicing to the limit
    const recentSnowfall = mapLeaderboardEntries(
      (leaderboard.recentSnowLeaderboard || []).slice(0, limit), 
      resorts, 
      'recentSnowfall', 
      'recentSnow'
    );
    
    const forecastSnowfall = mapLeaderboardEntries(
      (leaderboard.forecastSnowLeaderboard || []).slice(0, limit), 
      resorts, 
      'forecastSnowfall', 
      'fiveDaySnowForecast'
    );
    
    const seasonTotals = mapLeaderboardEntries(
      (leaderboard.snowOnGroundLeaderboard || []).slice(0, limit), 
      resorts, 
      'seasonTotals', 
      'snowOnGround'
    );

    const result = {
      recentSnowfall,
      forecastSnowfall,
      seasonTotals,
    };

    log('[LeaderboardQuery]', 'debug', 'Transformed leaderboard data:', [result]);
    return result;

  } catch (error) {
    log('[LeaderboardQuery]', 'error', 'Failed to fetch leaderboard data:', [error]);
    throw error;
  }
}

/**
 * React Query hook for leaderboard data
 * Uses the new LeaderboardService with built-in caching
 * @param limit Number of entries to fetch
 * @param options Optional settings (bypassCache)
 */
export function useLeaderboardQuery(limit: number = 10, options: { bypassCache?: boolean } = {}) {
  return useQuery<LeaderboardData, Error>({
    queryKey: ['leaderboardData', limit, options.bypassCache],
    queryFn: () => fetchLeaderboardData(limit, options),
    staleTime: 5 * 60 * 1000, // 5 minutes - shorter since service handles persistent caching
    refetchOnWindowFocus: false, // Reduce refetching since we have persistent cache
    retry: 1, // Let the service handle retries
    meta: {
      description: 'Leaderboard data with resort information'
    }
  });
}
