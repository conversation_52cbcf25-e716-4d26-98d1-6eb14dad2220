import { useQuery } from '@tanstack/react-query';
import ENV from '../constants/Env';

// Example type for weather data (customize as needed)
export interface WeatherData {
  temperature: number;
  description: string;
  high: number;
  low: number;
  wind: string;
  humidity: string;
  precipitation: string;
  visibility: string;
  iconCode: number;
  dayOrNight?: string;
  windSpeed?: number;
  windDirectionCardinal?: string;
}

/**
 * Fetches current weather data for a given location using the weather.com API.
 * @param location The location string or coordinates ("lat,lon")
 */
async function fetchWeather(location: string): Promise<WeatherData> {
  // Defensive: require a valid location string
  if (!location || !/^[-\d.]+,[-\d.]+$/.test(location)) {
    throw new Error('Invalid location format. Expected "lat,lon"');
  }
  const apiKey = ENV.sunApiToken;
  if (!apiKey) {
    throw new Error('Missing SUN_API_TOKEN in environment variables');
  }
  // Build the API URL
  const url = `https://api.weather.com/v3/wx/observations/current?geocode=${location}&units=e&language=en-US&format=json&apiKey=${apiKey}`;
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Weather API error: ${response.status}`);
    }
    const data = await response.json();
    // Map the API response to our WeatherData interface
    return {
      temperature: data.temperature ?? 0,
      description: data.wxPhraseLong ?? 'Unknown',
      high: data.temperatureMax24Hour ?? data.temperature ?? 0,
      low: data.temperatureMin24Hour ?? data.temperature ?? 0,
      wind: data.windPhrase ?? '',
      humidity: data.relativeHumidity !== undefined ? `${data.relativeHumidity}%` : '',
      precipitation: data.precip1Hour !== undefined ? `${data.precip1Hour} in` : '',
      visibility: data.visibility !== undefined ? `${data.visibility} mi` : '',
      iconCode: data.iconCode ?? 0,
      dayOrNight: data.dayOrNight,
      windSpeed: data.windSpeed,
      windDirectionCardinal: data.windDirectionCardinal,
    };
  } catch (err) {
    // Log and rethrow for react-query
    console.error('[WeatherAPI] Error fetching weather:', err);
    throw err;
  }
}

/**
 * React Query hook for current weather data
 * @param location The location string or coordinates
 */
export function useWeatherQuery(location: string) {
  return useQuery<WeatherData, Error>({
    queryKey: ['weather', location],
    queryFn: () => fetchWeather(location),
    // TODO: Adjust staleTime, cacheTime, and error handling as needed
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
} 