import { useQuery } from '@tanstack/react-query';

// TODO: Replace with your actual radar API endpoint and parameters
const RADAR_API_URL = 'https://api.example.com/radar';

export interface RadarData {
  imageUrl: string;
  timestamp: string;
}

/**
 * Fetches the current radar map image URL for a given location.
 * @param location The location string or coordinates
 */
async function fetchRadar(location: string): Promise<RadarData> {
  try {
    // TODO: Implement real API call with location parameter
    const response = await fetch(`${RADAR_API_URL}?location=${encodeURIComponent(location)}`);
    if (!response.ok) throw new Error('Failed to fetch radar data');
    return response.json();
  } catch (error) {
    console.error('Radar fetch error:', error);
    throw error;
  }
}

/**
 * React Query hook for current radar map
 * @param location The location string or coordinates
 */
export function useRadarQuery(location: string) {
  return useQuery<RadarData, Error>({
    queryKey: ['radar', location],
    queryFn: () => fetchRadar(location),
    staleTime: 2 * 60 * 1000,
    retry: 1,
  });
} 