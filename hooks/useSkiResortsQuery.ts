import { useQuery } from '@tanstack/react-query';
import { Resort } from '../types';

/**
 * Fetches all ski resorts from the mock data file as full Resort objects.
 */
async function fetchSkiResorts(): Promise<Resort[]> {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const data = require('../assets/mock-data/ski-resorts.json');
  // Defensive: ensure data.skiResorts exists and is an array
  return Array.isArray(data.skiResorts)
    ? data.skiResorts
    : [];
}

/**
 * React Query hook for all ski resorts (full Resort objects)
 */
export function useSkiResortsQuery() {
  return useQuery<Resort[], Error>({
    queryKey: ['skiResorts'],
    queryFn: fetchSkiResorts,
    staleTime: 60 * 60 * 1000, // 1 hour
    retry: 1,
  });
}

export { fetchSkiResorts }; // Export for use in other services

  export type { Resort };

