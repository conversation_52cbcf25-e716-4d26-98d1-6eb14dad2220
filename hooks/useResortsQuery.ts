import { useQuery } from '@tanstack/react-query';
import { ResortData, resortService } from '../services/resortService';

/**
 * React Query hook for nearby ski resorts
 * Uses the ResortService following the external data loading pattern
 * 
 * @param geocode Latitude,Longitude string (e.g., '33.74,-84.39')
 * @param limit Optional number of results to return
 * @param options Optional settings (bypassCache)
 */
export function useResortsQuery(geocode: string, limit?: number, options: { bypassCache?: boolean } = {}) {
  return useQuery<ResortData[], Error>({
    queryKey: ['resorts', geocode, limit, options.bypassCache],
    queryFn: () => resortService.fetchResorts(geocode, limit, options),
    enabled: !!geocode,
    staleTime: 10 * 60 * 1000, // 10 minutes - matches service cache TTL
    retry: 1, // Let service handle retries
  });
}

// Re-export ResortData interface for convenience
export type { ResortData } from '../services/resortService';
