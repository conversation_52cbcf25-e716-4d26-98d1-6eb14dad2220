import { useQuery } from '@tanstack/react-query';
import { ProbabilisticSnowfallData, probabilisticSnowfallService } from '../services/probabilisticSnowfallService';

export { ProbabilisticSnowfallData };

/**
 * React Query hook for fetching probabilistic snowfall data
 * @param geocode Location in "lat,lon" format
 * @param timespan The timespan to filter for (24hr, 48hr, 72hr)
 * @param options Query options including cache bypass
 * @returns React Query result with probabilistic snowfall data
 */
export function useProbabilisticSnowfallQuery(
  geocode: string,
  timespan: '24hr' | '48hr' | '72hr' = '24hr',
  options: { bypassCache?: boolean } = {}
) {
  return useQuery({
    queryKey: ['probabilistic-snowfall', geocode, timespan, options.bypassCache],
    queryFn: () => probabilisticSnowfallService.fetchProbabilisticSnowfall(geocode, 0, timespan, options),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    enabled: !!geocode && geocode.includes(','), // Only run if geocode is valid
  });
} 