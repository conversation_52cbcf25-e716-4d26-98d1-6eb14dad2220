import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import i18n from '../i18n/useTranslation';
import ConfigService from '../services/appConfigService';

// Context shape
interface ConfigContextValue {
  config: any;
  meta: any;
  status: 'loading' | 'ready' | 'error';
  reload: () => Promise<void>;
}

const ConfigContext = createContext<ConfigContextValue | undefined>(undefined);

export const ConfigProvider = ({ children }: { children: ReactNode }) => {
  const [config, setConfig] = useState(ConfigService.get(''));
  const [meta, setMeta] = useState(config._meta || {});
  const [status, setStatus] = useState<'loading' | 'ready' | 'error'>('loading');

  // Helper to apply i18nOverrides from config to i18next
  const applyI18nOverrides = (cfg: any) => {
    if (cfg.i18nOverrides) {
      Object.entries(cfg.i18nOverrides).forEach(([lang, overrides]) => {
        if (overrides && typeof overrides === 'object') {
          i18n.addResources(lang, 'translation', overrides);
        }
      });
    }
  };

  useEffect(() => {
    const update = () => {
      const cfg = ConfigService.get('');
      setConfig(cfg);
      setMeta(cfg._meta || {});
      // Apply i18nOverrides to i18next
      applyI18nOverrides(cfg);
    };
    update();
    setStatus('ready');
    // Subscribe to config changes
    const unsubscribe = ConfigService.subscribe(update);
    return () => unsubscribe();
  }, []);

  const reload = async () => {
    setStatus('loading');
    try {
      await ConfigService.reload();
      const cfg = ConfigService.get('');
      setConfig(cfg);
      setMeta(cfg._meta || {});
      // Apply i18nOverrides to i18next on reload
      applyI18nOverrides(cfg);
      setStatus('ready');
    } catch (e) {
      setStatus('error');
    }
  };

  return (
    <ConfigContext.Provider value={{ config, meta, status, reload }}>
      {children}
    </ConfigContext.Provider>
  );
};

export function useConfig() {
  const ctx = useContext(ConfigContext);
  if (!ctx) throw new Error('useConfig must be used within a ConfigProvider');
  return ctx;
} 