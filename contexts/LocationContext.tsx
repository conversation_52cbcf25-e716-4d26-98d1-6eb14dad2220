import * as Location from 'expo-location';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { log } from '../services/loggerService';

export interface LocationData {
  latitude: number;
  longitude: number;
  altitude?: number | null;
  accuracy?: number | null;
  timestamp: number;
}

export interface LocationContextType {
  location: LocationData | null;
  locationName: string | null;
  isLoading: boolean;
  error: string | null;
  refreshLocation: () => Promise<void>;
  hasPermission: boolean;
  requestPermission: () => Promise<boolean>;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export interface LocationProviderProps {
  children: ReactNode;
}

export function LocationProvider({ children }: LocationProviderProps) {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [locationName, setLocationName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);

  const requestPermission = async (): Promise<boolean> => {
    log('[LocationContext]', 'info', '🛡️ Requesting foreground location permission...');
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasPermission(granted);
      log('[LocationContext]', 'info', `Permission request result: ${status}`);
      if (!granted) {
        setError('Location permission denied');
        log('[LocationContext]', 'warn', 'Location permission denied by user.');
      }
      return granted;
    } catch (err) {
      setError('Failed to request location permission');
      log('[LocationContext]', 'error', 'Error requesting location permission:', [err]);
      return false;
    }
  };

  const reverseGeocode = async (latitude: number, longitude: number): Promise<string | null> => {
    log('[LocationContext]', 'info', `🌎 Reverse geocoding for lat: ${latitude}, lon: ${longitude}`);
    try {
      const reverseGeocodeResult = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      log('[LocationContext]', 'debug', 'Reverse geocode result:', [reverseGeocodeResult]);
      if (reverseGeocodeResult.length > 0) {
        const result = reverseGeocodeResult[0];
        const city = result.city || result.subregion || result.region;
        const state = result.region;
        if (city && state) {
          return `${city}, ${state}`;
        } else if (city) {
          return city;
        } else if (state) {
          return state;
        }
      }
      return null;
    } catch (err) {
      log('[LocationContext]', 'warn', 'Failed to reverse geocode location:', [err]);
      return null;
    }
  };

  const getCurrentLocation = async (): Promise<void> => {
    log('[LocationContext]', 'info', '📍 Getting current location...');
    try {
      setIsLoading(true);
      setError(null);
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced
      });
      log('[LocationContext]', 'debug', 'Current location result:', [currentLocation]);
      const locationData: LocationData = {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        altitude: currentLocation.coords.altitude,
        accuracy: currentLocation.coords.accuracy,
        timestamp: currentLocation.timestamp,
      };
      setLocation(locationData);
      // Get location name
      const name = await reverseGeocode(
        currentLocation.coords.latitude,
        currentLocation.coords.longitude
      );
      setLocationName(name);
      log('[LocationContext]', 'info', 'Location name set to:', [name]);
    } catch (err) {
      log('[LocationContext]', 'error', 'Failed to get current location:', [err]);
      setError('Failed to get current location');
    } finally {
      setIsLoading(false);
      log('[LocationContext]', 'info', 'Location loading finished.');
    }
  };

  const refreshLocation = async (): Promise<void> => {
    log('[LocationContext]', 'info', '🔄 Refreshing location...');
    if (!hasPermission) {
      const granted = await requestPermission();
      if (!granted) {
        log('[LocationContext]', 'warn', 'Permission not granted during refresh.');
        return;
      }
    }
    await getCurrentLocation();
  };

  // Initialize location on mount
  useEffect(() => {
    log('[LocationContext]', 'info', 'Initializing location context on mount...');
    const initializeLocation = async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasPermission(granted);
      log('[LocationContext]', 'info', `Initial permission status: ${status}`);
      if (granted) {
        await getCurrentLocation();
      } else if (status === 'undetermined') {
        log('[LocationContext]', 'info', 'Permission undetermined, requesting permission...');
        const permissionGranted = await requestPermission();
        if (permissionGranted) {
          log('[LocationContext]', 'info', 'Permission granted, getting location...');
          await getCurrentLocation();
        } else {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
        log('[LocationContext]', 'info', 'No location permission on mount.');
      }
    };
    initializeLocation();
  }, []);

  const value: LocationContextType = {
    location,
    locationName,
    isLoading,
    error,
    refreshLocation,
    hasPermission,
    requestPermission,
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
}

export function useLocation(): LocationContextType {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
}