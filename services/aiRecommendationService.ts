// aiRecommendationService.ts
// Service to fetch AI-powered snow/ski travel recommendations

import ENV from '../constants/Env';
import { ApiErrorHandler } from '../utils/ApiErrorHandler';
import { BaseApiService } from '../utils/BaseApiService';
import { ValidationUtils } from '../utils/ValidationUtils';
import ConfigService from './appConfigService';
import { log } from './loggerService';

// Types for AI recommendation
export interface AIResortInfo {
  name: string;
  forecast: any[]; // Use ThreeDayForecastDay[] if imported
  ski: any; // Use SkiConditionsData if imported
}

export interface AIRecommendationRequest {
  location: string;
  timeOfDay: string;
  userWeather: any; // Use WeatherData if imported
  resorts: AIResortInfo[];
}

export interface AIRecommendationResponse {
  recommendation: string;
}

/**
 * Simple ES6-style template interpolator: replaces ${var} with values from vars object.
 */
function interpolateES6(template: string, vars: Record<string, any>) {
  return template.replace(/\$\{(\w+)\}/g, (_, key) => (key in vars ? vars[key] : ''));
}

/**
 * Stringifies value if it's an object, otherwise returns as-is.
 */
function safeStringify(val: any) {
  if (val === null || val === undefined) return '';
  if (typeof val === 'object') return JSON.stringify(val);
  return String(val);
}

/**
 * Service for fetching AI-powered snow/ski travel recommendations
 * Handles API communication, caching, and data transformation
 */
export class AIRecommendationService extends BaseApiService<AIRecommendationResponse> {
  private static instance: AIRecommendationService;
  private readonly logScope = '[AIRecommendation]';

  private constructor() {
    super('AIRecommendationService');
  }

  public static getInstance(): AIRecommendationService {
    if (!AIRecommendationService.instance) {
      AIRecommendationService.instance = new AIRecommendationService();
    }
    return AIRecommendationService.instance;
  }

  /**
   * Fetch AI recommendation with caching and error handling
   * @param req - The request object containing location, resort, and weatherData
   * @param model - (Optional) The model to use for the AI - if not provided, uses config default
   * @param options - Optional settings (bypassCache)
   * @returns AIRecommendationResponse with a short recommendation string
   */
  async fetchRecommendation(
    req: AIRecommendationRequest,
    model?: string,
    options: { bypassCache?: boolean } = {}
  ): Promise<AIRecommendationResponse> {
    try {
      // Validate input
      if (!req.location ) {
        throw new Error('Invalid request: location is required');
      }

      // Validate API key
      const apiKey = ENV.openRouterApiKey;

      const keyValidation = ValidationUtils.validateApiKey(apiKey, 'AI Recommendation');
      if (!keyValidation.isValid) {
        throw new Error(`AI API key validation failed: ${keyValidation.errors.join(', ')}`);
      }

      // Get AI configuration from config service
      const configModel = ConfigService.get('ai.ai-recommendation.model');
      const temperature = ConfigService.get('ai.ai-recommendation.temperature');
      const maxTokens = ConfigService.get('ai.ai-recommendation.max_tokens');
      const systemPrompt = ConfigService.get('ai.ai-recommendation.system.prompt');
      const userPromptTemplate = ConfigService.get('ai.ai-recommendation.user.prompt');

      //TODO: Replace with with Mercury API
      const url = ConfigService.get('api.openRouter.url');

      if (!url) {
        throw new Error('AIRecommendationService: No URL configured at api.openRouter.url');
      }

      log(this.logScope, 'debug', 'AI URL:', [url]);

      // Use provided model or fall back to config default
      const finalModel = model || configModel;

      // Check cache first (cache by location and model for efficiency)
      //TODO: resortCount should not be what we use here.  We should use the resort ids here or hash them
      const cacheKey = { location: req.location, model: finalModel, resortCount: req.resorts.length };
      const cached = await this.getCachedData('fetchRecommendation', cacheKey, options.bypassCache);
      if (cached) {
        log(this.logScope, 'debug', 'Using cached AI recommendation');
        return cached;
      }

      log(this.logScope, 'info', '🤖 Fetching AI recommendation from API', [{ 
        model: finalModel, 
        temperature, 
        maxTokens, 
        resortCount: req.resorts.length,
        hasResorts: req.resorts.length > 0 
      }]);

      // Prepare data for AI prompt
      const location = req.location;
      const timeOfDay = new Date().toISOString();
      const userWeather = safeStringify(req.userWeather);

      const resortSnowConditions = req.resorts.length > 0 
        ? req.resorts.map((resort) => `${resort.name}: ${safeStringify(resort.ski)}`).join('\n')
        : 'No ski resorts found in the area';
      const resortForecasts = req.resorts.length > 0
        ? req.resorts.map((resort) => `${resort.name}: ${safeStringify(resort.forecast)}`).join('\n')
        : 'No ski resorts found in the area';

      // Use ES6-style interpolator with error handling
      let prompt = '';
      try {
        prompt = interpolateES6(userPromptTemplate, {
          location,
          timeOfDay,
          userWeather,
          resortSnowConditions,
          resortForecasts,
        });
        log(this.logScope, 'debug', 'Prompt interpolation succeeded.', [prompt]);
      } catch (templateError) {
        log(this.logScope, 'error', '🚨 Template interpolation failed:', [templateError]);
        throw templateError;
      }

      // Log the constructed prompt (truncate if very long)
      log(this.logScope, 'debug', 'Prompt:', [prompt.length > 500 ? prompt.slice(0, 500) + '... [truncated]' : prompt]);
      log(this.logScope, 'info', 'AI Config:', [{ model: finalModel, temperature, maxTokens }]);

      // Prepare request payload using config values
      const requestPayload = {
        model: finalModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt },
        ],
        max_tokens: maxTokens,
        temperature: temperature,
      };

      log(this.logScope, 'info', 'Sending request to AI service:', [{
        url,
        model: finalModel,
        promptLength: prompt.length,
        resortCount: req.resorts.length,
        temperature,
        maxTokens
      }]);

      // Get timeout and retries from config
      const timeout = ConfigService.get('api.openRouter.timeout', 10000); // AI calls can take longer
      const retries = ConfigService.get('api.openRouter.retries', 1); // AI calls are expensive, fewer retries

      // Use BaseApiService's fetch method with URL parameter replacement and custom options for POST
      const response = await this.fetchData(url, {}, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestPayload),
      }, timeout, retries);

      const data = await response.json();
      let recommendation = data.choices?.[0]?.message?.content?.trim() || 'No recommendation available.';
      
      // Remove leading/trailing quotes (single or double), including whitespace/newlines
      recommendation = recommendation.replace(/^['"]+|['"]+$/g, '').trim();
      
      const result: AIRecommendationResponse = { recommendation };

      // Cache the result with shorter TTL since recommendations are time-sensitive
      const ttl = ConfigService.get('api.openRouter.ttl', 600000); // 10 minutes default
      await this.setCachedData('fetchRecommendation', cacheKey, result, ttl);
      
      log(this.logScope, 'info', 'Recommendation received:', [recommendation.length > 200 ? recommendation.slice(0, 200) + '... [truncated]' : recommendation]);
      return result;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(
        error,
        this.serviceName,
        'Failed to fetch AI recommendation'
      );
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchRecommendation');
      throw apiError;
    }
  }
}

// Export singleton instance
export const aiRecommendationService = AIRecommendationService.getInstance();

// // Legacy function export for backward compatibility
// // TODO: Remove this once all consumers are updated to use the service instance
// export async function getAIRecommendation(req: AIRecommendationRequest, model: string = 'openai/gpt-3.5-turbo'): Promise<AIRecommendationResponse> {
//   return aiRecommendationService.fetchRecommendation(req, model);
// }

// // TODO: Move prompt template to config, add support for different AI providers 
// // TODO: Update legacy getAIRecommendation to support bypassCache if still used elsewhere 