import ENV from '../constants/Env';
import { ApiErrorHandler } from '../utils/ApiErrorHandler';
import { BaseApiService } from '../utils/BaseApiService';
import { ValidationUtils } from '../utils/ValidationUtils';
import ConfigService from './appConfigService';
import { log } from './loggerService';

// Type definitions for probabilistic snowfall API
export interface ProbabilisticSnowfallApiResponse {
  snowProbabilisticExceedThresholds: number[];
  timeSpan: {
    expirationTimeUtc: number[];
    qpfSnow: number[];
    snowProbabilisticExceedThresholdOne: number[];
    snowProbabilisticExceedThresholdTwo: number[];
    snowProbabilisticExceedThresholdThree: number[];
    snowProbabilisticExceedThresholdFour: number[];
    snowProbabilisticExceedThresholdFive: number[];
    snowProbabilisticExceedThresholdSix: number[];
    snowProbabilisticExceedThresholdSeven: number[];
    snowProbabilisticExceedThresholdEight: number[];
    validTimeLocal?: string[];
    validTimeUtc?: string[];
  };
}

export interface SnowfallBin {
  label: string;
  probability: number;
  range: string;
}

export interface ProbabilisticSnowfallData {
  bins: SnowfallBin[];
  cardType: 'primary' | 'alternate';
  timespan: '24hr' | '48hr' | '72hr';
  expectedSnowfall: number;
  validTime: string;
  summary: string;
  isMetric?: boolean;
}

/**
 * Service for fetching probabilistic snowfall data
 * Following external-data-loading-pattern with complex binning logic
 */
export class ProbabilisticSnowfallService extends BaseApiService<ProbabilisticSnowfallData> {
  protected readonly serviceName = 'ProbabilisticSnowfallService';

  constructor() {
    super('ProbabilisticSnowfallService');
  }

  protected getDefaultUrl(): string {
    return ConfigService.get('api.probabilistic-snowfall.url');
  }

  /**
   * Fetch probabilistic snowfall data for a location
   * @param geocode Location in "lat,lon" format
   * @param expectedSnowfall The qpfSnow value from current conditions for binning logic
   * @param timespan The timespan to filter for (24hr, 48hr, 72hr)
   * @param options Cache bypass options and units
   * @returns Promise<ProbabilisticSnowfallData>
   */
  public async fetchProbabilisticSnowfall(
    geocode: string, 
    expectedSnowfall: number,
    timespan: '24hr' | '48hr' | '72hr' = '24hr',
    options: { bypassCache?: boolean; isMetric?: boolean } = {}
  ): Promise<ProbabilisticSnowfallData> {
    try {
      // Validate geocode format
      const validation = ValidationUtils.validateCoordinates(geocode);
      if (!validation.isValid) {
        throw new Error(`Invalid geocode format: ${validation.errors.join(', ')}`);
      }

      // Check cache first (unless bypassed)
      const cacheKey = 'fetchProbabilisticSnowfall';
      const cacheParams = { geocode, expectedSnowfall, timespan, isMetric: options.isMetric };
      const cached = await this.getCachedData(cacheKey, cacheParams, options.bypassCache);
      if (cached) {
        log(`[${this.serviceName}]`, 'debug', `Returning cached probabilistic snowfall for geocode: ${geocode}, timespan: ${timespan}`);
        return cached;
      }

      // Get API configuration
      const apiKey = ENV.sunApiToken;
      if (!apiKey) {
        throw new Error('Missing SUN_API_TOKEN in environment variables');
      }

      const timeout = ConfigService.get('api.probabilistic-snowfall.timeout', 10000);
      const retries = ConfigService.get('api.probabilistic-snowfall.retries', 2);

      log(`[${this.serviceName}]`, 'info', `Fetching probabilistic snowfall for geocode: ${geocode}, timespan: ${timespan}`);

      const url = this.getDefaultUrl();
      const urlParams = {
        geocode,
        apiKey,
        // Add units parameter if metric
        ...(options.isMetric && { units: 'm' })
      };
      
      // Detailed logging for debugging
      log(`[${this.serviceName}]`, 'debug', `Base URL from config: ${url}`);
      log(`[${this.serviceName}]`, 'debug', `URL parameters:`, [urlParams]);
      log(`[${this.serviceName}]`, 'debug', `Request timeout: ${timeout}ms, retries: ${retries}`);
      
      // Fetch from API
      const response = await this.fetchData(url, urlParams, {}, timeout, retries);
      
      log(`[${this.serviceName}]`, 'debug', `Response status: ${response.status} ${response.statusText}`);
      log(`[${this.serviceName}]`, 'debug', `Response headers:`, [Object.fromEntries(response.headers.entries())]);

      // Get raw response text for debugging
      const responseText = await response.text();
      log(`[${this.serviceName}]`, 'debug', `Raw response text (first 500 chars): ${responseText.substring(0, 500)}`);
      
      // Try to parse JSON
      let apiResponse: ProbabilisticSnowfallApiResponse;
      try {
        apiResponse = JSON.parse(responseText);
        log(`[${this.serviceName}]`, 'debug', `Successfully parsed JSON response`);
      } catch (parseError) {
        log(`[${this.serviceName}]`, 'error', `Failed to parse response as JSON:`, [parseError]);
        log(`[${this.serviceName}]`, 'error', `Full response text: ${responseText}`);
        throw parseError;
      }

      // Validate response structure
      if (!apiResponse.timeSpan || !apiResponse.timeSpan.snowProbabilisticExceedThresholdOne || !Array.isArray(apiResponse.timeSpan.snowProbabilisticExceedThresholdOne)) {
        throw new Error('Invalid API response: missing or invalid probabilistic snowfall data');
      }

      // Filter data for the specified timespan (24hr = first element, 48hr = second, 72hr = third)
      const timespanIndex = timespan === '24hr' ? 0 : timespan === '48hr' ? 1 : 2;
      if (apiResponse.timeSpan.snowProbabilisticExceedThresholdOne.length <= timespanIndex) {
        throw new Error(`Insufficient data for timespan: ${timespan}`);
      }

      // Extract expectedSnowfall from qpfSnow (use it instead of the parameter)
      const actualExpectedSnowfall = apiResponse.timeSpan.qpfSnow[timespanIndex] || 0;
      log(`[${this.serviceName}]`, 'info', `Using qpfSnow from API: ${actualExpectedSnowfall} (was passed: ${expectedSnowfall})`);

      // Extract threshold values for the specified timespan
      const thresholds = {
        one: apiResponse.timeSpan.snowProbabilisticExceedThresholdOne[timespanIndex] || 0,
        two: apiResponse.timeSpan.snowProbabilisticExceedThresholdTwo[timespanIndex] || 0,
        three: apiResponse.timeSpan.snowProbabilisticExceedThresholdThree[timespanIndex] || 0,
        four: apiResponse.timeSpan.snowProbabilisticExceedThresholdFour[timespanIndex] || 0,
        five: apiResponse.timeSpan.snowProbabilisticExceedThresholdFive[timespanIndex] || 0,
        six: apiResponse.timeSpan.snowProbabilisticExceedThresholdSix[timespanIndex] || 0,
        seven: apiResponse.timeSpan.snowProbabilisticExceedThresholdSeven[timespanIndex] || 0,
        eight: apiResponse.timeSpan.snowProbabilisticExceedThresholdEight[timespanIndex] || 0,
      };

      // Convert thresholds to percentages if they're decimals
      const normalizedThresholds = this.normalizeThresholds(thresholds);
      
      log(`[${this.serviceName}]`, 'debug', `Normalized thresholds:`, [normalizedThresholds]);

      // Apply complex binning logic (use actualExpectedSnowfall from API)
      const { bins, cardType } = this.applyBinningLogic(
        actualExpectedSnowfall, 
        normalizedThresholds, 
        options.isMetric || false
      );

      // Create the response data
      const probabilisticData: ProbabilisticSnowfallData = {
        bins,
        cardType,
        timespan,
        expectedSnowfall: actualExpectedSnowfall,
        validTime: apiResponse.timeSpan.validTimeLocal?.[timespanIndex] || apiResponse.timeSpan.expirationTimeUtc[timespanIndex]?.toString() || '',
        summary: this.generateSummary(bins, cardType, timespan, options.isMetric || false),
        isMetric: options.isMetric || false
      };

      // Cache the result
      const cacheTtl = ConfigService.get('api.probabilistic-snowfall.cacheTtl', 300000); // 5 minutes default
      await this.setCachedData(cacheKey, cacheParams, probabilisticData, cacheTtl);

      log(`[${this.serviceName}]`, 'info', `Successfully fetched probabilistic snowfall for geocode: ${geocode}, cardType: ${cardType}`);
      return probabilisticData;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'Failed to fetch probabilistic snowfall');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchProbabilisticSnowfall');
      throw apiError;
    }
  }

  /**
   * Normalize threshold values to percentages (0-100)
   */
  private normalizeThresholds(thresholds: any): any {
    const normalized = { ...thresholds };
    
    // Check if values are decimals (0-1) and convert to percentages
    Object.keys(normalized).forEach(key => {
      if (normalized[key] <= 1) {
        normalized[key] = normalized[key] * 100;
      }
    });
    
    return normalized;
  }

  /**
   * Apply complex binning logic based on expected snowfall amount
   * Returns bins and determines whether to show Primary or Alternate card
   */
  private applyBinningLogic(
    expectedSnowfall: number, 
    thresholds: any, 
    isMetric: boolean
  ): { bins: SnowfallBin[], cardType: 'primary' | 'alternate' } {
    
    // Convert expectedSnowfall to appropriate units for comparison
    const snowfallForComparison = isMetric ? 
      this.convertCmToInches(expectedSnowfall) : expectedSnowfall;
    
    // Generate primary bins based on snowfall amount
    const primaryBins = this.generatePrimaryBins(snowfallForComparison, thresholds, isMetric);
    
    // Determine card type based on alignment logic
    const { cardType, mostLikelyBin } = this.determineCardType(
      snowfallForComparison, 
      primaryBins, 
      isMetric
    );
    
    let finalBins: SnowfallBin[];
    
    if (cardType === 'primary') {
      finalBins = primaryBins;
    } else {
      // Generate alternate (PoE) bins
      finalBins = this.generateAlternateBins(snowfallForComparison, thresholds, isMetric);
    }

    // Ensure all probabilities are positive
    finalBins = finalBins.map(bin => ({
      ...bin,
      probability: Math.max(0, bin.probability)
    }));

    log(`[${this.serviceName}]`, 'debug', `Applied binning logic: cardType=${cardType}, expectedSnowfall=${expectedSnowfall}, isMetric=${isMetric}`);
    log(`[${this.serviceName}]`, 'debug', `Final bins:`, [finalBins]);

    return { bins: finalBins, cardType };
  }

  /**
   * Generate primary bins based on specification logic
   * Following the exact guide provided for Light/Light-Moderate/Moderate-Heavy/Heavy snowfall cases
   */
  private generatePrimaryBins(
    expectedSnowfall: number, 
    thresholds: any, 
    isMetric: boolean
  ): SnowfallBin[] {
    
    if (expectedSnowfall < 3) {
      // Light Snowfall Amount case
      return isMetric ? [
        { label: '< 2 cm', probability: thresholds.one - thresholds.two, range: '< 2' },
        { label: '2-7 cm', probability: thresholds.two - thresholds.three, range: '2-7' },
        { label: '7-12 cm', probability: thresholds.three - thresholds.four, range: '7-12' },
        { label: '12-30 cm', probability: thresholds.four - thresholds.six, range: '12-30' },
        { label: '30+ cm', probability: thresholds.six, range: '30+' }
      ] : [
        { label: '< 1 in', probability: thresholds.one - thresholds.two, range: '< 1' },
        { label: '1-3 in', probability: thresholds.two - thresholds.three, range: '1-3' },
        { label: '3-5 in', probability: thresholds.three - thresholds.four, range: '3-5' },
        { label: '5-12 in', probability: thresholds.four - thresholds.six, range: '5-12' },
        { label: '12+ in', probability: thresholds.six, range: '12+' }
      ];
    } else if (expectedSnowfall >= 3 && expectedSnowfall < 7) {
      // Light-Moderate Snowfall Amount case
      return isMetric ? [
        { label: '0-7 cm', probability: thresholds.one - thresholds.three, range: '0-7' },
        { label: '7-12 cm', probability: thresholds.three - thresholds.four, range: '7-12' },
        { label: '12-20 cm', probability: thresholds.four - thresholds.five, range: '12-20' },
        { label: '20-30 cm', probability: thresholds.five - thresholds.six, range: '20-30' },
        { label: '30+ cm', probability: thresholds.six, range: '30+' }
      ] : [
        { label: '< 3 in', probability: thresholds.one - thresholds.three, range: '< 3' },
        { label: '3-5 in', probability: thresholds.three - thresholds.four, range: '3-5' },
        { label: '5-8 in', probability: thresholds.four - thresholds.five, range: '5-8' },
        { label: '8-12 in', probability: thresholds.five - thresholds.six, range: '8-12' },
        { label: '12+ in', probability: thresholds.six, range: '12+' }
      ];
    } else if (expectedSnowfall >= 7 && expectedSnowfall < 12) {
      // Moderate-Heavy Snowfall Amount case
      return isMetric ? [
        { label: '< 7 cm', probability: thresholds.one - thresholds.three, range: '< 7' },
        { label: '7-20 cm', probability: thresholds.three - thresholds.five, range: '7-20' },
        { label: '20-30 cm', probability: thresholds.five - thresholds.six, range: '20-30' },
        { label: '30-45 cm', probability: thresholds.six - thresholds.seven, range: '30-45' },
        { label: '45+ cm', probability: thresholds.seven, range: '45+' }
      ] : [
        { label: '< 3 in', probability: thresholds.one - thresholds.three, range: '< 3' },
        { label: '3-8 in', probability: thresholds.three - thresholds.five, range: '3-8' },
        { label: '8-12 in', probability: thresholds.five - thresholds.six, range: '8-12' },
        { label: '12-18 in', probability: thresholds.six - thresholds.seven, range: '12-18' },
        { label: '18+ in', probability: thresholds.seven, range: '18+' }
      ];
    } else {
      // Heavy Snowfall Amount case (expectedSnowfall >= 12)
      return isMetric ? [
        { label: '< 12 cm', probability: thresholds.one - thresholds.four, range: '< 12' },
        { label: '12-20 cm', probability: thresholds.four - thresholds.five, range: '12-20' },
        { label: '20-30 cm', probability: thresholds.five - thresholds.six, range: '20-30' },
        { label: '30-45 cm', probability: thresholds.six - thresholds.seven, range: '30-45' },
        { label: '45+ cm', probability: thresholds.seven, range: '45+' }
      ] : [
        { label: '< 5 in', probability: thresholds.one - thresholds.four, range: '< 5' },
        { label: '5-8 in', probability: thresholds.four - thresholds.five, range: '5-8' },
        { label: '8-12 in', probability: thresholds.five - thresholds.six, range: '8-12' },
        { label: '12-18 in', probability: thresholds.six - thresholds.seven, range: '12-18' },
        { label: '18+ in', probability: thresholds.seven, range: '18+' }
      ];
    }
  }

  /**
   * Determine card type based on alignment logic with special metric override
   */
  private determineCardType(
    expectedSnowfall: number, 
    primaryBins: SnowfallBin[], 
    isMetric: boolean
  ): { cardType: 'primary' | 'alternate', mostLikelyBin: SnowfallBin | null } {
    
    // Find the bin with the highest probability (excluding "Some Snow" bin)
    let maxProbability = 0;
    let mostLikelyBin: SnowfallBin | null = null;
    
    for (const bin of primaryBins) {
      if (bin.probability > maxProbability) {
        maxProbability = bin.probability;
        mostLikelyBin = bin;
      }
    }

    // Handle edge case: if all probabilities are 0, default to the first bin
    // This ensures primary card is shown when there's no snow forecast
    if (mostLikelyBin === null && primaryBins.length > 0) {
      mostLikelyBin = primaryBins[0];
      log(`[${this.serviceName}]`, 'debug', `All probabilities are 0, defaulting to first bin: ${mostLikelyBin.range}`);
    }

    // Check if expectedSnowfall falls within the highest probability bin
    let isAligned = false;
    if (mostLikelyBin?.range) {
      isAligned = this.isSnowfallInRange(expectedSnowfall, mostLikelyBin.range, isMetric);
    }

    // Apply special metric units scenario override
    if (isAligned && isMetric && expectedSnowfall < 7) {
      log(`[${this.serviceName}]`, 'debug', `Applying special metric units override: expectedSnowfall=${expectedSnowfall}cm < 7cm`);
      isAligned = false;
    }

    const cardType: 'primary' | 'alternate' = isAligned ? 'primary' : 'alternate';
    
    log(`[${this.serviceName}]`, 'debug', `Alignment check: expectedSnowfall=${expectedSnowfall}, mostLikelyBin=${mostLikelyBin?.range || 'null'}, isAligned=${isAligned}, cardType=${cardType}`);
    
    return { cardType, mostLikelyBin };
  }

  /**
   * Generate alternate (Probability of Exceedance) bins
   * Following Appendix 3 - Logic for determining aggregated snowfall accumulation bins (Alternate Card)
   */
  private generateAlternateBins(
    expectedSnowfall: number, 
    thresholds: any, 
    isMetric: boolean
  ): SnowfallBin[] {
    
    if (expectedSnowfall < 3) {
      // Light Snowfall Amount case
      return isMetric ? [
        { label: 'Some Snow', probability: thresholds.one, range: 'some' },
        { label: 'At least 2 cm', probability: thresholds.two, range: 'at-least-2cm' },
        { label: 'At least 12 cm', probability: thresholds.four, range: 'at-least-12cm' },
        { label: 'At least 20 cm', probability: thresholds.five, range: 'at-least-20cm' },
        { label: 'More than 30 cm', probability: thresholds.six, range: 'more-than-30cm' }
      ] : [
        { label: 'Some Snow', probability: thresholds.one, range: 'some' },
        { label: 'At least 1 in', probability: thresholds.two, range: 'at-least-1in' },
        { label: 'At least 5 in', probability: thresholds.four, range: 'at-least-5in' },
        { label: 'At least 8 in', probability: thresholds.five, range: 'at-least-8in' },
        { label: 'More than 12 in', probability: thresholds.six, range: 'more-than-12in' }
      ];
    } else if (expectedSnowfall >= 3 && expectedSnowfall < 12) {
      // Moderate Snowfall Amount case
      return isMetric ? [
        { label: 'Some Snow', probability: thresholds.one, range: 'some' },
        { label: 'At least 7 cm', probability: thresholds.three, range: 'at-least-7cm' },
        { label: 'At least 20 cm', probability: thresholds.five, range: 'at-least-20cm' },
        { label: 'At least 30 cm', probability: thresholds.six, range: 'at-least-30cm' },
        { label: 'More than 45 cm', probability: thresholds.seven, range: 'more-than-45cm' }
      ] : [
        { label: 'Some Snow', probability: thresholds.one, range: 'some' },
        { label: 'At least 3 in', probability: thresholds.three, range: 'at-least-3in' },
        { label: 'At least 8 in', probability: thresholds.five, range: 'at-least-8in' },
        { label: 'At least 12 in', probability: thresholds.six, range: 'at-least-12in' },
        { label: 'More than 18 in', probability: thresholds.seven, range: 'more-than-18in' }
      ];
    } else {
      // Heavy Snowfall Amount case (expectedSnowfall >= 12)
      return isMetric ? [
        { label: 'At least 7 cm', probability: thresholds.three, range: 'at-least-7cm' },
        { label: 'At least 20 cm', probability: thresholds.five, range: 'at-least-20cm' },
        { label: 'At least 30 cm', probability: thresholds.six, range: 'at-least-30cm' },
        { label: 'At least 45 cm', probability: thresholds.seven, range: 'at-least-45cm' },
        { label: 'More than 60 cm', probability: thresholds.eight, range: 'more-than-60cm' }
      ] : [
        { label: 'At least 3 in', probability: thresholds.three, range: 'at-least-3in' },
        { label: 'At least 8 in', probability: thresholds.five, range: 'at-least-8in' },
        { label: 'At least 12 in', probability: thresholds.six, range: 'at-least-12in' },
        { label: 'At least 18 in', probability: thresholds.seven, range: 'at-least-18in' },
        { label: 'More than 24 in', probability: thresholds.eight, range: 'more-than-24in' }
      ];
    }
  }

  /**
   * Check if snowfall amount falls within a given range
   * Used only for primary bins alignment checking
   * Uses tolerance for floating point comparisons to handle boundary conditions
   */
  private isSnowfallInRange(snowfall: number, range: string, isMetric: boolean): boolean {
    // Convert metric ranges to imperial for comparison if needed
    const checkValue = isMetric ? this.convertCmToInches(snowfall) : snowfall;
    
    // Small tolerance for floating point comparisons (0.001 inches)
    const TOLERANCE = 0.001;
    
    // Helper function for tolerant comparisons
    const isEqual = (a: number, b: number) => Math.abs(a - b) < TOLERANCE;
    const isLessOrEqual = (a: number, b: number) => a < b || isEqual(a, b);
    const isGreaterOrEqual = (a: number, b: number) => a > b || isEqual(a, b);
    
    switch (range) {
      // Imperial ranges
      case '< 1':
        return checkValue < 1;
      case '1-3':
        return isGreaterOrEqual(checkValue, 1) && checkValue < 3;
      case '< 3':
        return checkValue < 3;
      case '3-5':
        return isGreaterOrEqual(checkValue, 3) && checkValue < 5;
      case '5-8':
        return isGreaterOrEqual(checkValue, 5) && checkValue < 8;
      case '3-8':
        return isGreaterOrEqual(checkValue, 3) && checkValue < 8;
      case '8-12':
        return isGreaterOrEqual(checkValue, 8) && checkValue < 12;
      case '5-12':
        return isGreaterOrEqual(checkValue, 5) && checkValue < 12;
      case '12-18':
        return isGreaterOrEqual(checkValue, 12) && checkValue < 18;
      case '12+':
        return isGreaterOrEqual(checkValue, 12);
      case '18+':
        return isGreaterOrEqual(checkValue, 18);
      case '< 5':
        return checkValue < 5;
      // Metric ranges (convert to inches for comparison)
      case '< 2':
        return checkValue < this.convertCmToInches(2);
      case '2-7':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(2)) && checkValue < this.convertCmToInches(7);
      case '0-7':
        return checkValue >= 0 && checkValue < this.convertCmToInches(7);
      case '7-12':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(7)) && checkValue < this.convertCmToInches(12);
      case '7-20':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(7)) && checkValue < this.convertCmToInches(20);
      case '12-20':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(12)) && checkValue < this.convertCmToInches(20);
      case '12-30':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(12)) && checkValue < this.convertCmToInches(30);
      case '20-30':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(20)) && checkValue < this.convertCmToInches(30);
      case '30-45':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(30)) && checkValue < this.convertCmToInches(45);
      case '< 7':
        return checkValue < this.convertCmToInches(7);
      case '< 12':
        return checkValue < this.convertCmToInches(12);
      case '30+':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(30));
      case '45+':
        return isGreaterOrEqual(checkValue, this.convertCmToInches(45));
      default:
        return false;
    }
  }

  /**
   * Convert centimeters to inches
   */
  private convertCmToInches(cm: number): number {
    return cm / 2.54;
  }

  /**
   * Generate a summary description for the probabilistic snowfall data
   */
  private generateSummary(
    bins: SnowfallBin[], 
    cardType: 'primary' | 'alternate', 
    timespan: string,
    isMetric: boolean
  ): string {
    
    const timespanText = timespan === '24hr' ? '24 hours' : timespan === '48hr' ? '48 hours' : '72 hours';
    
    if (cardType === 'alternate') {
      // For alternate card, use the "Some Snow" bin if it exists, otherwise first bin
      const someSnowBin = bins.find(bin => bin.range === 'some');
      if (someSnowBin) {
        return `There is a ${Math.round(someSnowBin.probability)}% chance of snowfall in the next ${timespanText}.`;
      }
      // If no "Some Snow" bin (Heavy snowfall case), use first bin
      const firstBin = bins[0];
      return `There is a ${Math.round(firstBin.probability)}% chance of ${firstBin.label.toLowerCase()} in the next ${timespanText}.`;
    }

    // For primary card, find the bin with the highest probability
    let maxProbBin = bins[0];
    for (const bin of bins) {
      if (bin.probability > maxProbBin.probability) {
        maxProbBin = bin;
      }
    }

    return `There is a ${Math.round(maxProbBin.probability)}% chance of snowfall in the next ${timespanText}, with ${maxProbBin.label} most likely.`;
  }
}

// Export singleton instance
export const probabilisticSnowfallService = new ProbabilisticSnowfallService(); 