import ENV from '../constants/Env';
import { ApiErrorHandler } from '../utils/ApiErrorHandler';
import { BaseApiService } from '../utils/BaseApiService';
import { ValidationUtils } from '../utils/ValidationUtils';
import ConfigService from './appConfigService';
import { log } from './loggerService';

// Type definitions for current conditions API
export interface CurrentConditionsData {
  qpfSnow: number;
  temperature: number;
  windSpeed: number;
  windDirection: number;
  humidity: number;
  visibility: number;
  pressure: number;
  dewpoint: number;
  uvIndex: number;
  iconCode: number;
  narrative: string;
  validTimeLocal: string;
  validTimeUtc: string;
}

interface CurrentConditionsApiResponse {
  qpfSnow: number[];
  temperature: number[];
  windSpeed: number[];
  windDirection: number[];
  humidity: number[];
  visibility: number[];
  pressure: number[];
  dewpoint: number[];
  uvIndex: number[];
  iconCode: number[];
  narrative: string[];
  validTimeLocal: string[];
  validTimeUtc: string[];
}

/**
 * Service for fetching current weather conditions
 * Following external-data-loading-pattern
 */
export class CurrentConditionsService extends BaseApiService<CurrentConditionsData> {
  protected readonly serviceName = 'CurrentConditionsService';

  constructor() {
    super('CurrentConditionsService');
  }

  protected getDefaultUrl(): string {
    return ConfigService.get('api.current-conditions.url');
  }

  /**
   * Fetch current weather conditions for a location
   * @param geocode Location in "lat,lon" format
   * @param options Cache bypass options
   * @returns Promise<CurrentConditionsData>
   */
  public async fetchCurrentConditions(geocode: string, options: { bypassCache?: boolean } = {}): Promise<CurrentConditionsData> {
    try {
      // Validate geocode format
      const validation = ValidationUtils.validateCoordinates(geocode);
      if (!validation.isValid) {
        throw new Error(`Invalid geocode format: ${validation.errors.join(', ')}`);
      }

      // Check cache first (unless bypassed)
      const cacheKey = 'fetchCurrentConditions';
      const cached = await this.getCachedData(cacheKey, { geocode }, options.bypassCache);
      if (cached) {
        log(`[${this.serviceName}]`, 'debug', `Returning cached current conditions for geocode: ${geocode}`);
        return cached;
      }

      // Get API configuration
      const apiKey = ENV.sunApiToken;
      if (!apiKey) {
        throw new Error('Missing SUN_API_TOKEN in environment variables');
      }

      const timeout = ConfigService.get('api.current-conditions.timeout', 10000);
      const retries = ConfigService.get('api.current-conditions.retries', 2);

            log(`[${this.serviceName}]`, 'info', `Fetching current conditions for geocode: ${geocode}`);

      const url = this.getDefaultUrl();
      const urlParams = {
        geocode,
        apiKey
      };
      
      // Detailed logging for debugging
      log(`[${this.serviceName}]`, 'debug', `Base URL from config: ${url}`);
      log(`[${this.serviceName}]`, 'debug', `URL parameters:`, [urlParams]);
      log(`[${this.serviceName}]`, 'debug', `Request timeout: ${timeout}ms, retries: ${retries}`);
      
      // Fetch from API
      const response = await this.fetchData(url, urlParams, {}, timeout, retries);
      
      log(`[${this.serviceName}]`, 'debug', `Response status: ${response.status} ${response.statusText}`);
      log(`[${this.serviceName}]`, 'debug', `Response headers:`, [Object.fromEntries(response.headers.entries())]);

      // Get raw response text for debugging
      const responseText = await response.text();
      log(`[${this.serviceName}]`, 'debug', `Raw response text (first 500 chars): ${responseText.substring(0, 500)}`);
      
      // Try to parse JSON
      let apiResponse: CurrentConditionsApiResponse;
      try {
        apiResponse = JSON.parse(responseText);
        log(`[${this.serviceName}]`, 'debug', `Successfully parsed JSON response`);
      } catch (parseError) {
        log(`[${this.serviceName}]`, 'error', `Failed to parse response as JSON:`, [parseError]);
        log(`[${this.serviceName}]`, 'error', `Full response text: ${responseText}`);
        throw parseError;
      }

      // Validate response structure
      if (!apiResponse.qpfSnow || !Array.isArray(apiResponse.qpfSnow)) {
        throw new Error('Invalid API response: missing or invalid qpfSnow data');
      }

      // Transform API response to our format (take first element since it's current conditions)
      const currentConditions: CurrentConditionsData = {
        qpfSnow: apiResponse.qpfSnow[0] || 0,
        temperature: apiResponse.temperature[0] || 0,
        windSpeed: apiResponse.windSpeed[0] || 0,
        windDirection: apiResponse.windDirection[0] || 0,
        humidity: apiResponse.humidity[0] || 0,
        visibility: apiResponse.visibility[0] || 0,
        pressure: apiResponse.pressure[0] || 0,
        dewpoint: apiResponse.dewpoint[0] || 0,
        uvIndex: apiResponse.uvIndex[0] || 0,
        iconCode: apiResponse.iconCode[0] || 0,
        narrative: apiResponse.narrative[0] || '',
        validTimeLocal: apiResponse.validTimeLocal[0] || '',
        validTimeUtc: apiResponse.validTimeUtc[0] || ''
      };

      // Cache the result
      const cacheTtl = ConfigService.get('api.current-conditions.cacheTtl', 300000); // 5 minutes default
      await this.setCachedData(cacheKey, { geocode }, currentConditions, cacheTtl);

      log(`[${this.serviceName}]`, 'info', `Successfully fetched current conditions for geocode: ${geocode}`);
      return currentConditions;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'Failed to fetch current conditions');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchCurrentConditions');
      throw apiError;
    }
  }
}

// Export singleton instance
export const currentConditionsService = new CurrentConditionsService(); 