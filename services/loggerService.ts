// Logger Service for consistent logging throughout the app
// Supports log level and scope filtering based on ConfigService

import ConfigService from './appConfigService';

export const LOG_LEVELS = ['debug', 'info', 'warn', 'error'] as const;
export type LogLevel = typeof LOG_LEVELS[number];

/**
 * Get logging config from ConfigService
 */
function getLogConfig() {
  return {
    level: ConfigService.get('logging.level', 'info') as LogLevel,
    scope: ConfigService.get('logging.scope', []) as string[]
  };
}

/**
 * Converts a wildcard pattern to a regex pattern
 * Supports * as wildcard for any characters
 * @param {string} pattern - Pattern with wildcards (e.g., "[Leaderboard*]")
 * @returns {RegExp} - Compiled regex pattern
 */
function wildcardToRegex(pattern: string): RegExp {
  // Escape special regex characters except *
  const escaped = pattern.replace(/[.+?^${}()|[\]\\]/g, '\\$&');
  // Replace * with .* for regex matching
  const regexPattern = escaped.replace(/\*/g, '.*');
  return new RegExp(`^${regexPattern}$`);
}

/**
 * Check if a prefix matches any of the scope patterns (with wildcard support)
 * @param {string} prefix - The log prefix to check
 * @param {string[]} scope - Array of scope patterns (may include wildcards)
 * @returns {boolean} - True if prefix matches any scope pattern
 */
function matchesScope(prefix: string, scope: string[]): boolean {
  return scope.some((pattern: string) => {
    if (pattern.includes('*')) {
      // Use wildcard matching
      const regex = wildcardToRegex(pattern);
      return regex.test(prefix);
    } else {
      // Use simple prefix matching for patterns without wildcards
      return prefix.startsWith(pattern);
    }
  });
}

/**
 * Log function that respects ConfigService log level and scope settings
 * Note: Scope filtering only applies to 'info' and 'debug' levels - 'warn' and 'error' always come through
 * @param {string} prefix - e.g. '[ResortCard]'
 * @param {LogLevel} level
 * @param {string} message
 * @param {any[]} args
 */
export function log(prefix: string, level: LogLevel, message: string, args: any[] = []) {
  const { level: configLevel, scope } = getLogConfig();
  const levelIdx = LOG_LEVELS.indexOf(level);
  const configLevelIdx = LOG_LEVELS.indexOf(configLevel);
  
  // Only log if level is high enough
  if (levelIdx < configLevelIdx) {
    return;
  }
  
  // For warn/error, always log regardless of scope
  // For info/debug, check scope filtering
  const shouldLog = level === 'warn' || level === 'error' || 
                   !scope?.length || 
                   matchesScope(prefix, scope);
  
  if (shouldLog) {
    // eslint-disable-next-line no-console
    console[level === 'debug' ? 'log' : level](`${prefix} [${level.toUpperCase()}] ${message}`, ...args);
  }
}

// TODO: Add support for async/remote logging, log file output, etc. 