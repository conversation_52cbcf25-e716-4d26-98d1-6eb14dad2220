import ENV from '../constants/Env';
import { ApiErrorHandler } from '../utils/ApiErrorHandler';
import { BaseApiService } from '../utils/BaseApiService';
import ConfigService from './appConfigService';
import { log } from './loggerService';

export interface ResortData {
  id: string;
  name: string;
  location: string;
  distance: string;
  latitude: number;
  longitude: number;
  ianaTimeZone?: string;
}

export interface ResortApiResponse {
  location: {
    skiName: string[];
    skiId: string[];
    adminDistrictCode: string[];
    countryCode: string[];
    distanceMi: number[];
    latitude: number[];
    longitude: number[];
    ianaTimeZone: string[];
  };
}

/**
 * Service for fetching ski resort data from weather.com API
 * Follows the external data loading pattern with BaseApiService
 */
export class ResortService extends BaseApiService<ResortData[]> {
  private static instance: ResortService;
  private listeners: Set<(data: ResortData[]) => void> = new Set();

  private constructor() {
    super('ResortService');
  }

  public static getInstance(): ResortService {
    if (!ResortService.instance) {
      ResortService.instance = new ResortService();
    }
    return ResortService.instance;
  }

  /**
   * Fetches nearby ski resorts for a given location
   * @param geocode Latitude,Longitude string (e.g., '33.74,-84.39')
   * @param limit Optional number of results to return
   * @param options Optional settings (bypassCache)
   */
  public async fetchResorts(geocode: string, limit?: number, options: { bypassCache?: boolean } = {}): Promise<ResortData[]> {
    // Define cache key outside try block so it's accessible in catch
    const cacheKey = { geocode, limit };
    
    try {
      // Validate geocode format
      if (!geocode || !geocode.includes(',')) {
        throw new Error('Invalid geocode format. Expected "lat,lon"');
      }

      // Check cache first
      const cached = await this.getCachedData('fetchResorts', cacheKey, options.bypassCache);
      if (cached) {
        log(`[${this.serviceName}]`, 'debug', 'Returning cached resort data', [{ geocode, limit }]);
        return cached;
      }

      // Get configuration
      const baseUrl = ConfigService.get('api.resorts.url', 'https://api.weather.com/v3/location/near');
      const timeout = ConfigService.get('api.resorts.timeout', 10000);
      const retries = ConfigService.get('api.resorts.retries', 2);
      const apiKey = ENV.sunApiToken;

      if (!apiKey) {
        throw new Error('SUN_API_TOKEN is required for resort API');
      }

      // Build URL with parameters
      const url = `${baseUrl}?geocode=${geocode}&product=ski&format=json&apiKey=${apiKey}`;

      // Use fetch directly to handle 404s as valid "no results" responses
      log(`[${this.serviceName}]`, 'debug', `Fetching from: ${url}`);
      
      const response = await fetch(url);
      
      // Handle 404 as "no resorts found" rather than an error
      if (response.status === 404) {
        log(`[${this.serviceName}]`, 'info', `No resorts found near geocode: ${geocode}`);
        
        // Cache the empty result to avoid repeated API calls
        const emptyResult: ResortData[] = [];
        const cacheTtl = ConfigService.get('api.resorts.cacheTtl', 10 * 60 * 1000); // 10 minutes
        await this.setCachedData('fetchResorts', cacheKey, emptyResult, cacheTtl);
        
        // Notify listeners with empty array
        this.notifyListeners(emptyResult);
        
        return emptyResult;
      }
      
      // Handle other HTTP errors
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText || 'Request failed'}`);
      }
      
      const data: ResortApiResponse = await response.json();

      // Transform and validate response
      const resorts = this.transformApiResponse(data, limit);

      // Cache the results
      const cacheTtl = ConfigService.get('api.resorts.cacheTtl', 10 * 60 * 1000); // 10 minutes
      await this.setCachedData('fetchResorts', cacheKey, resorts, cacheTtl);

      // Notify listeners
      this.notifyListeners(resorts);

      log(`[${this.serviceName}]`, 'info', `Fetched ${resorts.length} resorts for geocode: ${geocode}`);
      return resorts;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'Failed to fetch resorts');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchResorts');
      throw apiError;
    }
  }

  /**
   * Transform API response to ResortData array
   */
  private transformApiResponse(data: ResortApiResponse, limit?: number): ResortData[] {
    // Defensive: ensure data.location and required arrays exist
    if (!data.location || !Array.isArray(data.location.skiName)) {
      log(`[${this.serviceName}]`, 'error', 'Malformed resorts API response', [data]);
      throw new Error('Malformed resorts data received from API');
    }

    const { location } = data;
    
    // Ensure all arrays have the same length
    const length = location.skiName.length;
    const requiredArrays = ['skiId', 'adminDistrictCode', 'countryCode', 'latitude', 'longitude'];
    
    for (const arrayName of requiredArrays) {
      if (!Array.isArray(location[arrayName as keyof typeof location]) || 
          location[arrayName as keyof typeof location].length !== length) {
        throw new Error(`Invalid API response: ${arrayName} array length mismatch`);
      }
    }

    // Map API response to ResortData[]
    let resorts: ResortData[] = location.skiName.map((name: string, idx: number) => ({
      id: location.skiId[idx],
      name,
      location: `${location.adminDistrictCode[idx]}, ${location.countryCode[idx]}`,
      distance: location.distanceMi[idx]?.toString() ?? '',
      latitude: location.latitude[idx],
      longitude: location.longitude[idx],
      ianaTimeZone: location.ianaTimeZone[idx],
    }));

    // Sort resorts by distance (closest first)
    resorts.sort((a: ResortData, b: ResortData) => {
      const distanceA = parseFloat(a.distance) || 0;
      const distanceB = parseFloat(b.distance) || 0;
      return distanceA - distanceB;
    });

    // Apply limit if specified
    if (limit !== undefined && limit > 0) {
      resorts = resorts.slice(0, limit);
    }

    return resorts;
  }

  /**
   * Listener management for change notifications
   */
  public addListener(callback: (data: ResortData[]) => void): () => void {
    this.listeners.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  private notifyListeners(data: ResortData[]): void {
    this.listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        log(`[${this.serviceName}]`, 'error', 'Error in listener callback', [error]);
      }
    });
  }

  /**
   * Refresh resorts data for a location (used for background refresh)
   */
  public async refreshResorts(geocode: string, limit?: number): Promise<void> {
    try {
      // Clear cache for this specific request
      await this.clearCache();
      // Fetch fresh data with bypassCache
      await this.fetchResorts(geocode, limit, { bypassCache: true });
      log(`[${this.serviceName}]`, 'info', `Refreshed resort data for geocode: ${geocode}`);
    } catch (error) {
      log(`[${this.serviceName}]`, 'error', 'Failed to refresh resort data', [error]);
    }
  }
}

// Export singleton instance
export const resortService = ResortService.getInstance(); 