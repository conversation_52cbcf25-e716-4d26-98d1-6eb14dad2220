/*




*/

// NOTE: We import local-config.json and app-settings.json directly for React Native compatibility (no fs/path)
import AsyncStorage from '@react-native-async-storage/async-storage';
import appSettings from '../app-settings.json';
import debugConfig from '../debug-config.json';
import localConfig from '../local-config.json';
import { log } from './loggerService';

const REMOTE_CONFIG_CACHE_KEY = 'remoteConfigCache';
const DEBUG_CONFIG_CACHE_KEY = 'debugRuntimeOverrides';

// Simple deep merge helper for objects
function deepMerge(target: any, source: any): any {
  if (typeof target !== 'object' || typeof source !== 'object' || !target || !source) return source ?? target;
  const merged = { ...target };
  for (const key of Object.keys(source)) {
    if (key in target) {
      merged[key] = deepMerge(target[key], source[key]);
    } else {
      merged[key] = source[key];
    }
  }
  return merged;
}

// Check if we're in development mode
function isDev(): boolean {
  return __DEV__ ?? false;
}

async function fetchRemoteConfig(): Promise<any> {
  const url = appSettings.remoteConfigUrl;
  log('[ConfigService]', 'info', `Fetching remote config from: ${url}`);
  try {
    const response = await fetch(url, { cache: 'no-store' });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    const remoteConfig = await response.json();
    log('[ConfigService]', 'debug', 'Fetched remote config successfully', [remoteConfig]);
    // Cache remote config
    await AsyncStorage.setItem(REMOTE_CONFIG_CACHE_KEY, JSON.stringify({
      config: remoteConfig,
      timestamp: Date.now(),
    }));
    return remoteConfig;
  } catch (error) {
    log('[ConfigService]', 'error', 'Failed to fetch remote config', [error]);
    throw error;
  }
}

async function getCachedRemoteConfig() {
  try {
    const cached = await AsyncStorage.getItem(REMOTE_CONFIG_CACHE_KEY);
    if (cached) {
      const parsed = JSON.parse(cached);
      log('[ConfigService]', 'debug', 'Loaded cached remote config', [parsed]);
      return parsed;
    }
  } catch (error) {
    log('[ConfigService]', 'warn', 'Failed to load cached remote config', [error]);
  }
  return null;
}

async function getCachedRuntimeOverrides() {
  if (!isDev()) return null;
  
  try {
    const cached = await AsyncStorage.getItem(DEBUG_CONFIG_CACHE_KEY);
    if (cached) {
      const parsed = JSON.parse(cached);
      log('[ConfigService]', 'debug', 'Loaded cached debug runtime overrides', [parsed]);
      return parsed;
    }
  } catch (error) {
    log('[ConfigService]', 'warn', 'Failed to load cached debug runtime overrides', [error]);
  }
  return null;
}

async function setCachedRuntimeOverrides(overrides: any) {
  if (!isDev()) {
    log('[ConfigService]', 'warn', 'Attempted to set runtime overrides in production mode');
    return;
  }
  
  try {
    await AsyncStorage.setItem(DEBUG_CONFIG_CACHE_KEY, JSON.stringify(overrides));
    log('[ConfigService]', 'debug', 'Cached debug runtime overrides', [overrides]);
  } catch (error) {
    log('[ConfigService]', 'error', 'Failed to cache debug runtime overrides', [error]);
  }
}

function isCacheValid(cache: any): boolean {
  if (!cache || !cache.timestamp) return false;
  const now = Date.now();
  return now - cache.timestamp < appSettings.remoteConfigTTL;
}

// Event system for config change listeners
const listeners: Array<() => void> = [];
function notifyListeners() {
  listeners.forEach(fn => fn());
}

// Helper to build _meta info
function buildMeta({ source, timestamp, lastFetchStatus, log }: { source: string, timestamp: number, lastFetchStatus: string, log: string[] }) {
  const expiry = timestamp + (appSettings.remoteConfigTTL || 0);
  return { _meta: { source, timestamp, expiry, lastFetchStatus, log: [...log] } };
}

export class ConfigService {
  private static instance: ConfigService;
  private config: any = localConfig;
  private runtimeOverrides: any = {};
  private meta = { source: 'local', timestamp: Date.now(), expiry: Date.now() + (appSettings.remoteConfigTTL || 0), lastFetchStatus: 'init', log: [] as string[] };

  private constructor() {
    this.init();
  }

  private async init() {
    this.meta.log.push('Init: Loading cached remote config...');
    
    // Load runtime overrides if in dev mode
    if (isDev()) {
      this.meta.log.push('DEV mode: Loading cached runtime overrides...');
      const cachedOverrides = await getCachedRuntimeOverrides();
      if (cachedOverrides) {
        this.runtimeOverrides = cachedOverrides;
        this.meta.log.push('Loaded cached runtime overrides.');
      }
    }
    
    const cached = await getCachedRemoteConfig();
    if (cached && isCacheValid(cached)) {
      this.meta.log.push('Valid cached config found. Using cached config.');
      this.config = this.buildFinalConfig(localConfig, cached.config);
      this.meta = { source: 'cached', timestamp: cached.timestamp, expiry: cached.timestamp + (appSettings.remoteConfigTTL || 0), lastFetchStatus: 'cached', log: [...this.meta.log] };
      log('[ConfigService]', 'info', 'Using valid cached config as initial config', [this.config]);
      notifyListeners();
      this.reload(true);
    } else {
      this.meta.log.push('No valid cache. Fetching remote config...');
      await this.reload();
    }
  }

  /**
   * Build the final config by merging in order: local -> remote -> debug -> runtime overrides
   */
  private buildFinalConfig(localConfig: any, remoteConfig?: any): any {
    let merged = localConfig;
    
    // Merge remote config if provided
    if (remoteConfig) {
      merged = deepMerge(merged, remoteConfig);
    }
    
    // Merge debug config if in dev mode
    if (isDev()) {
      merged = deepMerge(merged, debugConfig);
    }
    
    // Merge runtime overrides if in dev mode and they exist
    if (isDev() && Object.keys(this.runtimeOverrides).length > 0) {
      merged = deepMerge(merged, this.runtimeOverrides);
    }
    
    return merged;
  }

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  public get(pathStr: string, defaultValue?: any): any {
    // Always attach _meta to the root config
    const configWithMeta = { ...this.config, _meta: { ...this.meta } };
    if (!pathStr || pathStr === '') {
      return configWithMeta;
    }
    
    // Try to get the exact path first
    const exactValue = pathStr.split('.').reduce((obj, key) => (obj && obj[key] !== undefined ? obj[key] : undefined), configWithMeta);
    
    if (exactValue !== undefined) {
      return exactValue;
    }
    
    // If exact path not found, check for wildcard defaults
    const wildcardDefault = this.getWildcardDefault(pathStr, configWithMeta);
    if (wildcardDefault !== undefined) {
      return wildcardDefault;
    }
    
    // Finally return the provided default value
    return defaultValue;
  }

  /**
   * Check for wildcard defaults in app.defaults
   * For path "api.reverse-geocode.units", checks if app.defaults.api.* has "units"
   */
  public getWildcardDefault(pathStr: string, config: any): any {
    const pathParts = pathStr.split('.');
    if (pathParts.length < 2) {
      return undefined; // Need at least 2 parts for wildcard matching
    }
    
    // Check if app.defaults exists
    const appDefaults = config.app?.defaults;
    if (!appDefaults) {
      return undefined;
    }
    
    // Try different wildcard patterns
    // For "api.reverse-geocode.units", try app.defaults.api.*
    for (let i = 1; i < pathParts.length; i++) {
      const basePath = pathParts.slice(0, i);
      const remainingPath = pathParts.slice(i);
      
      // Navigate to the base path in defaults
      let defaultsSection = appDefaults;
      for (const part of basePath) {
        if (!defaultsSection || typeof defaultsSection !== 'object') {
          break;
        }
        defaultsSection = defaultsSection[part];
      }
      
      // Check if there's a wildcard section
      if (defaultsSection && typeof defaultsSection === 'object' && defaultsSection['*']) {
        const wildcardDefaults = defaultsSection['*'];
        
        // Navigate through the remaining path in the wildcard defaults
        const wildcardValue = remainingPath.reduce((obj, key) => (obj && obj[key] !== undefined ? obj[key] : undefined), wildcardDefaults);
        
        if (wildcardValue !== undefined) {
          log('[ConfigService]', 'debug', `Found wildcard default for ${pathStr}: ${JSON.stringify(wildcardValue)}`, [{ path: pathStr, basePath, remainingPath, wildcardValue }]);
          return wildcardValue;
        }
      }
    }
    
    return undefined;
  }

  /**
   * DEV-only method to set runtime config overrides
   * @param path The config path to override (e.g., 'logging.level')
   * @param value The value to set
   */
  public setDebugOverride(path: string, value: any): void {
    if (!isDev()) {
      log('[ConfigService]', 'warn', 'Attempted to set debug override in production mode');
      return;
    }

    // Parse the path and set the value in runtimeOverrides
    const pathParts = path.split('.');
    let target = this.runtimeOverrides;
    
    for (let i = 0; i < pathParts.length - 1; i++) {
      const key = pathParts[i];
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {};
      }
      target = target[key];
    }
    
    target[pathParts[pathParts.length - 1]] = value;
    
    // Rebuild config with new overrides
    const cached = this.meta.source === 'remote' || this.meta.source === 'cached';
    const remoteConfig = cached ? this.extractRemoteFromCurrent() : undefined;
    this.config = this.buildFinalConfig(localConfig, remoteConfig);
    
    // Cache the overrides
    setCachedRuntimeOverrides(this.runtimeOverrides);
    
    log('[ConfigService]', 'debug', `Set debug override: ${path} = ${JSON.stringify(value)}`, [this.runtimeOverrides]);
    notifyListeners();
  }

  /**
   * DEV-only method to clear all runtime overrides
   */
  public clearDebugOverrides(): void {
    if (!isDev()) {
      log('[ConfigService]', 'warn', 'Attempted to clear debug overrides in production mode');
      return;
    }

    this.runtimeOverrides = {};
    
    // Rebuild config without overrides
    const cached = this.meta.source === 'remote' || this.meta.source === 'cached';
    const remoteConfig = cached ? this.extractRemoteFromCurrent() : undefined;
    this.config = this.buildFinalConfig(localConfig, remoteConfig);
    
    // Clear cached overrides
    setCachedRuntimeOverrides({});
    
    log('[ConfigService]', 'debug', 'Cleared all debug overrides');
    notifyListeners();
  }

  /**
   * DEV-only convenience method to toggle cache bypass
   */
  public toggleCacheBypass(): boolean {
    if (!isDev()) {
      log('[ConfigService]', 'warn', 'Cache bypass toggle only available in development mode');
      return false;
    }

    const currentValue = this.get('debug.bypassCache', false);
    const newValue = !currentValue;
    
    this.setDebugOverride('debug.bypassCache', newValue);
    
    log('[ConfigService]', 'info', `🔄 Cache bypass ${newValue ? 'ENABLED' : 'DISABLED'}`, [{ previousValue: currentValue, newValue }]);
    
    return newValue;
  }

  /**
   * DEV-only method to get current runtime overrides
   */
  public getDebugOverrides(): any {
    if (!isDev()) {
      return {};
    }
    return { ...this.runtimeOverrides };
  }

  /**
   * Helper to extract remote config from current merged config (approximate)
   * TODO: This is a simplified extraction - in practice we'd need to track the remote config separately
   */
  private extractRemoteFromCurrent(): any {
    // For now, return empty object - this would need more sophisticated tracking
    // to properly separate remote config from the merged result
    return {};
  }

  /**
   * Reload config: fetch remote, update cache, merge, notify listeners.
   * If background is true, errors do not fallback to local/cached.
   */
  public async reload(background = false): Promise<void> {
    this.meta.log.push('Reload: Attempting to fetch remote config...');
    try {
      let remoteConfig;
      try {
        remoteConfig = await fetchRemoteConfig();
        this.meta.log.push('Remote config fetched successfully.');
      } catch (e) {
        this.meta.log.push('Remote fetch failed. Attempting to use cached config.');
        const cached = await getCachedRemoteConfig();
        if (cached && cached.config) {
          this.config = this.buildFinalConfig(localConfig, cached.config);
          this.meta = { source: 'cached', timestamp: cached.timestamp, expiry: cached.timestamp + (appSettings.remoteConfigTTL || 0), lastFetchStatus: 'fallback-cached', log: [...this.meta.log] };
          log('[ConfigService]', 'warn', 'Remote fetch failed, using cached config', [this.config]);
        } else {
          this.config = this.buildFinalConfig(localConfig);
          this.meta = { source: 'local', timestamp: Date.now(), expiry: Date.now() + (appSettings.remoteConfigTTL || 0), lastFetchStatus: 'fallback-local', log: [...this.meta.log] };
          log('[ConfigService]', 'error', 'Remote fetch failed, no cached config, using local config', [this.config]);
        }
        notifyListeners();
        if (!background) throw e;
        return;
      }
      // On remote fetch success, update config and cache
      this.config = this.buildFinalConfig(localConfig, remoteConfig);

      log('[ConfigService]', 'debug', `👉 remoteConfig: ${JSON.stringify(remoteConfig, null, 2)}`);

      const now = Date.now();
      this.meta = { source: 'remote', timestamp: now, expiry: now + (appSettings.remoteConfigTTL || 0), lastFetchStatus: 'success', log: [...this.meta.log] };
      log('[ConfigService]', 'debug', 'Config updated (remote merged)', [this.config]);
      notifyListeners();
    } catch (e) {
      this.meta.log.push('Config reload failed: ' + ((e as any)?.message || e));
      log('[ConfigService]', 'error', 'Config reload failed', [e]);
    }
  }

  public subscribe(fn: () => void) {
    listeners.push(fn);
    return () => {
      const idx = listeners.indexOf(fn);
      if (idx !== -1) listeners.splice(idx, 1);
    };
  }
}

const configInstance = ConfigService.getInstance();

/**
 * Helper function to get config values as mentioned in requirements
 * Usage: getConfigValue(config, 'api.reverse-geocode.units', 'imperial')
 */
export function getConfigValue(config: any, path: string, defaultValue?: any): any {
  // If a specific config object is passed, use it with the same logic as get()
  if (config && typeof config === 'object' && config._meta) {
    // Try exact path first
    const exactValue = path.split('.').reduce((obj, key) => (obj && obj[key] !== undefined ? obj[key] : undefined), config);
    if (exactValue !== undefined) {
      return exactValue;
    }
    
    // Check for wildcard defaults using the same logic
    const wildcardDefault = configInstance.getWildcardDefault(path, config);
    if (wildcardDefault !== undefined) {
      return wildcardDefault;
    }
    
    return defaultValue;
  }
  
  // Otherwise use the singleton instance
  return configInstance.get(path, defaultValue);
}

export default configInstance;