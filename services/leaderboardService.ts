import { fetchSkiResorts } from '../hooks/useSkiResortsQuery';
import { ApiErrorHandler } from '../utils/ApiErrorHandler';
import { BaseApiService } from '../utils/BaseApiService';
import { CacheHelpers } from '../utils/CacheManager';
import { ValidationUtils } from '../utils/ValidationUtils';
import ConfigService from './appConfigService';
import { log } from './loggerService';

export interface LeaderboardApiResponse {
  data: {
    recentSnowLeaderboard: Array<{
      resortId: string;
      recentSnow: number;
      rank?: number;
    }>;
    forecastSnowLeaderboard: Array<{
      resortId: string;
      fiveDaySnowForecast: number;
      rank?: number;
    }>;
    snowOnGroundLeaderboard: Array<{
      resortId: string;
      snowOnGround: number;
      rank?: number;
    }>;
  };
}

export interface LeaderboardData {
  recentSnowLeaderboard: any[];
  forecastSnowLeaderboard: any[];
  snowOnGroundLeaderboard: any[];
}

/**
 * Service for fetching leaderboard data
 * Handles API communication, caching, and data transformation
 */
export class LeaderboardService extends BaseApiService<any> {
  
  constructor() {
    super('LeaderboardService');
  }

  /**
   * Fetch leaderboard data with caching and error handling
   * @param options Optional settings (bypassCache)
   */
  async fetchLeaderboardData(options: { bypassCache?: boolean } = {}): Promise<LeaderboardData> {
    try {
      // Check cache first
      const cached = await this.getCachedData('fetchLeaderboardData', {}, options.bypassCache);
      if (cached) {
        log('[LeaderboardService]', 'debug', 'Using cached leaderboard data');
        return cached;
      }

      log('[LeaderboardService]', 'info', '📡 Fetching leaderboard data from API');
      
      // Get URL from config and fetch from API
      const url = ConfigService.get('api.leaderboard.url');
      if (!url) {
        throw new Error('LeaderboardService: No URL configured at api.leaderboard.url');
      }

      const timeout = ConfigService.get('api.leaderboard.timeout', 5000);
      const retries = ConfigService.get('api.leaderboard.retries', 2);
      
      // Fetch from API with URL parameter replacement
      const response = await this.fetchData(url, {}, {}, timeout, retries);
      const apiResponse: LeaderboardApiResponse = await response.json();
      
      // Validate response structure
      if (!apiResponse.data) {
        throw new Error('Invalid API response: missing data property');
      }

      // Transform API response to expected format
      const leaderboardData: LeaderboardData = {
        recentSnowLeaderboard: apiResponse.data.recentSnowLeaderboard || [],
        forecastSnowLeaderboard: apiResponse.data.forecastSnowLeaderboard || [],
        snowOnGroundLeaderboard: apiResponse.data.snowOnGroundLeaderboard || []
      };

      // Cache the result with configurable TTL
      const ttl = ConfigService.get('api.leaderboard.ttl', 300000); // 5 minutes default
      await this.setCachedData('fetchLeaderboardData', {}, leaderboardData, ttl);
      
      log('[LeaderboardService]', 'debug', 'Leaderboard data fetched and cached successfully');
      return leaderboardData;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(
        error, 
        this.serviceName, 
        'Failed to fetch leaderboard data'
      );
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchLeaderboardData');
      throw apiError;
    }
  }

  /**
   * Fetch resort data with cross-service caching
   * Attempts to use cached resort data from other services first
   * @param options Optional settings (bypassCache)
   */
  async fetchResortData(options: { bypassCache?: boolean } = {}): Promise<any[]> {
    try {
      // Try to get resort data from cache (might be cached by ResortService)
      let resorts = await CacheHelpers.getCachedServiceData<any[]>(
        'ResortService', 
        'fetchSkiResorts', 
        {}
      );

      if (resorts) {
        log('[LeaderboardService]', 'debug', 'Using cached resort data from ResortService');
        return resorts;
      }

      // Fallback to direct fetch (legacy approach)
      log('[LeaderboardService]', 'info', '📡 Fetching resort data');
      resorts = await fetchSkiResorts();
      
      // Ensure IDs are strings for consistency
      resorts = resorts.map((resort: any) => ({
        ...resort,
        id: String(resort.id)
      }));

      // Cache for other services to use
      await CacheHelpers.cacheServiceData(
        'ResortService',
        'fetchSkiResorts',
        {},
        resorts,
        { ttl: 60 * 60 * 1000 } // 1 hour - resorts don't change often
      );

      log('[LeaderboardService]', 'debug', 'Resort data fetched and cached successfully');
      return resorts;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(
        error, 
        this.serviceName, 
        'Failed to fetch resort data'
      );
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchResortData');
      throw apiError;
    }
  }

  /**
   * Fetch combined leaderboard and resort data
   * This is the main method that components should use
   * @param limit Number of entries to fetch
   * @param options Optional settings (bypassCache)
   */
  async fetchCombinedData(limit: number = 10, options: { bypassCache?: boolean } = {}): Promise<{
    leaderboard: LeaderboardData;
    resorts: any[];
  }> {
    try {
      // Validate limit parameter
      const validation = ValidationUtils.validateNumber(limit, 'limit', 1, 100);
      if (!validation.isValid) {
        throw new Error(`Invalid limit parameter: ${validation.errors.join(', ')}`);
      }

      // Check if we have cached combined data
      const cached = await this.getCachedData('fetchCombinedData', { limit }, options.bypassCache);
      if (cached) {
        log('[LeaderboardService]', 'debug', 'Using cached combined data');
        return cached;
      }

      // Fetch both datasets in parallel for better performance
      const [leaderboard, resorts] = await Promise.all([
        this.fetchLeaderboardData(options),
        this.fetchResortData(options)
      ]);

      const combinedData = { leaderboard, resorts };

      // Cache combined data with shorter TTL since it's processed
      await this.setCachedData('fetchCombinedData', { limit }, combinedData, 5 * 60 * 1000); // 5 minutes

      return combinedData;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(
        error, 
        this.serviceName, 
        'Failed to fetch combined leaderboard and resort data'
      );
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchCombinedData');
      throw apiError;
    }
  }
}

// Export singleton instance
export const leaderboardService = new LeaderboardService();

// Legacy function exports for backward compatibility
// TODO: Remove these once all consumers are updated
export async function fetchLeaderboardFromApi(): Promise<any> {
  const data = await leaderboardService.fetchLeaderboardData();
  return data;
}

export async function fetchAllResorts(): Promise<any[]> {
  return leaderboardService.fetchResortData();
}

// TODO: Add types for the API response if available 
// TODO: Integrate config for logging when available 