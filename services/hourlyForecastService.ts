import ENV from '../constants/Env';
import { ApiErrorHandler } from '../utils/ApiErrorHandler';
import { BaseApiService } from '../utils/BaseApiService';
import { PrecisionTimer } from '../utils/PrecisionTimer';
import { ValidationUtils } from '../utils/ValidationUtils';
import ConfigService from './appConfigService';
import { log } from './loggerService';

export interface HourlyForecastData {
  time: string;
  icon: string;
  temp: string;
  precipitation?: string;
  dayOrNight?: string; // 'D' for day, 'N' for night
}

/**
 * Service for fetching hourly weather forecast data
 * Follows the external data loading pattern with BaseApiService
 */
export class HourlyForecastService extends BaseApiService<HourlyForecastData[]> {
  private static instance: HourlyForecastService;
  private listeners: Set<(data: HourlyForecastData[]) => void> = new Set();

  private constructor() {
    super('HourlyForecastService');
  }

  public static getInstance(): HourlyForecastService {
    if (!HourlyForecastService.instance) {
      HourlyForecastService.instance = new HourlyForecastService();
    }
    return HourlyForecastService.instance;
  }

  /**
   * Fetches hourly weather forecast for a given location
   * @param location The location string in "lat,lon" format
   * @param options Optional settings (bypassCache)
   * @returns Promise<HourlyForecastData[]>
   */
  public async fetchHourlyForecast(location: string, options: { bypassCache?: boolean } = {}): Promise<HourlyForecastData[]> {
    // Validate location format
    const validation = ValidationUtils.validateCoordinates(location);
    if (!validation.isValid) {
      const error = new Error(`Invalid location format: ${validation.errors.join(',')}`);
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'fetchHourlyForecast');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchHourlyForecast');
      throw apiError;
    }

    // Check cache first (unless bypassed)
    const cacheKey = 'fetchHourlyForecast';
    const cached = await this.getCachedData(cacheKey, { location }, options.bypassCache);
    if (cached) {
      log(`[${this.serviceName}]`, 'info', `Returning cached data for location: ${location}`);
      return cached;
    }

    // Get API configuration
    const apiKey = ENV.sunApiToken;
    if (!apiKey) {
      const error = new Error('Missing SUN_API_TOKEN in environment variables');
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'fetchHourlyForecast');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchHourlyForecast');
      throw apiError;
    }

    // Get service configuration
    const baseUrl = ConfigService.get('api.hourly-forecast.url');
    const timeout = ConfigService.get('api.hourly-forecast.timeout');
    const retries = ConfigService.get('api.hourly-forecast.retries');
    const url = ConfigService.get('api.hourly-forecast.url');
   

    try {
      log(`[${this.serviceName}]`, 'info', `Fetching hourly forecast for location: ${location}`);
      
      PrecisionTimer.start('fetchHourlyForecast');

      const response = await this.fetchData(url,
        { location, apiKey },
        {},
        timeout,
        retries
      );

      PrecisionTimer.stop('fetchHourlyForecast');

      const data = await response.json();

      // Validate response structure
      if (!Array.isArray(data.validTimeLocal) || !Array.isArray(data.iconCode) || !Array.isArray(data.temperature)) {
        throw new Error('Malformed hourly forecast data: missing required arrays');
      }

      // Transform API response to HourlyForecastData[]
      const result: HourlyForecastData[] = data.validTimeLocal.map((time: string, idx: number) => ({
        time: time, // ISO string, can be formatted in the component
        icon: data.iconCode[idx]?.toString() ?? '', // icon code as string
        temp: data.temperature[idx] !== undefined ? `${data.temperature[idx]}°` : '',
        precipitation: data.qpf[idx] !== undefined ? `${data.qpf[idx]} in` : '',
        dayOrNight: data.dayOrNight?.[idx] ?? undefined, // 'D' or 'N' if available
      }));

      // Cache the result
      const cacheTtl = ConfigService.get('app.defaults.api.hourlyForecast.cacheTtl', 300000); // 5 minutes default
      await this.setCachedData(cacheKey, { location }, result, cacheTtl);

      // Notify listeners
      this.notifyListeners(result);

      log(`[${this.serviceName}]`, 'info', `Successfully fetched ${result.length} hourly forecast entries`);
      return result;

    } catch (error) {
      const apiError = ApiErrorHandler.createApiError(error, this.serviceName, 'fetchHourlyForecast');
      ApiErrorHandler.logError(apiError, this.serviceName, 'fetchHourlyForecast');
      throw apiError;
    }
  }

  /**
   * Add a listener for hourly forecast data updates
   * @param callback Function to call when data is updated
   * @returns Unsubscribe function
   */
  public addListener(callback: (data: HourlyForecastData[]) => void): () => void {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  /**
   * Remove a listener
   * @param callback The callback function to remove
   */
  public removeListener(callback: (data: HourlyForecastData[]) => void): void {
    this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of data updates
   * @param data The updated hourly forecast data
   */
  private notifyListeners(data: HourlyForecastData[]): void {
    this.listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        log(`[${this.serviceName}]`, 'error', 'Error in listener callback:', [error]);
      }
    });
  }

  /**
   * Refresh hourly forecast data for a location
   * This bypasses cache and fetches fresh data
   * @param location The location string in "lat,lon" format
   */
  public async refreshHourlyForecast(location: string): Promise<HourlyForecastData[]> {
    // Clear cache for this location
    await this.clearCache();
    
    // Fetch fresh data
    return this.fetchHourlyForecast(location, { bypassCache: true });
  }
}

// Export singleton instance
export const hourlyForecastService = HourlyForecastService.getInstance(); 