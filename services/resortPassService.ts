// Placeholder for services/resortPassService.ts
import { ResortPass } from '../types'; // Assuming types.ts will exist

// Load resort pass data from JSON
const passData = require('../assets/mock-data/resort-passes.json');
const mockResortPasses: { [key: string]: ResortPass } = Array.isArray(passData.resortPasses)
  ? passData.resortPasses.reduce((acc: { [key: string]: ResortPass }, pass: ResortPass) => {
      acc[pass.id] = pass;
      return acc;
    }, {})
  : {};

export const getResortPassById = (passId: string): ResortPass | null => {
  console.log(`Fetching resort pass data for ID: ${passId}`);
  return mockResortPasses[passId] || null;
};

// Optional: Function to get all passes if needed later
export const getAllResortPasses = (): ResortPass[] => {
  return Object.values(mockResortPasses);
};
