{"name": "twc-snow", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npm run prebuild && expo start", "reset-project": "node ./scripts/reset-project.js", "android": "npm run prebuild && expo run:android", "ios": "npm run prebuild && expo run:ios", "web": "expo start --web", "lint": "expo lint", "prebuild": "node ./scripts/build-pangea-bundle.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@faker-js/faker": "^9.8.0", "@miblanchard/react-native-slider": "^2.6.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.81.5", "color2k": "^2.0.3", "dotenv": "^16.5.0", "expo": "~53.0.10", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.0", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "i18next": "^25.3.0", "lodash": "^4.17.21", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.0", "react-native": "0.79.3", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.61", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-tab-view": "^4.1.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "victory-native": "^36.6.11"}, "devDependencies": {"@babel/core": "^7.25.2", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/lodash": "^4.17.19", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}