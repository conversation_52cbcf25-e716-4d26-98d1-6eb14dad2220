import { useTheme } from '@/app/theme/ThemeContext'; // Import our new useTheme
import { View, type ViewProps } from 'react-native';

export type ThemedViewProps = ViewProps & {
  // lightColor and darkColor props removed
};

export function ThemedView({ style, ...otherProps }: ThemedViewProps) {
  const { colors } = useTheme(); // Use our theme

  // Default background color from theme. Can be overridden by style prop.
  return <View style={[{ backgroundColor: colors.background }, style]} {...otherProps} />;
}
