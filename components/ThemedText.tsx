import { StyleSheet, Text, type TextProps } from 'react-native';

import { SemanticColors, useTheme } from '@/app/theme/ThemeContext'; // Import our new useTheme

export type ThemedTextProps = TextProps & {
  // lightColor and darkColor props removed
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
};

export function ThemedText({
  style,
  // lightColor and darkColor props removed
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const { colors } = useTheme(); // Use our theme
  const themedStyles = styles(colors); // Pass colors to styles function
  // const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text'); // Removed

  return (
    <Text
      style={[
        { color: colors.text }, // Default color from theme
        type === 'default' ? themedStyles.default : undefined,
        type === 'title' ? themedStyles.title : undefined,
        type === 'defaultSemiBold' ? themedStyles.defaultSemiBold : undefined,
        type === 'subtitle' ? themedStyles.subtitle : undefined,
        type === 'link' ? themedStyles.link : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = (colors: SemanticColors) => StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 24,
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '600',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  link: {
    lineHeight: 30,
    fontSize: 16,
    color: colors.primary, // Use themed color
  },
});
