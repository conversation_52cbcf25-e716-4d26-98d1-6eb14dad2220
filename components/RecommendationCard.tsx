import { useTheme } from '@/app/theme/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { useLocation } from '../contexts/LocationContext';
import { useAiRecommendationQuery } from '../hooks/useRecommendationQuery';
import { useResortsQuery } from '../hooks/useResortsQuery';
import { useSkiConditionsQuery } from '../hooks/useSkiConditionsQuery';
import useThreeDayForecast from '../hooks/useThreeDayForecast';
import { useWeatherQuery } from '../hooks/useWeatherQuery';
import { ThemedText } from './ThemedText';

interface RecommendationCardProps {
  location: string;
}

// Add a list of light-hearted loading phrases
const loadingPhrases = [
  "Waxing the skis...",
  "Checking the snow report...",
  "Scouting the best runs...",
  "Tuning up the snowcat...",
  "Consulting the mountain goats...",
  "Measuring the powder...",
  "Sharpening the edges...",
  "Finding the freshest tracks...",
  "Brewing some hot cocoa...",
  "Counting snowflakes...",
  "Untangling the lift lines...",
  "Polishing the goggles...",
  "Listening for avalanche beacons...",
  "Warming up the hot tub...",
  "Testing the chairlifts...",
  "Packing extra mittens...",
  "Checking avalanche reports...",
  "Mapping out the moguls...",
  "Spotting snow bunnies...",
  "Prepping the snow cannons...",
  "Doing a snow dance...",
  "Sipping on glühwein...",
  "Hunting for the best après-ski...",
  "Counting icicles...",
  "Tracking snowplows...",
  "Scouting for powder stashes...",
  "Checking helmet straps...",
  "Tuning the playlist for the slopes...",
  "Practicing perfect parallel turns...",
  "Dreaming of bluebird days...",
  "Plotting the perfect run..."
];

export const RecommendationCard: React.FC<RecommendationCardProps> = ({ location }) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);

  // Get current location from context
  const {
    location: userLocation,
    isLoading: isLocLoading,
    error: locError,
    locationName: userLocationName,
  } = useLocation();

  // Get weather for current location
  const userGeocode = userLocation ? `${userLocation.latitude},${userLocation.longitude}` : '';
  const { data: userWeather, isLoading: isUserWeatherLoading, error: userWeatherError } = useWeatherQuery(userGeocode);

  // Get 2 nearby resorts
  const { data: resorts, isLoading: isResortsLoading, error: resortsError, refetch } = useResortsQuery(userGeocode, 2);
  const resort1 = resorts && resorts.length > 0 ? resorts[0] : null;
  const resort2 = resorts && resorts.length > 1 ? resorts[1] : null;
  
  // Check if we have no resorts found - be more lenient about error state
  // The service might return empty array but React Query might still have cached error
  const hasNoResortsFound = !isResortsLoading && resorts && resorts.length === 0;
  


  // For each resort, get forecast and ski conditions (only if resorts exist)
  const resort1Geocode = resort1 ? `${resort1.latitude},${resort1.longitude}` : '';
  const resort2Geocode = resort2 ? `${resort2.latitude},${resort2.longitude}` : '';

  // Only fetch forecast/ski data if we have resorts and haven't determined there are none nearby
  const shouldFetchResortData = !hasNoResortsFound && (resort1 || resort2);

  const { data: resort1Forecast, isLoading: isResort1ForecastLoading, error: resort1ForecastError } = useThreeDayForecast(
    shouldFetchResortData && resort1 ? resort1Geocode : ''
  );
  const { data: resort2Forecast, isLoading: isResort2ForecastLoading, error: resort2ForecastError } = useThreeDayForecast(
    shouldFetchResortData && resort2 ? resort2Geocode : ''
  );

  const { data: resort1Ski, isLoading: isResort1SkiLoading, error: resort1SkiError } = useSkiConditionsQuery(
    shouldFetchResortData && resort1 ? resort1.id : undefined
  );
  const { data: resort2Ski, isLoading: isResort2SkiLoading, error: resort2SkiError } = useSkiConditionsQuery(
    shouldFetchResortData && resort2 ? resort2.id : undefined
  );

  // State for random loading phrase
  const [loadingPhrase, setLoadingPhrase] = React.useState(loadingPhrases[0]);

  // Pick a new random loading phrase each time loading starts
  React.useEffect(() => {
    if (
      isLocLoading || isUserWeatherLoading || isResortsLoading ||
      isResort1ForecastLoading || isResort2ForecastLoading ||
      isResort1SkiLoading || isResort2SkiLoading
    ) {
      const pickRandom = () => {
        const idx = Math.floor(Math.random() * loadingPhrases.length);
        setLoadingPhrase(loadingPhrases[idx]);
      };
      pickRandom();
      const interval = setInterval(pickRandom, 2000);
      return () => clearInterval(interval);
    }
  }, [isLocLoading, isUserWeatherLoading, isResortsLoading, isResort1ForecastLoading, isResort2ForecastLoading, isResort1SkiLoading, isResort2SkiLoading]);

  // Compose AIRecommendationRequest when all data is ready
  const aiRequest = React.useMemo(() => {
    // Always create AI request if we have user location and weather
    if (!userLocation || !userWeather) {
      return undefined;
    }

    // Build resorts array with available data (could be empty)
    const availableResorts = [];
    
    if (resort1 && resort1Forecast && resort1Ski) {
      availableResorts.push({
        name: resort1.name,
        forecast: resort1Forecast.forecasts, // TODO: Ensure this matches expected type
        ski: resort1Ski,
      });
    }
    
    if (resort2 && resort2Forecast && resort2Ski) {
      availableResorts.push({
        name: resort2.name,
        forecast: resort2Forecast.forecasts, // TODO: Ensure this matches expected type
        ski: resort2Ski,
      });
    }

    // Create AI request with user data and whatever resort data we have (even if empty)
    return {
      location: userLocationName || userGeocode,
      timeOfDay: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
      userWeather,
      resorts: availableResorts, // Could be empty array - AI will handle this case
    };
  }, [userLocation, userWeather, resort1, resort2, resort1Forecast, resort2Forecast, resort1Ski, resort2Ski, userLocationName, userGeocode]);

  // Use the new React Query hook
  const {
    data: aiData,
    isLoading: isAiLoading,
    error: aiError,
  } = useAiRecommendationQuery(aiRequest as any, 'mistralai/mistral-7b-instruct:free'); // TODO: Remove 'as any' if types align

  // Don't show early "no resorts found" message - let AI handle it
  // The AI can provide recommendations even with no nearby resorts

  // Show loading card with rotating phrase
  if (
    isLocLoading || isUserWeatherLoading || isResortsLoading ||
    (shouldFetchResortData && (isResort1ForecastLoading || isResort2ForecastLoading)) ||
    (shouldFetchResortData && (isResort1SkiLoading || isResort2SkiLoading)) ||
    isAiLoading
  ) {
    return (
      <LinearGradient
        colors={[colors.recommendationGradient1, colors.recommendationGradient2, colors.recommendationGradient3]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={themedStyles.gradientBorder}
      >
        <View style={themedStyles.cardContent}>
          <ActivityIndicator size="small" color={colors.textSecondary} style={themedStyles.aiSpinner} />
          <ThemedText style={themedStyles.aiText}>{loadingPhrase}</ThemedText>
        </View>
      </LinearGradient>
    );
  }
  // Show error if any required data fails or AI fails (but not if we just have no resorts)
  if (
    locError || userWeatherError || resortsError ||
    (shouldFetchResortData && (resort1ForecastError || resort2ForecastError)) ||
    (shouldFetchResortData && (resort1SkiError || resort2SkiError)) ||
    aiError
  ) {
    // If we have no resorts and AI failed, show a specific message
    if (hasNoResortsFound && aiError) {
      return (
        <LinearGradient
          colors={[colors.recommendationGradient1, colors.recommendationGradient2, colors.recommendationGradient3]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={themedStyles.gradientBorder}
        >
          <View style={themedStyles.cardContent}>
            <ThemedText style={themedStyles.title}>No Ski Resorts Nearby</ThemedText>
            <ThemedText style={themedStyles.aiText}>
              We couldn't find any ski resorts in your area. Consider traveling to mountain regions for skiing! 🏔️
            </ThemedText>
          </View>
        </LinearGradient>
      );
    }
    
    return <ThemedText style={themedStyles.error}>Unable to load AI recommendation</ThemedText>;
  }

  // Show the AI recommendation if available
  if (aiData && aiData.recommendation) {
    return (
      <LinearGradient
        colors={[colors.recommendationGradient1, colors.recommendationGradient2, colors.recommendationGradient3]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={themedStyles.gradientBorder}
      >
        <View style={themedStyles.cardContent}>
          <ThemedText style={themedStyles.title}>Recommendation</ThemedText>
          <ThemedText style={themedStyles.aiText}>{aiData.recommendation}</ThemedText>
        </View>
      </LinearGradient>
    );
  }

  // Default: null (should not happen)
  return null;
};

const styles = (colors: any) => StyleSheet.create({
  gradientBorder: {
    borderRadius: 10,
    padding: 2,
    marginVertical: 12,
    shadowColor: '#FFFFFF',
    shadowOpacity: 0.4,
    shadowRadius: 15,
    shadowOffset: { width: 0, height: 0 },
    elevation: 8
  },
  cardContent: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 18,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
    color: colors.text,
  },
  summary: {
    fontSize: 15,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  centered: {
    marginVertical: 16,
  },
  error: {
    color: colors.errorText,
    textAlign: 'center',
    marginVertical: 16,
  },
  aiRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    minHeight: 24,
  },
  aiSpinner: {
    marginRight: 8,
  },
  aiText: {
    fontSize: 14,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  aiError: {
    color: colors.errorText,
    fontSize: 14,
    fontStyle: 'italic',
  },
});
