import { useTheme } from '@/app/theme/ThemeContext';
import React, { useEffect, useState } from 'react';
import { Dimensions, ImageSourcePropType, StyleSheet, TextStyle, View } from 'react-native';
import Animated, {
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedReaction,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

// Types for props
interface AnimatedHeaderListProps {
  headerImage: ImageSourcePropType;
  headerTitle: string;
  headerIcons?: (isCollapsed: boolean) => React.ReactNode;
  renderTabBar: () => React.ReactNode;
  renderContent: () => React.ReactNode;
  headerHeight?: number;
  headerFontSize?: number;
  /**
   * If true, hides the header image when the header is fully collapsed and shows a background color instead.
   */
  hideImageWhenCollapsed?: boolean;
  /**
   * Background color to use when the image is hidden (collapsed state). Defaults to #222.
   */
  collapsedHeaderBackgroundColor?: string;
  /**
   * Style for the header text when expanded (object, e.g. { color: 'white' })
   */
  expandedHeaderTextStyle?: TextStyle;
  /**
   * Style for the header text when collapsed (object, e.g. { color: '#222' })
   */
  collapsedHeaderTextStyle?: TextStyle;
  /**
   * Style for the header icons when expanded (object, e.g. { color: 'white' })
   */
  expandedHeaderIconsStyle?: object;
  /**
   * Style for the header icons when collapsed (object, e.g. { color: '#222' })
   */
  collapsedHeaderIconsStyle?: object;
}

// Screen dimensions and layout constants
const { width, height } = Dimensions.get('window');
const _spacing = 10;
const _defaultHeaderHeight = height / 2;
const _defaultHeaderFontSize = 64;
const _headerSpacing = _defaultHeaderHeight * 0.3;

/**
 * AnimatedHeaderList
 * Renders an animated header, a sticky tab bar, and scrollable content using FlatList.
 * The tab bar is sticky and all scroll/animation behaviors are preserved.
 */
const AnimatedHeaderList: React.FC<AnimatedHeaderListProps> = ({
  headerImage,
  headerTitle,
  headerIcons,
  renderTabBar,
  renderContent,
  headerHeight = _defaultHeaderHeight,
  headerFontSize = _defaultHeaderFontSize,
  hideImageWhenCollapsed = false,
  collapsedHeaderBackgroundColor,
  expandedHeaderTextStyle,
  collapsedHeaderTextStyle,
  expandedHeaderIconsStyle = {},
  collapsedHeaderIconsStyle = {},
}) => {
  const { colors } = useTheme();
  
  // Set default values that depend on theme colors
  const finalCollapsedHeaderBackgroundColor = collapsedHeaderBackgroundColor || colors.background;
  const finalExpandedHeaderTextStyle = expandedHeaderTextStyle || { color: colors.textOnDark };
  const finalCollapsedHeaderTextStyle = collapsedHeaderTextStyle || { color: colors.text };
  // Shared values for scroll and header
  const scrollY = useSharedValue(0);
  const headerHeightSV = useSharedValue(headerHeight);
  const insets = useSafeAreaInsets();
  const [contentHeight, setContentHeight] = useState(0);
  const minHeaderHeight = 24 + insets.top+12; // Collapsed header font size (matches interpolate)
  const [isCollapsed, setIsCollapsed] = useState(false);

  //this is window height minus the size of the header when it is fully collapsed minus the header spacing
  //so it is the starting height of the content area (which is the full height of the screen minus the header height)
  //then subtract the smallest header height we want
  //then subtract the header spacing (which is the starting height of the header before any scrolling)
  const minScrollableContentHeight = (height - minHeaderHeight - _headerSpacing);

  // Handle scroll events for header animation
  const onScroll = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });

  const fadeDistance = headerHeightSV.value + minHeaderHeight;
  const fadeStartHeaderHeight = minHeaderHeight + fadeDistance * 0.1;
  const fadeEndHeaderHeight = 200;
  const fadeStartScrollY = 126;
  const fadeEndScrollY = fadeEndHeaderHeight;

  // Animated style for header text size and color
  const textStylez = useAnimatedStyle(() => {
    const fontSize = interpolate(
      scrollY.value,
      [0, _headerSpacing, headerHeightSV.value],
      [headerFontSize, 24, 24],
      Extrapolation.CLAMP
    );
    let color: string = (typeof finalExpandedHeaderTextStyle.color === 'string') ? finalExpandedHeaderTextStyle.color : 'white';
    if (hideImageWhenCollapsed) {
     
      if (
        typeof finalExpandedHeaderTextStyle.color === 'string' &&
        typeof finalCollapsedHeaderTextStyle.color === 'string'
      ) {
        const t = interpolate(
          scrollY.value,
          [fadeStartScrollY, fadeEndScrollY],
          [0, 1],
          Extrapolation.CLAMP
        );
        function hexToRgb(hex: string): [number, number, number] {
          let c = hex.replace('#', '');
          if (c.length === 3) c = c[0]+c[0]+c[1]+c[1]+c[2]+c[2];
          const num = parseInt(c, 16);
          return [num >> 16 & 255, (num >> 8) & 255, num & 255];
        }
        function parseColor(str: string): [number, number, number] {
          if (str.startsWith('#')) return hexToRgb(str);
          const m = str.match(/rgb[a]?\((\d+), ?(\d+), ?(\d+)/);
          if (m && m.length >= 4) return [parseInt(m[1]), parseInt(m[2]), parseInt(m[3])];
          return [255,255,255];
        }
        const rgb1 = parseColor(finalExpandedHeaderTextStyle.color);
        const rgb2 = parseColor(finalCollapsedHeaderTextStyle.color);
        const r = Math.round(rgb1[0] + (rgb2[0] - rgb1[0]) * t);
        const g = Math.round(rgb1[1] + (rgb2[1] - rgb1[1]) * t);
        const b = Math.round(rgb1[2] + (rgb2[2] - rgb1[2]) * t);
        color = `rgb(${r},${g},${b})`;
      }
    }
    return {
      fontSize,
      color,
      fontWeight: '700',
      letterSpacing: -1,
      paddingRight: width / 4 - _spacing * 2,
    };
  });

  // Animated style for header container margin
  const headerContainerStyle = useAnimatedStyle(() => {
    return {
      marginBottom: interpolate(
        scrollY.value,
        [-1, 0, _headerSpacing + headerFontSize, headerHeightSV.value + headerFontSize],
        [_headerSpacing + 1, _headerSpacing, 0, 0]
      ),
    };
  });

  // Animated style for dummy header (spacer)
  const dummyHeaderStylez = useAnimatedStyle(() => {
    return {
        height: Math.max(headerHeightSV.value - _headerSpacing - headerFontSize/2, 0),
    };
  });

  // Animated style for header image height and bounce effect (only when overscrolling at top)
  const headerImageStyle = useAnimatedStyle(() => {
    const transform: any[] = [];
    if (scrollY.value < 0) {
      const translateY = interpolate(
        scrollY.value,
        [-headerHeight, 0],
        [-headerHeight / 2, 0],
        Extrapolation.CLAMP
      );
      const scale = interpolate(
        scrollY.value,
        [-headerHeight, 0],
        [2, 1],
        Extrapolation.CLAMP
      );
      transform.push(
        { translateY },
        { scale },
      );
    }
    let opacity = 1;
    if (hideImageWhenCollapsed) {
     
      opacity = interpolate(
        scrollY.value,
        [0, fadeStartScrollY, fadeEndScrollY],
        [1, 1, 0],
        Extrapolation.CLAMP
      );
    }
    return {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: headerHeightSV.value,
      width: '100%',
      zIndex: 0,
      transform,
      opacity,
    };
  });

  // Animated style for background color when image is hidden
  const headerBgStyle = useAnimatedStyle(() => {
    let opacity = 0;
    if (hideImageWhenCollapsed) {
      
      opacity = interpolate(
        scrollY.value,
        [0, fadeStartScrollY, fadeEndScrollY],
        [0, 0, 1],
        Extrapolation.CLAMP
      );
    }
    return {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: headerHeightSV.value,
      width: '100%',
      zIndex: 0,
      backgroundColor: finalCollapsedHeaderBackgroundColor,
      opacity,
    };
  });

  
  useAnimatedReaction(
    () => scrollY.value >= fadeEndScrollY,
    (collapsed, prev) => {
      if (collapsed !== prev) {
        console.log('isCollapsed changed:', collapsed);
        runOnJS(setIsCollapsed)(collapsed);
      }
    },
    [fadeEndScrollY]
  );

  useEffect(() => {
    console.log('useEffect => isCollapsed', isCollapsed);
  }, [isCollapsed]);

  // FlatList data: dummy header, tab bar, content
  const flatListData = [
    { key: 'dummy-header', type: 'dummy' },
    { key: 'tab-bar', type: 'tab' },
    { key: 'content', type: 'content' },
  ];

  // Dynamic footer spacer if content is too short
  const footerSpacerHeight = Math.max(0, minScrollableContentHeight - contentHeight);

  return (
    <View style={styles.container}>
      {/* Animated Header */}
      <Animated.View
        style={[
          {
            zIndex: 1,
            position: 'absolute',
            left: 0,
            right: 0,
            overflow: 'hidden',
          },
          styles.statusBar,
        ]}
        onLayout={(ev) => {
          if (
            headerHeightSV.value === ev.nativeEvent.layout.height ||
            headerHeightSV.value !== height
          ) {
            return;
          }
          headerHeightSV.value = withTiming(ev.nativeEvent.layout.height, {
            duration: 0,
          });
        }}
      >
        {/* Animated background image fills header */}
        <Animated.Image
          source={headerImage}
          style={headerImageStyle}
          resizeMode="cover"
        />
        {/* Animated background color for collapsed state */}
        {hideImageWhenCollapsed && (
          <Animated.View style={headerBgStyle} />
        )}
        {/* Foreground header content with SafeAreaView for top inset */}
        <SafeAreaView style={{ zIndex: 1, padding: _spacing }} edges={['top', 'left', 'right']}>
          <Animated.Text
            style={[
              textStylez,
              // Allow user to override other styles if needed
              scrollY.value < (minHeaderHeight)
                ? finalExpandedHeaderTextStyle
                : finalCollapsedHeaderTextStyle,
            ]}
            numberOfLines={1}
            adjustsFontSizeToFit
          >
            {headerTitle}
          </Animated.Text>
        </SafeAreaView>
        <Animated.View style={headerContainerStyle} />
        {/* Header icons (optional) */}
        {headerIcons && headerIcons(isCollapsed)}
      </Animated.View>
      {/* Animated FlatList with dummy header, sticky tab bar, and content */}
      <Animated.FlatList
        data={flatListData}
        keyExtractor={(item) => item.key}
        scrollEventThrottle={16}
        onScroll={onScroll}
        stickyHeaderIndices={[1]} // Make tab bar sticky
        ListFooterComponent={
          <>
            <Animated.View style={{ height: 80 }} />
            {footerSpacerHeight > 0 && <View style={{ height: footerSpacerHeight }} />}
          </>
        }
        renderItem={({ item }) => {
          if (item.type === 'dummy') {
            return <Animated.View style={dummyHeaderStylez} />;
          }
          if (item.type === 'tab') {
            return <>{renderTabBar()}</>;
          }
          if (item.type === 'content') {
            return (
              <View
                style={{ flex: 1, padding: 0 }}
                onLayout={e => setContentHeight(e.nativeEvent.layout.height)}
              >
                {renderContent()}
              </View>
            );
          }
          return null;
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statusBar: {
    // TODO: Optionally add status bar height if needed
    // paddingTop: Constants.statusBarHeight,
  },
});

export default AnimatedHeaderList; 