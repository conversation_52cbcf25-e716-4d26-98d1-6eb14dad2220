import { useTheme } from '@/app/theme/ThemeContext';
import ENV from '@/constants/Env';
import { useLocation } from '@/contexts/LocationContext';
import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import RNMapView from './bridge-components/RNMapView';
import { ThemedText } from './ThemedText';

/**
 * Displays a radar/map centered on the user's current location using RNMapView.
 * Handles loading, error, and permission states gracefully.
 */
export const CurrentRadarCard: React.FC = () => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);
  const { location, isLoading, error, hasPermission, refreshLocation } = useLocation();

  // If still loading location
  if (isLoading) {
    return <ActivityIndicator size="small" style={themedStyles.centered} />;
  }

  // If no permission, prompt user
  if (!hasPermission) {
    return (
      <View style={themedStyles.card}>
        <ThemedText style={themedStyles.error}>Location permission required to show radar map.</ThemedText>
        <ThemedText style={themedStyles.link} onPress={() => {
          console.log('[CurrentRadarCard] User tapped to enable location permission.');
          refreshLocation();
        }}>Tap to enable location</ThemedText>
      </View>
    );
  }

  // If error getting location
  if (error) {
    return (
      <View style={themedStyles.card}>
        <ThemedText style={themedStyles.error}>Failed to get location: {error}</ThemedText>
      </View>
    );
  }

  // If location is not available
  if (!location) {
    return (
      <View style={themedStyles.card}>
        <ThemedText style={themedStyles.error}>Location not available.</ThemedText>
      </View>
    );
  }

  // Prepare the location for the native map
  const { latitude, longitude } = location;

  return (
    <View style={themedStyles.card}>
      <ThemedText style={themedStyles.title}>Current Radar</ThemedText>
      {/* RNMapView displays the native map centered on the user's location */}
      <RNMapView
        style={themedStyles.mapview}
        mapboxToken={ENV.mapboxToken}
        sunApiToken={ENV.sunApiToken}
        initialCenter={{ latitude, longitude }}
        onMapMove={(event) => {
          //console.log('[CurrentRadarCard] Map moved to:', event.nativeEvent.center);
        }}
      />
    </View>
  );
};

const styles = (colors: any) => StyleSheet.create({
  card: {
    display: 'flex',
    padding: 20,
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 10,
    alignSelf: 'stretch',
    backgroundColor: colors.card,
    borderRadius: 16,
    marginVertical: 12,
    shadowColor: colors.shadow,
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
    color: colors.text,
  },
  mapview: {
    width: '100%',
    height: 180,
    borderRadius: 12,
    backgroundColor: colors.placeholderBackground,
    overflow: 'hidden',
  },
  centered: {
    marginVertical: 16,
  },
  error: {
    color: colors.errorText,
    textAlign: 'center',
    marginVertical: 16,
  },
  link: {
    color: colors.primary,
    textAlign: 'center',
    textDecorationLine: 'underline',
    marginVertical: 8,
  },
});
