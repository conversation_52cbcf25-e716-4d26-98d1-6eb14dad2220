import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import { useSkiConditionsQuery } from '@/hooks/useSkiConditionsQuery';
import useThreeDayForecast from '@/hooks/useThreeDayForecast';
import { useWeatherQuery } from '@/hooks/useWeatherQuery';
import { log } from '@/services/loggerService';
import { ResortData } from '@/services/resortService';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useRef } from 'react';
import {
  ActivityIndicator,
  Animated,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import SnowAmount from './SnowAmount';
import ThreeDayForecast from './ThreeDayForecast';
import WeatherSummaryCompact from './WeatherSummaryCompact';
import Condition_breezy_style_color_light from './weather_conditions/Condition_breezy_style_color_light';
import Condition_snow_style_color_light from './weather_conditions/Condition_snow_style_color_light';

interface ResortCardProps {
  resort: ResortData;
  selectedCard: string | null;
  setSelectedCard: (id: string | null) => void;
}

const formatLocalTime = (ianaTimeZone?: string) => {
  if (!ianaTimeZone) return '--:--';
  const now = new Date();
  return now.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
    timeZone: ianaTimeZone,
  });
};

const formatDate = (dateString?: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

const getWeatherIcon = (condition?: string) => {
  if (!condition) return 'partly-sunny';
  if (condition.includes('Snow')) return 'snow';
  if (condition.includes('Cloudy')) return 'cloudy';
  if (condition.includes('Clear') || condition.includes('Sunny')) return 'sunny';
  return 'partly-sunny';
};

const getCardGradient = (index: number, colors: SemanticColors): [string, string] => {
  const gradients: [string, string][] = [
    colors.cardGradients.blue,
    colors.cardGradients.purple,
    colors.cardGradients.teal,
    colors.cardGradients.red,
    colors.cardGradients.green,
  ];
  return gradients[index % gradients.length];
};

const ResortCard: React.FC<ResortCardProps> = ({ resort, selectedCard, setSelectedCard }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const isSelected = selectedCard === resort.id;
  const expandAnimation = useRef(new Animated.Value(0)).current;

  // Get latitude and longitude from merged resort data
  const latitude = resort.latitude || (resort as any).coordinates?.latitude;
  const longitude = resort.longitude || (resort as any).coordinates?.longitude;
  const geocode = latitude && longitude && latitude !== 0 && longitude !== 0 ? `${latitude},${longitude}` : '';
  
  // Get ski ID - use the id from ResortData (from weather.com API)
  const skiId = resort.id || (resort as any).skiId;

  // Data hooks - only call if we have valid data
  const { data: weatherData, isLoading: weatherLoading, error: weatherError } = useWeatherQuery(geocode);
  const { data: skiConditions, isLoading: skiLoading, error: skiError } = useSkiConditionsQuery(skiId);
  // 3-day forecast hook
  const {
    data: threeDayData,
    isLoading: threeDayLoading,
    error: threeDayError
  } = useThreeDayForecast(geocode);

  // Animate expand/collapse
  useEffect(() => {
    Animated.timing(expandAnimation, {
      toValue: isSelected ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isSelected]);

  const expandedHeight = expandAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 500],
  });

  // Show loading only if we're actively loading data
  if ((geocode && weatherLoading) || (skiId && skiLoading)) {
    return (
      <View style={styles.cardContainer}>
        <View style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}> 
          <View style={{ padding: 32, alignItems: 'center', justifyContent: 'center' }}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={{ color: colors.textMuted, marginTop: 12 }}>Loading resort data...</Text>
          </View>
        </View>
      </View>
    );
  }

  // Show error only if we have actual errors (not just missing data)
  if (weatherError || skiError) {
    log('[ResortCard]', 'warn', 'API Errors:', [{ weatherError, skiError }]);
  }

  // Helper function to calculate days until opening/closing
  const calculateStatusDays = () => {
    if (!skiConditions) {
      log('[ResortCard]', 'warn', 'No ski conditions data available for resort:', [resort.name]);
      return null;
    }
    
    log('[ResortCard]', 'debug', `Ski conditions for ${resort.name}:`, [{
      resortOperatingStatus: skiConditions.resortOperatingStatus,
      projectedOpenDate: skiConditions.projectedOpenDate,
      projectedClosureDate: skiConditions.projectedClosureDate
    }]);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison
    
    // If resort is open, calculate days until closing (if close date available)
    if (skiConditions.resortOperatingStatus?.toLowerCase() === 'open') {
      if (skiConditions.projectedClosureDate) {
        const closureDate = new Date(skiConditions.projectedClosureDate);
        closureDate.setHours(0, 0, 0, 0);
        const diffTime = closureDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        log('[ResortCard]', 'debug', `Days until closure for ${resort.name}:`, [diffDays]);
        return diffDays > 0 ? `${diffDays}d` : null;
      }
      return null; // Open resort with no closure date
    } else {
      // If resort is closed, calculate days until opening
      if (skiConditions.projectedOpenDate) {
        const openingDate = new Date(skiConditions.projectedOpenDate);
        openingDate.setHours(0, 0, 0, 0);
        const diffTime = openingDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        log('[ResortCard]', 'debug', `Days until opening for ${resort.name}:`, [diffDays]);
        return diffDays > 0 ? `${diffDays}d` : null;
      }
      
      // Log warning if no projected opening date found
      log('[ResortCard]', 'warn', `Missing projected opening date for closed resort: ${resort.name}`, [{
        projectedOpenDate: skiConditions.projectedOpenDate,
        resortOperatingStatus: skiConditions.resortOperatingStatus
      }]);
      return null;
    }
  };

  // Data mapping - use actual API response data, log warnings for missing data
  const display = {
    name: resort.name || 'Unknown Resort',
    rating: 4.1, // TODO: Get from real data - log when missing
    distance: resort.distance ? `${Math.round(parseFloat(resort.distance))} km` : 'Unknown',
    driveTime: '--', // TODO: Calculate from real data when available
    temperature: weatherData?.temperature ?? 0,
    seasonStatus: skiConditions?.resortOperatingStatus?.toLowerCase() === 'open' ? 'open' : 'closed',
    statusDays: calculateStatusDays(),
    weather: weatherData?.description || 'Unknown',
    // Expanded data
    snowDepth: skiConditions?.snowDepthBase || '0',
    snowfall24h: skiConditions?.snowFall24hours || '0',
    humidity: weatherData?.humidity || 'Unknown',
    visibility: weatherData?.visibility !== undefined && weatherData?.visibility !== null ? `${weatherData.visibility}` : '--',
    windSpeed: weatherData?.windSpeed !== undefined && weatherData?.windSpeed !== null ? weatherData.windSpeed : '--',
    windDirectionCardinal: weatherData?.windDirectionCardinal || '--',
    liftsOpen: skiConditions?.liftsOpen || 0,
    totalLifts: skiConditions?.resortLifts || 0,
    totalRuns: skiConditions?.resortRuns || 0,
    resortAcres: skiConditions?.resortAcres || 0,
    facilities: {
      alpine: skiConditions?.hasSkiing === 'Y',
      nordic: skiConditions?.hasNordic === 'Y',
      night: skiConditions?.hasNightSkiing === 'Y',
      park: skiConditions?.hasTerrainPark === 'Y',
      tubing: skiConditions?.hasTubing === 'Y',
      snowMaking: skiConditions?.snowMaking === 'Y',
    }
  };

  // Log warnings for missing critical data
  if (!resort.name) {
    log('[ResortCard]', 'warn', 'Missing resort name');
  }
  if (!weatherData?.temperature) {
    log('[ResortCard]', 'warn', `Missing weather temperature for resort: ${resort.name}`);
  }
  if (!weatherData?.description) {
    log('[ResortCard]', 'warn', `Missing weather description for resort: ${resort.name}`);
  }

  // Calculate index for gradient selection
  const resortIndex = Math.abs(resort.name?.charCodeAt(0) || 0) % 5;
  const cardGradient = getCardGradient(resortIndex, colors);

  // Debug log for ianaTimeZone and formatted time
  const debugIana = (resort as any).ianaTimeZone;
  const debugTime = debugIana ? formatLocalTime(debugIana) : '--:--';
  log('[ResortCard]', 'debug', 'Collapsed local time:', [{ 
    ianaTimeZone: debugIana, 
    formatted: debugTime, 
    resortName: resort.name 
  }]);

  return (
    <View style={styles.cardContainer}>
      <TouchableOpacity
        style={styles.touchable}
        activeOpacity={0.95}
        onPress={() => {
          if (isSelected) {
            setSelectedCard(null);
          } else {
            setSelectedCard(resort.id);
          }
        }}
      >
        <ExpoLinearGradient
          colors={colors.cardGradients.default}
          style={[styles.card, isSelected && styles.cardSelected]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Collapsed Content */}
          {!isSelected ? (
            <View style={styles.collapsedContent}>
              <View style={styles.collapsedMainRowWithMore}>
                {/* Left side - Two rows only */}
                <View style={styles.collapsedLeft}>
                  {/* Row 1: Resort name and rating */}
                  <View style={styles.collapsedTopRow}>
                    <Text style={styles.collapsedResortName} numberOfLines={1}>
                      {display.name}
                    </Text>
                    {/* <View style={styles.ratingContainer}>
                      <Ionicons name="star" size={14} color={colors.weatherHighlight} />
                      <Text style={styles.ratingText}>{display.rating}</Text>
                    </View> */}
                  </View>
                  {/* Row 2: Distance, drive time, status */}
                  <View style={styles.collapsedBottomRow}>
                    {/* Local time */}
                    <View style={styles.collapsedDetailItem}>
                      <Ionicons name="time" size={12} color={colors.textMuted} />
                      <Text style={styles.collapsedDetailText}>
                        {debugIana ? debugTime : '--:--'}
                      </Text>
                    </View>
                    <View style={styles.collapsedDetailItem}>
                      <Ionicons name="location" size={12} color={colors.textMuted} />
                      <Text style={styles.collapsedDetailText}>{display.distance}</Text>
                    </View>
                    <View style={styles.collapsedDetailItem}>
                      <Ionicons name="car" size={12} color={colors.textMuted} />
                      <Text style={styles.collapsedDetailText}>{display.driveTime}</Text>
                    </View>
                    {/* Only show open/closed badge in collapsed view */}
                    {!isSelected && (
                      <View style={[styles.openClosedBadge, display.seasonStatus === 'open' ? styles.openBadge : styles.closedBadge]}>
                        <Ionicons
                          name={display.seasonStatus === 'open' ? 'checkmark-circle' : 'time'}
                          size={14}
                          color={display.seasonStatus === 'open' ? colors.success : colors.error}
                        />
                        <Text style={[styles.openClosedText, display.seasonStatus === 'open' ? styles.openText : styles.closedText]}>
                          {display.seasonStatus === 'open' ? 'Open' : display.statusDays || 'Closed'}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
                {/* Middle+Right - Weather summary and expand indicator */}
                <View style={styles.collapsedRightColumn}>
                  <WeatherSummaryCompact
                    temperature={display.temperature}
                    condition={display.weather}
                    iconCode={weatherData?.iconCode}
                  />
                  {/* Expand indicator */}
                  <View style={styles.expandIndicator}>
                    <Ionicons 
                      name="chevron-down" 
                      size={16} 
                      color={colors.textOnCardSecondary} 
                    />
                  </View>
                </View>
              </View>
            </View>
          ) : (
            <View style={styles.expandedContentHeader}>
              {/* Resort name/title is now at the very top */}
              <View style={styles.expandedTopRow}>
                <View style={styles.expandedNameRating}>
                  <Text style={styles.collapsedResortName} numberOfLines={1}>
                    {display.name}
                  </Text>
                  {/* <View style={styles.ratingContainer}>
                    <Ionicons name="star" size={14} color={colors.weatherHighlight} />
                    <Text style={styles.ratingText}>{display.rating}</Text>
                  </View> */}
                </View>
                {/* Collapse indicator */}
                <View style={styles.collapseIndicator}>
                  <Ionicons 
                    name="chevron-up" 
                    size={16} 
                    color={colors.textMuted} 
                  />
                </View>
              </View>
              <View style={styles.collapsedBottomRow}>
                {/* Local time */}
                <View style={styles.collapsedDetailItem}>
                  <Ionicons name="time" size={12} color={colors.textMuted} />
                  <Text style={styles.collapsedDetailText}>
                    {debugIana ? debugTime : '--:--'}
                  </Text>
                </View>
                <View style={styles.collapsedDetailItem}>
                  <Ionicons name="location" size={12} color={colors.textMuted} />
                  <Text style={styles.collapsedDetailText}>{display.distance}</Text>
                </View>
                <View style={styles.collapsedDetailItem}>
                  <Ionicons name="car" size={12} color={colors.textMuted} />
                  <Text style={styles.collapsedDetailText}>{display.driveTime}</Text>
                </View>
              </View>
              {/* Controls can be added here in the future */}
            </View>
          )}

          {/* Expanded Content */}
          <Animated.View style={[styles.expandedContent, isSelected ? { minHeight: 0, maxHeight: 1000 } : { minHeight: 0, maxHeight: 0 }]}>
            {isSelected && (
              <View style={styles.expandedInner}>
                {/* Season Status Banner */}
                <View style={[
                  styles.seasonBanner,
                  display.seasonStatus === 'open' ? styles.seasonOpen : styles.seasonClosed,
                  styles.seasonBannerRow
                ]}>
                  {/* Left: Icon, status, subtext */}
                  <View style={styles.seasonLeft}>
                    <Ionicons 
                      name={display.seasonStatus === 'open' ? 'checkmark-circle' : 'time'}
                      size={28}
                      color={display.seasonStatus === 'open' ? colors.success : colors.error}
                      style={{ marginRight: 4 }}
                    />
                    <View>
                      <Text
                        style={[
                          styles.seasonStatusText,
                          display.seasonStatus === 'open'
                            ? styles.seasonStatusOpenText
                            : styles.seasonStatusClosedText
                        ]}
                      >
                        Season {display.seasonStatus === 'open' ? 'Open' : 'Closed'}
                      </Text>
                      <Text
                        style={[
                          styles.seasonSubtext,
                          display.seasonStatus === 'open'
                            ? styles.seasonSubtextOpen
                            : styles.seasonSubtextClosed
                        ]}
                      >
                        {display.seasonStatus === 'open'
                          ? (skiConditions?.projectedClosureDate ? `Closes ${formatDate(skiConditions.projectedClosureDate)}` : '')
                          : (skiConditions?.projectedOpenDate ? `Opens ${formatDate(skiConditions.projectedOpenDate)}` : '')}
                      </Text>
                    </View>
                  </View>
                  {/* Right: Days until open/close */}
                  {display.statusDays && (
                    <View style={styles.seasonRight}>
                      <Text style={[
                        styles.seasonDaysNumber,
                        display.seasonStatus === 'open'
                          ? styles.seasonStatusOpenText
                          : styles.seasonStatusClosedText
                      ]}>
                        {display.statusDays.replace('d', '')}
                      </Text>
                      <Text style={[
                        styles.seasonDaysLabel,
                        display.seasonStatus === 'open'
                          ? styles.seasonStatusOpenText
                          : styles.seasonStatusClosedText
                      ]}>
                        days
                      </Text>
                    </View>
                  )}
                </View>

                {/* Weather Stats Grid */}
                <View style={styles.statsGrid}>
                  <View style={styles.statCard}>
                    <View style={styles.statIconContainer}>
                      <Ionicons name="thermometer" size={16} color={colors.textMuted} />
                    </View>
                    <Text style={styles.statValue}>{display.temperature}°</Text>
                    <Text style={styles.statLabel}>Feels {display.temperature - 2}°</Text>
                  </View>
                  <View style={styles.statCard}>
                    <View style={styles.statIconContainer}>
                      <Condition_snow_style_color_light style={{ marginRight: 6, width: 20, height: 20 }} />
                    </View>
                    <Text style={styles.statValue}>{display.snowDepth}"</Text>
                    <Text style={styles.statLabel}>Base depth</Text>
                  </View>
                  <View style={styles.statCard}>
                    <View style={styles.statIconContainer}>
                      <Condition_breezy_style_color_light style={{ marginRight: 6, width: 20, height: 20 }} />
                    </View>
                    <Text style={styles.statValue}>{display.windSpeed} mph</Text>
                    <Text style={styles.statLabel}>{display.windDirectionCardinal} wind</Text>
                  </View>
                  <View style={styles.statCard}>
                  <View style={styles.statIconContainer}>
                      <Ionicons name="eye" size={16} color={colors.textMuted} />
                    </View>
                    <Text style={styles.statValue}>{display.visibility}</Text>
                    <Text style={styles.statLabel}>Visibility</Text>
                  </View>
                </View>

                {/* Weather Summary */}
                <View style={styles.weatherSummary}>
                  <Ionicons name="sunny" size={20} color={colors.weatherHighlight} />
                  <View style={styles.weatherDetails}>
                    <Text style={styles.weatherTitle}>{display.weather}</Text>
                    <Text style={styles.weatherSubtitle}>Clear • {display.humidity} humidity</Text>
                  </View>
                  {/* Snowfall info: icon to the left of data, in a row */}
                  <SnowAmount snowfall={display.snowfall24h} />
                </View>
                {/* 3-Day Forecast Section */}
                {/* Show loading, error, or forecast */}
                <View style={{ marginBottom: 16 }}>
                  {threeDayLoading && (
                    <Text style={{ color: colors.textMuted, fontSize: 13 }}>Loading forecast...</Text>
                  )}
                  {threeDayError && (
                    <Text style={{ color: colors.error, fontSize: 13 }}>Unable to load forecast.</Text>
                  )}
                  {threeDayData && threeDayData.forecasts && (
                    <ThreeDayForecast forecasts={threeDayData.forecasts} />
                  )}
                </View>

                {/* Lifts, Runs, and Resort Acres */}
                <View style={styles.operationsRow}>
                  <View style={styles.operationSection}>
                    <View style={styles.operationHeader}>
                      <Ionicons name="flash" size={16} color={colors.success} />
                      <Text style={styles.operationTitle}>Lifts</Text>
                    </View>
                    <View style={styles.progressContainer}>
                      <View style={styles.progressBar}>
                        <View style={[styles.progressFill, { 
                          width: `${(display.liftsOpen / display.totalLifts) * 100}%`,
                          backgroundColor: colors.success
                        }]} />
                      </View>
                      <Text style={styles.progressText}>{display.liftsOpen}/{display.totalLifts}</Text>
                    </View>
                  </View>

                  <View style={styles.operationSection}>
                    <View style={styles.operationHeader}>
                      <Ionicons name="trending-up" size={16} color={colors.info} />
                      <Text style={styles.operationTitle}>Runs</Text>
                    </View>
                    <View style={styles.numberContainer}>
                      <Text style={styles.numberValue}>{display.totalRuns}</Text>
                    </View>
                  </View>

                  <View style={styles.operationSection}>
                    <View style={styles.operationHeader}>
                      <Ionicons name="resize" size={16} color={colors.warning} />
                      <Text style={styles.operationTitle}>Acres</Text>
                    </View>
                    <View style={styles.numberContainer}>
                      <Text style={styles.numberValue}>{display.resortAcres}</Text>
                    </View>
                  </View>
                </View>

                {/* Facilities */}
                <View style={styles.facilitiesGrid}>
                  <View style={[styles.facilityItem, display.facilities.alpine && styles.facilityActive]}>
                    <Ionicons name="triangle" size={12} color={display.facilities.alpine ? colors.success : colors.textMuted} />
                    <Text style={[styles.facilityText, display.facilities.alpine && styles.facilityTextActive]}>Alpine</Text>
                  </View>
                  <View style={[styles.facilityItem, display.facilities.nordic && styles.facilityActive]}>
                    <Ionicons name="walk" size={12} color={display.facilities.nordic ? colors.success : colors.textMuted} />
                    <Text style={[styles.facilityText, display.facilities.nordic && styles.facilityTextActive]}>Nordic</Text>
                  </View>
                  <View style={[styles.facilityItem, display.facilities.night && styles.facilityActive]}>
                    <Ionicons name="moon" size={12} color={display.facilities.night ? colors.success : colors.textMuted} />
                    <Text style={[styles.facilityText, display.facilities.night && styles.facilityTextActive]}>Night</Text>
                  </View>
                  <View style={[styles.facilityItem, display.facilities.park && styles.facilityActive]}>
                    <Ionicons name="triangle" size={12} color={display.facilities.park ? colors.success : colors.textMuted} />
                    <Text style={[styles.facilityText, display.facilities.park && styles.facilityTextActive]}>Park</Text>
                  </View>
                  <View style={[styles.facilityItem, display.facilities.tubing && styles.facilityActive]}>
                    <Ionicons name="water" size={12} color={display.facilities.tubing ? colors.success : colors.textMuted} />
                    <Text style={[styles.facilityText, display.facilities.tubing && styles.facilityTextActive]}>Tubing</Text>
                  </View>
                  <View style={[styles.facilityItem, display.facilities.snowMaking && styles.facilityActive]}>
                    <Ionicons name="snow" size={12} color={display.facilities.snowMaking ? colors.success : colors.textMuted} />
                    <Text style={[styles.facilityText, display.facilities.snowMaking && styles.facilityTextActive]}>Snow Making</Text>
                  </View>
                </View>
              </View>
            )}
          </Animated.View>
        </ExpoLinearGradient>
      </TouchableOpacity>
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  cardContainer: {
    marginBottom: 16,
  },
  touchable: {
    borderRadius: 16,
  },
  card: {
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  cardSelected: {
    // Add any selected state styling here
  },
  collapsedContent: {
    flexDirection: 'column',
    padding: 16,
    paddingTop: 16,
    minHeight: 80,
  },
  collapsedMainRowWithMore: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 12,
  },
  collapsedLeft: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  collapsedTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  collapsedResortName: {
    fontSize: 16,
    fontFamily: 'Inter',
    fontWeight: '600',
    color: colors.textOnCard,
    marginRight: 8,
  },
  collapsedBottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  collapsedDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  collapsedDetailText: {
    fontSize: 12,
    color: colors.textOnCardSecondary,
    fontWeight: '500',
    fontFamily: 'Inter',
  },
  collapsedRightColumn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  temperatureText: {
    fontSize: 36,
    fontFamily: 'Inter',
    fontWeight: '600',
    color: colors.textOnCard,
    lineHeight: 40,
  },
  weatherCondition: {
    fontSize: 13,
    color: colors.textOnCardSecondary,
    textAlign: 'right',
    fontFamily: 'Inter',
    fontWeight: '500',
  },
  tempLabel: {
    fontSize: 12,
      color: colors.textOnCardSecondary,
      fontFamily: 'Inter',
    fontWeight: '500',
  },
  moreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 4,
  },
  moreText: {
    fontSize: 13,
    color: colors.textOnCardSecondary,
    fontFamily: 'Inter',
    fontWeight: '600',
  },
  expandedContent: {
    overflow: 'hidden',
  },
  expandedInner: {
    paddingTop: 0,
    paddingLeft: 20,
    paddingRight: 20,
    paddingBottom: 16,
  },
  seasonBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surfaceSuccess,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    gap: 8,
  },
  seasonOpen: {
    backgroundColor: colors.surfaceSuccess,
    borderColor: colors.success,
  },
  seasonClosed: {
    backgroundColor: colors.surfaceErrorLight,
    borderColor: colors.errorLight,
  },
  seasonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
    flex: 1,
  },
  seasonSubtext: {
    fontSize: 12,
    color: colors.textOnCardSecondary,
    fontFamily: 'Inter',
    fontWeight: '500',
  },
  limitedBadge: {
    backgroundColor: colors.warning,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8,
  },
  limitedText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.backgroundOverlayLight,
    borderRadius: 12,
    padding: 10,
    alignItems: 'center',
    gap: 0,
  },
  statIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 30,
    width: "100%"
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
    width: "100%",
    textAlign: 'center',
  },
  statLabel: {
    fontSize: 10,
    color: colors.textMuted,
    textAlign: 'center',
    fontFamily: 'Inter',
    width: "100%",
  },
  weatherSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundOverlayLight,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    gap: 12,
  },
  weatherDetails: {
    flex: 1,
  },
  weatherTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
  },
  weatherSubtitle: {
    fontSize: 14,
    color: colors.textMuted,
    fontFamily: 'Inter',
  },
  snowfallInfo: {
    alignItems: 'center',
    gap: 4,
  },
  snowfallText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
  },
  snowfallLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: 'Inter',
  },
  operationsRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  operationSection: {
    flex: 1,
  },
  operationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  operationTitle: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    fontFamily: 'Inter',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: colors.backgroundOverlayLight,
    borderRadius: 4,
  },
  progressFill: {
    height: 8,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
  },
  numberContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  numberValue: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
  },
  facilitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 0,
  },
  facilityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: colors.backgroundAccent,
    gap: 4,
    width: '30%',
  },
  facilityActive: {
    backgroundColor: colors.surfaceSuccess,
  },
  facilityText: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: 'Inter',
    fontWeight: '500',
  },
  facilityTextActive: {
    color: colors.success,
  },
  expandedContentHeader: {
    padding: 20,
    paddingTop: 20, // Reduced to match side padding, no time badge
  },
  expandedHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  expandedLeft: {
    flex: 1,
    marginRight: 16,
  },
  expandedRight: {
    alignItems: 'flex-end',
  },
  expandedResortName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.textOnCard,
    fontFamily: 'Inter',
    marginBottom: 8,
  },
  expandedDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  expandedTemperature: {
    fontSize: 36,
    fontWeight: '600',
    color: colors.textOnDark,
    lineHeight: 40,
  },
  expandedWeather: {
    fontSize: 13,
    color: colors.textDimmed,
    textAlign: 'right',
    fontWeight: '500',
  },
  expandedTempLabel: {
    fontSize: 12,
    color: colors.textFaded,
    fontWeight: '500',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusOpen: {
    backgroundColor: colors.surfaceSuccess,
  },
  statusClosed: {
    backgroundColor: colors.surfaceError,
  },
  statusOpenText: {
    color: colors.success,
  },
  statusClosedText: {
    color: colors.error,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.weatherHighlight,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: 12,
    color: colors.textDimmed,
    fontWeight: '500',
  },
  openClosedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 4,
    minWidth: 0,
    justifyContent: 'center',
  },
  openBadge: {
    backgroundColor: colors.surfaceSuccess,
  },
  closedBadge: {
    backgroundColor: colors.surfaceError,
  },
  openClosedText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textDimmed,
  },
  openText: {
    color: colors.success,
  },
  closedText: {
    color: colors.error,
  },
  moreButtonPillSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundAccent,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 2,
    marginTop: 2,
  },
  moreButtonPillSmallText: {
    color: colors.textDimmed,
    fontSize: 12,
    fontWeight: '600',
    marginRight: 2,
  },
  seasonBannerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderWidth: 1.5,
    gap: 12,
  },
  seasonLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 4,
  },
  seasonStatusText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  seasonRight: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    minWidth: 60,
  },
  seasonDaysNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 20,
  },
  seasonDaysLabel: {
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.8,
    marginTop: -2,
  },
  seasonStatusOpenText: {
    color: colors.success,
  },
  seasonStatusClosedText: {
    color: colors.error,
  },
  seasonSubtextOpen: {
    color: colors.success,
  },
  seasonSubtextClosed: {
    color: colors.error,
  },
  expandIndicator: {
    padding: 1,
  },
  collapseIndicator: {
    padding: 1,
  },

  expandedTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  expandedNameRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ResortCard; 