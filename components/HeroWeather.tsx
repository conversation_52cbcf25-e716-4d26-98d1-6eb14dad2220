import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import { useReverseGeocodingQuery } from '@/hooks/useReverseGeocodingQuery';
import { useWeatherQuery } from '@/hooks/useWeatherQuery';
import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';

interface HeroWeatherProps {
  location: string;
}

export const HeroWeather: React.FC<HeroWeatherProps> = ({ location }: HeroWeatherProps) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);
  const { data: weatherData, isLoading: weatherLoading, error: weatherError } = useWeatherQuery(location);
  const { data: geoData, isLoading: geoLoading, error: geoError } = useReverseGeocodingQuery(location);

  if (weatherLoading || geoLoading) {
    return (
      <View style={themedStyles.container}>
        <ThemedText style={[themedStyles.location, themedStyles.placeholder]}>{'\u00A0'}</ThemedText>
        <ThemedText style={[themedStyles.temp, themedStyles.placeholder]}>{'\u00A0'}</ThemedText>
        <ThemedText style={[themedStyles.desc, themedStyles.placeholder]}>{'\u00A0'}</ThemedText>
        <ThemedText style={[themedStyles.range, themedStyles.placeholder]}>{'\u00A0'}</ThemedText>
        <ActivityIndicator size="large" style={themedStyles.loadingIndicator} />
      </View>
    );
  }
  if (weatherError) return <ThemedText style={themedStyles.error}>Failed to load weather data</ThemedText>;
  if (geoError) return <ThemedText style={themedStyles.error}>Failed to load location data</ThemedText>;
  if (!weatherData || !geoData) return null;

  return (
    <View style={themedStyles.container}>
      <ThemedText style={themedStyles.location}>{geoData.fullAddress}</ThemedText>
      <ThemedText
        style={themedStyles.temp}
        numberOfLines={1}
        adjustsFontSizeToFit
      >
        {weatherData.temperature}°
      </ThemedText>
      <ThemedText style={themedStyles.desc}>{weatherData.description}</ThemedText>
      <ThemedText style={themedStyles.range}>High {weatherData.high}° • Low {weatherData.low}°</ThemedText>
    </View>
  );
};

const styles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 25,
    paddingVertical: 16,
    backgroundColor: 'transparent',
  },
  location: {
    color: colors.heroText,
    fontSize: 16,
    fontWeight: '400',
    textAlign: 'center',
  },
  temp: {
    fontSize: 72,
    fontWeight: '200',
    color: colors.heroText,
    marginTop: 2,
    marginBottom: 2,
    lineHeight: 80,
    minHeight: 80,
    maxWidth: 220,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.18)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  desc: {
    color: colors.heroText,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  range: {
    color: colors.heroText,
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
  },
  centered: {
    marginTop: 40,
  },
  error: {
    color: colors.errorText,
    textAlign: 'center',
    marginTop: 40,
  },
  placeholder: {
    backgroundColor: 'rgba(255,255,255,0.12)',
    borderRadius: 6,
    minHeight: 20,
    minWidth: 80,
    marginVertical: 4,
    color: 'transparent', // Hide text but keep space
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -20,
    marginTop: -20,
    zIndex: 1,
  },
});
