import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { getWeatherIconComponent } from './weather_conditions/weatherIconResolver';

interface MiniThreeDayForecastDay {
  temperatureMax: number;
  temperatureMin: number;
  iconCode: number;
}

interface MiniThreeDayForecastProps {
  forecasts: MiniThreeDayForecastDay[];
}

/**
 * Mini 2-day forecast for leaderboard row: icons + high/low temps, no day names.
 */
const MiniThreeDayForecast: React.FC<MiniThreeDayForecastProps> = ({ forecasts }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  // Only show the next 2 days
  const days = forecasts && forecasts.length > 2 ? forecasts.slice(1, 3) : [];
  if (!days.length) return null;

  return (
    <View style={styles.container}>
      {days.map((day, idx) => {
        const Icon = getWeatherIconComponent(day.iconCode, 'day') as React.FC<any>;
        return (
          <View key={idx} style={styles.dayContainer}>
            <View style={styles.dayBg}>
              <Icon width={18} height={18} />
              <View style={styles.tempsRow}>
                <Text style={styles.tempMax}>{day.temperatureMax}°</Text>
                <Text style={styles.tempMin}>/{day.temperatureMin}°</Text>
              </View>
            </View>
          </View>
        );
      })}
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginLeft: 4,
  },
  dayContainer: {
    alignItems: 'center',
    marginHorizontal: 2,
    minWidth: 32,
  },
  dayBg: {
    // backgroundColor: 'rgba(55, 65, 81, 0.12)',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 32,
  },
  tempsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tempMax: {
    fontSize: 11,
    fontWeight: 'bold',
    color: colors.weatherHighlight,
  },
  tempMin: {
    fontSize: 10,
    color: colors.textMuted,
    marginLeft: 1,
  },
});

export default MiniThreeDayForecast; 