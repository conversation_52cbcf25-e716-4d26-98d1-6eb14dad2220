import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import { useResortsQuery } from '@/hooks/useResortsQuery';
import { useSkiResortsQuery } from '@/hooks/useSkiResortsQuery';
import { ResortData } from '@/services/resortService';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { ActivityIndicator, Pressable, StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';
import ResortLogo from './ui/ResortLogo';

interface ResortCardListProps {
  geocode: string; // Latitude,Longitude string
  limit?: number; // Optional: max number of resorts to fetch
}

const ResortCard: React.FC<{ resort: ResortData; logo: string; colors: SemanticColors }> = ({ resort, logo, colors }) => {
  const themedStyles = styles(colors);
  
  return (
    <View style={themedStyles.card}>
      <View style={themedStyles.topRow}>
        <View style={{ marginRight: 10 }}>
          <ResortLogo name={resort.name || 'Unknown Resort'} logoUrl={logo} size={40} />
        </View>
        <View style={themedStyles.infoSection}>
          <ThemedText style={themedStyles.name}>{resort.name || 'Unknown Resort'}</ThemedText>
          <ThemedText style={themedStyles.distance}>{resort.distance} miles away</ThemedText>
        </View>
      </View>
      <View style={themedStyles.buttonRow}>
        <Pressable style={themedStyles.checkInBtn} onPress={() => { /* TODO: Implement check-in */ }}>
          <Ionicons name="location" size={18} color={colors.primaryText} style={{ marginRight: 4 }} />
          <ThemedText style={themedStyles.checkInText}>Check-In</ThemedText>
        </Pressable>
        <Pressable style={themedStyles.moreInfoBtn} onPress={() => { /* TODO: Implement more info */ }}>
          <ThemedText style={themedStyles.moreInfoText}>More Info</ThemedText>
        </Pressable>
      </View>
    </View>
  );
};

export const ResortCardList: React.FC<ResortCardListProps> = ({ geocode, limit }) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);
  
  // Debug: Log geocode prop
  console.log('[ResortCardList] geocode prop:', geocode);

  // Fetch resorts using the new API and geocode
  console.log('[ResortCardList] Calling useResortsQuery with geocode:', geocode, 'limit:', limit);
  const { data, isLoading, error } = useResortsQuery(geocode, limit);
  console.log('[ResortCardList] Calling useSkiResortsQuery');
  const skiResortsQuery = useSkiResortsQuery(); // Still used for logo lookup

  // Debug: Log loading and error states
  console.log('[ResortCardList] useResortsQuery:', { isLoading, error, data });
  console.log('[ResortCardList] useSkiResortsQuery:');

  if (isLoading || skiResortsQuery.isLoading) return <ActivityIndicator size="small" style={themedStyles.centered} />;
  if (error || skiResortsQuery.error) return <ThemedText style={themedStyles.error}>Failed to load resorts</ThemedText>;
  if (!data || !skiResortsQuery.data) return null;

  // Helper: Find logo by matching resort name (case-insensitive)
  const getLogoForResort = (resortName: string) => {
    const match = skiResortsQuery.data.find(r => r.name.toLowerCase() === resortName.toLowerCase());
    return match?.logo || '';
  };

  return (
    <View style={themedStyles.list}>
      {data.map(resort => (
        <ResortCard key={resort.id} resort={resort} logo={getLogoForResort(resort.name)} colors={colors} />
      ))}
    </View>
  );
};

const styles = (colors: SemanticColors) => StyleSheet.create({
  list: {
    gap: 16,
  },
  card: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 10,
    shadowColor: colors.shadow,
    shadowOpacity: 0.07,
    shadowRadius: 8,
    elevation: 2,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoSection: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'column',
  },
  name: {
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.text,
    marginBottom: 2,
  },
  distance: {
    fontSize: 13,
    color: colors.textMuted,
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flexShrink: 1,
    flexGrow: 0,
    minWidth: 0,
    marginTop: 4,
  },
  checkInBtn: {
    flexBasis: 0,
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    borderRadius: 20,
    paddingVertical: 6,
    justifyContent: 'center',
    marginRight: 6,
    minWidth: 0,
  },
  checkInText: {
    color: colors.primaryText,
    fontWeight: '600',
    fontSize: 14,
  },
  moreInfoBtn: {
    flexBasis: 0,
    flexGrow: 1,
    flexShrink: 1,
    backgroundColor: colors.secondary,
    borderRadius: 20,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 0,
  },
  moreInfoText: {
    color: colors.secondaryText,
    fontWeight: '600',
    fontSize: 14,
  },
  centered: {
    marginVertical: 16,
  },
  error: {
    color: colors.errorText,
    textAlign: 'center',
    marginVertical: 16,
  },
});
