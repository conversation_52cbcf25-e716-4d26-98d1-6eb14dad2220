import { requireNativeComponent, ViewStyle } from 'react-native';

interface LocationCoordinate {
    latitude: number;
    longitude: number;
}

interface MapMoveEvent {
    nativeEvent: {
        center: LocationCoordinate;
        zoomLevel: number;
        bearing: number;
        pitch: number;
    };
}

interface RNMapViewProps {
    style?: ViewStyle;
    mapboxToken: string;
    sunApiToken: string;
    initialCenter?: LocationCoordinate;
    onMapMove?: (event: MapMoveEvent) => void;
}

const RNMapView = requireNativeComponent<RNMapViewProps>('RNMapView');

type Props = {
    style?: ViewStyle;
    mapboxToken: string;
    sunApiToken: string;
    initialCenter?: LocationCoordinate;
    onMapMove?: (event: MapMoveEvent) => void;
};

export default function MapViewWrapper(props: Props) {
    return <RNMapView 
        style={{ flex: 1, ...props.style }} 
        mapboxToken={props.mapboxToken} 
        sunApiToken={props.sunApiToken} 
        initialCenter={props.initialCenter}
        onMapMove={props.onMapMove}
    />;
}