// components/placeholders/MapPlaceholder.tsx
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useTheme } from '../../app/theme/ThemeContext'; // Adjusted path

const MapPlaceholder = () => {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]} >
      <Text style={[styles.text, { color: colors.text }]} >Map Placeholder</Text>
      <Text style={[styles.subText, { color: colors.textSecondary }]} >An interactive map (e.g., trail map, resort location) will be shown here.</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 180, // Slightly taller for map
  },
  text: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default MapPlaceholder;
