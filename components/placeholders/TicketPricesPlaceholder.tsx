// components/placeholders/TicketPricesPlaceholder.tsx
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useTheme } from '../../app/theme/ThemeContext'; // Adjusted path

const TicketPricesPlaceholder = () => {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]} >
      <Text style={[styles.text, { color: colors.text }]} >Ticket Prices Placeholder</Text>
      <Text style={[styles.subText, { color: colors.textSecondary }]} >Lift ticket pricing and package information will be displayed here.</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  text: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default TicketPricesPlaceholder;
