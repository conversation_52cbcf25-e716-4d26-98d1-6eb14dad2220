import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { useTranslation } from '../i18n/useTranslation';
import { ThemedText } from './ThemedText';

interface ElevationData {
  altitude: number; // in feet
  windSpeed: number; // in mph
  visibility: number; // in miles
}

interface MountainElevationCardProps {
  elevationData?: ElevationData[];
}

/**
 * MountainElevationCard Component
 * 
 * Displays current weather conditions at different mountain elevations
 * including altitude, wind speed (gusts), and visibility data.
 * Used in resort modal alongside other weather information cards.
 */
const MountainElevationCard: React.FC<MountainElevationCardProps> = ({ elevationData }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const { t } = useTranslation();

  // Mock data for development - TODO: Replace with real elevation data when API is available
  const mockElevationData: ElevationData[] = [
    { altitude: 7200, windSpeed: 25, visibility: 0.5 },
    { altitude: 4265, windSpeed: 5, visibility: 7 },
    { altitude: 2200, windSpeed: 2, visibility: 15 }
  ];

  const dataToDisplay = elevationData || mockElevationData;

  /**
   * Get wind speed color based on intensity
   * Red for high winds (>20 mph), yellow for moderate (10-20 mph), green for calm (<10 mph)
   */
  const getWindSpeedColor = (windSpeed: number): string => {
    if (windSpeed >= 20) return colors.error;
    if (windSpeed >= 10) return colors.warning;
    return colors.success;
  };

  /**
   * Format visibility value with appropriate unit
   * Shows "<1 mi" for values less than 1, otherwise shows whole number
   */
  const formatVisibility = (visibility: number): string => {
    if (visibility < 1) return t('mountainElevationCard.lessThanOneMile');
    return t('mountainElevationCard.miles', { count: Math.round(visibility) });
  };

  /**
   * Format wind speed with appropriate styling
   */
  const formatWindSpeed = (windSpeed: number): string => {
    return `${windSpeed} ${t('mountainElevationCard.mph')}`;
  };

  return (
    <View style={styles.container}>
      <ThemedText style={styles.title}>{t('mountainElevationCard.rightNow')}</ThemedText>
          <Image
              source={require('../assets/images/mountain-elevations.png')}
              style={styles.mountainImage}
              resizeMode="contain"
          />
      
      {/* Header Row */}
      <View style={styles.headerRow}>
        <ThemedText style={styles.headerText}>{t('mountainElevationCard.altitude')}</ThemedText>
        <ThemedText style={styles.headerText}>{t('mountainElevationCard.gusts')}</ThemedText>
        <ThemedText style={styles.headerText}>{t('mountainElevationCard.visibility')}</ThemedText>
      </View>

      {/* Content with Mountain and Lines */}
      <View style={styles.contentArea}>
        
        {/* Data Rows with Dotted Lines */}
        {dataToDisplay.map((data, index) => (
          <View key={index} style={[styles.dataRowWithLine, getRowPosition(index)]}>
            {/* Data Values */}
            <View style={styles.dataValues}>
              <ThemedText style={styles.altitudeText}>
                {data.altitude} {t('mountainElevationCard.feet')}
              </ThemedText>
              <ThemedText style={[
                styles.windSpeedText, 
                { color: getWindSpeedColor(data.windSpeed) }
              ]}>
                {formatWindSpeed(data.windSpeed)}
              </ThemedText>
              <ThemedText style={[
                styles.visibilityText,
                { color: data.visibility < 10 ? colors.warning : colors.success }
              ]}>
                {formatVisibility(data.visibility)}
              </ThemedText>
            </View>
            
            {/* Dotted Line */}
            {/* <View style={[styles.dottedLine, getDottedLineLength(index)]} /> */}
          </View>
        ))}
      </View>
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.border,
    paddingTop: 20,
    paddingBottom: 1,
    paddingLeft: 10,
    paddingRight: 10,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOpacity: 0.07,
    shadowRadius: 8,
    elevation: 2,
    fontFamily: 'Inter',
    // borderWidth: 1,
    // borderColor: 'red',
    // backgroundColor: 'red',
  },
  title: {
    fontSize: 18,
    fontWeight: '500',
    fontFamily: 'Inter',
    color: colors.text,
    marginBottom: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    fontFamily: 'Inter',
    marginBottom: 10,
    paddingBottom: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    marginRight: 80, // Space for mountain
    paddingRight: 50,
  },
  headerText: {
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '500',
    color: colors.textSecondary,
    flex: 1,
    textAlign: 'left',
  },
  contentArea: {
    position: 'relative',
    height: 120,
  },
  mountainImage: {
    position: 'absolute',
    right: 0,
    top: 40,
    width: "50%",
    height: "90%",
    zIndex: 1,
    // opacity: 0.3,
  },
  dataRowWithLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dataValues: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // width: '100%', // Leave space for dotted line
    marginRight: "10%",
    paddingRight: "30%",
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    fontFamily: 'Inter',
    fontWeight: '500',
  },
  altitudeText: {
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '500',
    color: colors.text,
    flex: 1,
    textAlign: 'left',
  },
  windSpeedText: {
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  visibilityText: {
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  dottedLine: {
    height: 1,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    borderStyle: 'solid',
    marginLeft: 10,
  },
});

/**
 * Get row positioning for each elevation level
 */
const getRowPosition = (index: number) => {
  const positions = [
    { top: 0 },   // Top elevation
    { top: 40 },   // Mid elevation  
    { top: 80 },  // Base elevation
  ];
  return positions[index];
};

/**
 * Get dotted line length for each elevation level
 */
const getDottedLineLength = (index: number) => {
  const lengths = [
    { width: 80 },   // Top - shorter line
    { width: 60 },  // Mid - medium line
    { width: 15 },  // Base - longer line
  ];
  return lengths[index];
};

export default MountainElevationCard; 