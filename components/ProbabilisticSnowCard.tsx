import React, { useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SemanticColors, useTheme } from '../app/theme/ThemeContext';
import { useProbabilisticSnowfallQuery } from '../hooks/useProbabilisticSnowfallQuery';
import { useTranslation } from '../i18n/useTranslation';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';
import Condition_snow_style_solid from './weather_conditions/Condition_snow_style_solid';

interface ProbabilisticSnowCardProps {
  location: string; // geocode in "lat,lon" format
  initialTimespan?: '24hr' | '48hr' | '72hr';
  showDebug?: boolean; // Show debug information
}

interface CustomBarProps {
  label: string;
  value: number;
  maxValue: number;
  isHighlighted?: boolean;
  colors: any;
}

const CustomBar: React.FC<CustomBarProps> = ({ label, value, maxValue, isHighlighted, colors }) => {
  const percentage = (value / maxValue) * 100;
  
  console.log(`Label: ${label}, percentage: ${percentage}`);
  
  return (
    <View style={barStyles.barContainer}>
      {/* Background bar */}
      <View style={[barStyles.backgroundBar, { backgroundColor: '#EFEFEF' }]}>
        {/* Filled portion */}
        <View 
          style={[
            barStyles.filledBar, 
            { 
              width: `${percentage}%`,
              backgroundColor: isHighlighted ? '#3B82F6' : '#CCCCCC'
            }
          ]} 
        />
        {/* Overlaid label */}
        <View style={barStyles.labelContainer}>
          <Text style={[barStyles.barLabel, { color: isHighlighted ? colors.textOnDark : colors.textOnCard }]}>
            {label}
          </Text>
        </View>
      </View>
    </View>
  );
};

/**
 * ProbabilisticSnowCard component that displays probabilistic snowfall forecast
 * with tab functionality for different timespans
 */
export const ProbabilisticSnowCard: React.FC<ProbabilisticSnowCardProps> = ({ 
  location, 
  initialTimespan = '24hr',
  showDebug = false 
}) => {
  const { colors } = useTheme();
  const { t } = useTranslation();
  const styles = createStyles(colors);
  const [debugExpanded, setDebugExpanded] = useState(false);
  const [selectedTimespan, setSelectedTimespan] = useState<'24hr' | '48hr' | '72hr'>(initialTimespan);

  const timespans: Array<'24hr' | '48hr' | '72hr'> = ['24hr', '48hr', '72hr'];

  // Get probabilistic snowfall data for all timespans
  const { 
    data: data24hr, 
    isLoading: isLoading24hr, 
    error: error24hr,
    refetch: refetch24hr
  } = useProbabilisticSnowfallQuery(location, '24hr');

  const { 
    data: data48hr, 
    isLoading: isLoading48hr, 
    error: error48hr,
    refetch: refetch48hr
  } = useProbabilisticSnowfallQuery(location, '48hr');

  const { 
    data: data72hr, 
    isLoading: isLoading72hr, 
    error: error72hr,
    refetch: refetch72hr
  } = useProbabilisticSnowfallQuery(location, '72hr');

  // Helper function to get data for a specific timespan
  const getDataForTimespan = (timespan: '24hr' | '48hr' | '72hr') => {
    switch (timespan) {
      case '24hr': return data24hr;
      case '48hr': return data48hr;
      case '72hr': return data72hr;
    }
  };

  // Helper function to check if timespan has expected snowfall
  const hasExpectedSnowfall = (timespan: '24hr' | '48hr' | '72hr') => {
    const data = getDataForTimespan(timespan);
    return data?.expectedSnowfall ? data.expectedSnowfall > 0 : false;
  };

  // Get current selected data
  const probabilisticData = getDataForTimespan(selectedTimespan);
  const isProbabilisticLoading = isLoading24hr || isLoading48hr || isLoading72hr;
  const probabilisticError = error24hr || error48hr || error72hr;
  
  const refetchProbabilistic = () => {
    refetch24hr();
    refetch48hr();
    refetch72hr();
  };

  // Loading state
  if (isProbabilisticLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <ThemedText style={styles.title}>{t('probabilisticSnow.title')}</ThemedText>
          <TouchableOpacity
            style={styles.debugToggle}
            onPress={() => setDebugExpanded(!debugExpanded)}
          >
            <ThemedText style={styles.debugToggleText}>
              {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
            </ThemedText>
          </TouchableOpacity>
        </View>
        
        {/* Tab Bar */}
        <View style={styles.tabContainer}>
          {timespans.map((timespan) => (
            <TouchableOpacity
              key={timespan}
              style={[
                styles.tab,
                timespan === selectedTimespan && styles.activeTab
              ]}
              onPress={() => setSelectedTimespan(timespan)}
            >
              <View style={styles.tabContent}>
                {/* Don't show snow icons during loading */}
                <ThemedText style={[
                  styles.tabText,
                  timespan === selectedTimespan && styles.activeTabText
                ]}>
                  {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
                </ThemedText>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <ThemedText style={styles.loadingText}>{t('probabilisticSnow.loading')}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  // Error state
  if (probabilisticError) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <ThemedText style={styles.title}>{t('probabilisticSnow.title')}</ThemedText>
          <TouchableOpacity
            style={styles.debugToggle}
            onPress={() => setDebugExpanded(!debugExpanded)}
          >
            <ThemedText style={styles.debugToggleText}>
              {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
            </ThemedText>
          </TouchableOpacity>
        </View>
        
        {/* Tab Bar */}
        <View style={styles.tabContainer}>
          {timespans.map((timespan) => (
            <TouchableOpacity
              key={timespan}
              style={[
                styles.tab,
                timespan === selectedTimespan && styles.activeTab
              ]}
              onPress={() => setSelectedTimespan(timespan)}
            >
              <View style={styles.tabContent}>
                {/* Don't show snow icons during error */}
                <ThemedText style={[
                  styles.tabText,
                  timespan === selectedTimespan && styles.activeTabText
                ]}>
                  {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
                </ThemedText>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>
            {t('probabilisticSnow.loadingError')}
          </ThemedText>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              refetchProbabilistic();
            }}
          >
            <Text style={[styles.retryButtonText, { color: colors.primaryText }]}>
              {t('probabilisticSnow.retryButton')}
            </Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  // No data state
  if (!probabilisticData || !probabilisticData.bins || probabilisticData.bins.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <ThemedText style={styles.title}>{t('probabilisticSnow.title')}</ThemedText>
          <TouchableOpacity
            style={styles.debugToggle}
            onPress={() => setDebugExpanded(!debugExpanded)}
          >
            <ThemedText style={styles.debugToggleText}>
              {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
            </ThemedText>
          </TouchableOpacity>
        </View>
        
        {/* Tab Bar */}
        <View style={styles.tabContainer}>
          {timespans.map((timespan) => (
            <TouchableOpacity
              key={timespan}
              style={[
                styles.tab,
                timespan === selectedTimespan && styles.activeTab
              ]}
              onPress={() => setSelectedTimespan(timespan)}
            >
              <View style={styles.tabContent}>
                {/* Show snow icon if snowfall is expected for this timespan */}
                {hasExpectedSnowfall(timespan) && (
                  <Condition_snow_style_solid 
                    width={12} 
                    height={12} 
                    style={{ marginRight: 6 }}
                  />
                )}
                <ThemedText style={[
                  styles.tabText,
                  timespan === selectedTimespan && styles.activeTabText
                ]}>
                  {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
                </ThemedText>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <ThemedText style={styles.noDataText}>
          {t('probabilisticSnow.noData')}
        </ThemedText>
      </ThemedView>
    );
  }

  // Render the content with tabs
  return (
    <ThemedView style={styles.container}>
      <View style={styles.headerContainer}>
        <ThemedText style={styles.title}>{t('probabilisticSnow.title')}</ThemedText>
        <TouchableOpacity
          style={styles.debugToggle}
          onPress={() => setDebugExpanded(!debugExpanded)}
        >
          <ThemedText style={styles.debugToggleText}>
            {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Tab Bar */}
      <View style={styles.tabContainer}>
        {timespans.map((timespan) => (
          <TouchableOpacity
            key={timespan}
            style={[
              styles.tab,
              timespan === selectedTimespan && styles.activeTab
            ]}
            onPress={() => setSelectedTimespan(timespan)}
          >
            <View style={styles.tabContent}>
              {/* Show snow icon if snowfall is expected for this timespan */}
              {hasExpectedSnowfall(timespan) && (
                <Condition_snow_style_solid 
                  width={12} 
                  height={12} 
                  style={{ marginRight: 6 }}
                />
              )}
              <ThemedText style={[
                styles.tabText,
                timespan === selectedTimespan && styles.activeTabText
              ]}>
                {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
              </ThemedText>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content for selected timespan */}
      <ProbabilisticSnowCardContent
        data={probabilisticData}
        timespan={selectedTimespan}
        showDebug={showDebug || debugExpanded}
        colors={colors}
        t={t}
      />
    </ThemedView>
  );
};

/**
 * Content component for individual timespan cards
 */
interface ProbabilisticSnowCardContentProps {
  data: any;
  timespan: '24hr' | '48hr' | '72hr';
  showDebug: boolean;
  colors: SemanticColors;
  t: (key: string, options?: any) => string;
}

const ProbabilisticSnowCardContent: React.FC<ProbabilisticSnowCardContentProps> = ({
  data,
  timespan,
  showDebug,
  colors,
  t
}) => {
  const styles = createStyles(colors);
  const mostLikelyBin = data.bins.reduce((prev: any, current: any) => 
    current.probability > prev.probability ? current : prev
  );

  // Convert API data to chart format
  const chartData = data.bins.map((bin: any) => ({
    label: bin.label,
    value: bin.probability,
    maxValue: 100,
    isHighlighted: bin.label === mostLikelyBin.label
  }));

  return (
    <View style={styles.contentContainer}>
      {/* Debug information */}
      {showDebug && (
        <View style={styles.debugContainer}>
          <ThemedText>Expected Snowfall: {data.expectedSnowfall}</ThemedText>
          <ThemedText>Card Type: {data.cardType}</ThemedText>
          <ThemedText>Units: {data.units}</ThemedText>
          <ThemedText>Most Likely Bin: {mostLikelyBin.label} ({mostLikelyBin.probability}%)</ThemedText>
          <ThemedText>Total Probability: {data.bins.reduce((sum: number, bin: any) => sum + bin.probability, 0)}%</ThemedText>
          <ThemedText>Bins:</ThemedText>
          {data.bins.map((bin: any, index: number) => (
            <ThemedText key={index}>
              {bin.label}: {bin.probability}% (Range: {bin.rangeCode})
            </ThemedText>
          ))}
        </View>
      )}

      {/* Subtitle with expected snowfall info */}
      <ThemedText style={styles.subtitle}>
        {data.expectedSnowfall <= 0 
          ? t('probabilisticSnow.noSnowfallExpected') 
          : t('probabilisticSnow.expectedSnowfall', { 
              amount: data.expectedSnowfall, 
              range: mostLikelyBin.label 
            })
        }
      </ThemedText>

      {/* Custom horizontal bar chart */}
      <View style={styles.chartContainer}>
        {chartData.map((item: any, index: number) => (
          <CustomBar
            key={index}
            label={item.label}
            value={item.value}
            maxValue={item.maxValue}
            isHighlighted={item.value > 0 && item.isHighlighted}
            colors={colors}
          />
        ))}
      </View>

      {/* Bottom labels */}
      <View style={styles.bottomLabels}>
        <ThemedText style={styles.bottomLabel}>{t('probabilisticSnow.lessLikely')}</ThemedText>
        <ThemedText style={styles.bottomLabel}>{t('probabilisticSnow.moreLikely')}</ThemedText>
      </View>

      {/* Likelihood text */}
      {/* <View style={styles.likelihoodContainer}>
        <ThemedText style={styles.likelihoodText}>
          {t('probabilisticSnow.mostLikely')}
        </ThemedText>
        <ThemedText style={[styles.likelihoodValue, { color: colors.primary }]}>
          {mostLikelyBin.label}
        </ThemedText>
      </View> */}
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 12,
    minHeight: 200,
    borderWidth: 1,
    borderColor: colors.border,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '500',
    color: colors.textOnCard,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 20,
    color: colors.textOnCard,
    lineHeight: 20,
  },
  debugToggle: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: colors.border,
  },
  debugToggleText: {
    fontSize: 12,
    color: colors.textOnCardSecondary,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.border,
    borderRadius: 8,
    padding: 2,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textOnCardSecondary,
  },
  activeTabText: {
    color: colors.primaryText,
  },
  contentContainer: {
    flex: 1,
  },
  debugContainer: {
    backgroundColor: colors.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  chartContainer: {
    marginBottom: 0,
  },
  bottomLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bottomLabel: {
    fontSize: 11,
    fontWeight: '400',
    fontFamily: 'Inter',
    color: colors.textOnCardSecondary,
  },
  likelihoodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingHorizontal: 16, // Account for chart padding
  },
  likelihoodText: {
    fontSize: 12,
    color: colors.textOnCardSecondary,
    fontWeight: '500',
  },
  likelihoodValue: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  noDataText: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    textAlign: 'center',
    paddingVertical: 20,
  },
});

const barStyles = StyleSheet.create({
  barContainer: {
    marginBottom: 8,
  },
  backgroundBar: {
    height: 23,
    borderRadius: 5,
    position: 'relative',
    justifyContent: 'center',
  },
  filledBar: {
    height: '100%',
    borderRadius: 5,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  labelContainer: {
    position: 'absolute',
    left: 12,
    right: 12,
    justifyContent: 'center',
    height: '100%',
  },
  barLabel: {
    fontSize: 14,
    fontWeight: '500',
    zIndex: 1,
  },
}); 