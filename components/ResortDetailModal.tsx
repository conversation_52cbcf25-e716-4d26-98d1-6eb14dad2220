import { Ionicons } from '@expo/vector-icons';
import { useEffect, useState } from 'react';
import {
  Dimensions,
  Image,
  ImageBackground,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SemanticColors, useTheme } from '../app/theme/ThemeContext';
import { Resort, useSkiResortsQuery } from '../hooks/useSkiResortsQuery';
import { useTranslation } from '../i18n/useTranslation';
import { getResortPassById } from '../services/resortPassService';
// import { getDefaultResortWeatherData, getResortWeatherData } from '../services/resortWeatherService';
import { HourlyForecastBar } from './HourlyForecastBar';
import MountainElevationCard from './MountainElevationCard';
import { ProbabilisticSnowCard } from './ProbabilisticSnowCard';
import ResortLogo from './ui/ResortLogo';
import WIPBannerWrapper from './WIPBannerWrapper';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ResortDetailModalProps {
  visible: boolean;
  resort: Resort | null;
  onClose: () => void;
}

type DetailTab = 'overview' | 'forecast' | 'snowfall';

export default function ResortDetailModal({ visible, resort, onClose }: ResortDetailModalProps) {
  const { colors: themeColors } = useTheme();
  const styles = createStyles(themeColors);
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<DetailTab>('overview');

  // Load all ski resorts
  const { data: allResorts, isLoading: resortsLoading, error: resortsError } = useSkiResortsQuery();

  // Find the full resort data by id
  const fullResort = resort && allResorts
    ? allResorts.find(r => String(r.id) === String(resort.id))
    : resort;

  useEffect(() => {
    console.log('Resort (prop):', { resort });
    console.log('Full resort (from allResorts):', fullResort);
    if (fullResort) {
      console.log('Resort passId:', fullResort.passId);
      const pass = fullResort.passId ? getResortPassById(fullResort.passId) : null;
      console.log('Result of getResortPassById:', pass);
    }
  }, [resort, allResorts]);

  // Get resort background image or fallback to default
  const getResortBackgroundImage = (resort: Resort) => {
    // Use the backgroundImage if it's a valid URL
    if (resort.backgroundImage && resort.backgroundImage.startsWith('http')) {
      return { uri: resort.backgroundImage };
    }
    // Fallback to default
    return require('../assets/images/default-resort-background.png');
  };
  
  if (!visible) return null;
  if (resortsLoading) {
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="pageSheet" onRequestClose={onClose}>
        <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]} edges={['top']}>
          <Text style={{ textAlign: 'center', marginTop: 40 }}>Loading resort details...</Text>
        </SafeAreaView>
      </Modal>
    );
  }
  if (!fullResort) return null;

  // const weatherData = getResortWeatherData(fullResort.id) || getDefaultResortWeatherData(fullResort.id, fullResort.name);
  const backgroundImage = getResortBackgroundImage(fullResort);
  const resortPass = fullResort.passId ? getResortPassById(fullResort.passId) : null;

  const tabs: { key: DetailTab; label: string }[] = [
    { key: 'overview', label: 'Overview' },
    { key: 'forecast', label: 'Forecast' },
    { key: 'snowfall', label: 'Snowfall' },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]} edges={['top']}>
        {/* Header with background image */}
        <View style={styles.header}>
          <ImageBackground
            source={backgroundImage}
            style={styles.headerBackground}
            imageStyle={styles.headerImageStyle}
          >
            <View style={styles.headerOverlay} />
            <View style={styles.headerContent}>
              {/* Close button */}
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Ionicons name="chevron-back" size={24} color="white" />
              </TouchableOpacity>

              {/* Action buttons */}
              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.actionButton}>
                  <Ionicons name="notifications-outline" size={24} color="white" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <Ionicons name="add" size={24} color="white" />
                </TouchableOpacity>
              </View>
            </View>
          </ImageBackground>
        </View>

        {/* Pass Icon below header image, full width, rectangular */}
        {/* {resortPass && resortPass.logo && (
          <TouchableOpacity
            style={styles.passLogoRectContainer}
            onPress={() => {
              if (resortPass.website) {
                // TODO: Handle pass website navigation
                console.log('Navigate to:', resortPass.website);
              }
            }}
          >
            <Image
              source={{ uri: resortPass.logo }}
              style={styles.passLogoRect}
              resizeMode="contain"
            />
          </TouchableOpacity>
        )} */}

        {/* Logo positioned to overlap header and content */}
        <View style={styles.logoContainer}>
          <View style={{ marginBottom: 8 }}>
            <ResortLogo name={fullResort.name} logoUrl={fullResort.logo} size={80} />
          </View>
        </View>

        {/* Resort info section */}
        <View style={[styles.resortInfo, { backgroundColor: themeColors.background }]} >
          {/* Pass logo, small and left-aligned above resort name */}
          {resortPass && resortPass.logo && (
            <TouchableOpacity
              onPress={() => {
                if (resortPass.website) {
                  // TODO: Handle pass website navigation
                  console.log('Navigate to:', resortPass.website);
                }
              }}
            >
              <Image
                source={{ uri: resortPass.logo }}
                style={styles.passLogoSmall}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}
          <Text style={[styles.resortName, { color: themeColors.text }]} >
            {fullResort.name}
          </Text>

          <Text style={[styles.resortDescription, { color: themeColors.textSecondary }]} >
            {fullResort.description}
          </Text>

          {/* Website link */}
          
          <TouchableOpacity>
            <Text style={[styles.websiteLink, { color: themeColors.primary }]} >
              {fullResort.website?.replace(/^https?:\/\//, '')}
            </Text>
          </TouchableOpacity>
          
        </View>

        {/* Tabs */}
        <View style={[styles.tabsContainer, { backgroundColor: themeColors.background, borderBottomColor: themeColors.border }]} >
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key && [styles.activeTab, { borderBottomColor: themeColors.primary }],
              ]}
              onPress={() => setActiveTab(tab.key)}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: activeTab === tab.key ? themeColors.primary : themeColors.textSecondary },
                  activeTab === tab.key && styles.activeTabText,
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {activeTab === 'overview' && (
            <View style={styles.overviewContent}>
              {/* Hourly Forecast Bar as first item */}
              <HourlyForecastBar location={`${fullResort.coordinates.latitude},${fullResort.coordinates.longitude}`} />
              <WIPBannerWrapper>
                <MountainElevationCard />
              </WIPBannerWrapper>
            </View>
          )}

          {activeTab === 'forecast' && (
            <View style={styles.overviewContent}>
              <WIPBannerWrapper>
                <ProbabilisticSnowCard 
                  location={`${fullResort.coordinates.latitude},${fullResort.coordinates.longitude}`}
                  initialTimespan="24hr"
                />
              </WIPBannerWrapper>
            
            </View>
          )}

          {activeTab === 'snowfall' && (
            <View style={styles.tabContent}>
              <Text style={[styles.tabContentText, { color: themeColors.text }]} >
                Snowfall content coming soon...
              </Text>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: 160,

  },
  headerBackground: {
    flex: 1,
    justifyContent: 'space-between',    
  },
  headerImageStyle: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    height: 160,
    
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent', // Ensure overlay doesn't hide the image
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 20,
    
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resortInfo: {
    paddingHorizontal: 20,
    paddingTop: 0,
    paddingBottom: 8,
    position: 'relative',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -15,
  },
  logoContainer: {
    position: 'absolute',
    right: 20,
    top: 100, // Position to overlap header and content
    zIndex: 10, // Ensure logo appears above other elements
    elevation: 10, // For Android
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: colors.textOnDark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    
  },
  logoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: colors.textOnDark,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logoText: {
    color: colors.textOnDark,
    fontSize: 24,
    fontWeight: '500',
  },
  passLogoSmall: {
    width: 48,
    height: 24,
    marginBottom: 4,
  },
  resortName: {
    fontSize: 22,
    fontFamily: 'Inter',
    fontWeight: '600',
    marginBottom: 2,
    textAlign: 'left',
  },
  resortDescription: {
    fontSize: 13,
    fontFamily: 'Inter',
    fontWeight: '400',
    marginBottom: 4,
    textAlign: 'left',
  },
  websiteLink: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'left',
    marginTop: 2,
  },
  passContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  passInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  passLogo: {
    width: 40,
    height: 24,
    marginRight: 12,
  },
  passDetails: {
    flex: 1,
  },
  passLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  passName: {
    fontSize: 16,
    fontWeight: '600',
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  activeTabText: {
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  overviewContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  weatherCard: {
    flexDirection: 'row',
    gap: 20,
  },
  currentWeather: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 140,
  },
  weatherIcon: {
    fontSize: 40,
    marginBottom: 8,
  },
  conditionName: {
    color: colors.textOnDark,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  temperature: {
    color: colors.textOnDark,
    fontSize: 32,
    fontWeight: 'bold',
  },
  weatherStats: {
    flex: 1,
    justifyContent: 'space-around',
    paddingLeft: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  statSubLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  hourlyForecast: {
    marginTop: 24,
  },
  hourlyItem: {
    alignItems: 'center',
    marginRight: 16,
    paddingVertical: 8,
  },
  hourlyTime: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 8,
  },
  hourlyIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  hourlyTemp: {
    fontSize: 14,
    fontWeight: '600',
  },
  mapContainer: {
    marginTop: 24,
  },
  mapPlaceholder: {
    height: 120,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapText: {
    fontSize: 16,
    fontWeight: '500',
  },
  tabContent: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
  },
  tabContentText: {
    fontSize: 16,
    textAlign: 'center',
  },
  passLogoRectContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 8,
  },
  passLogoRect: {
    width: 120,
    height: 40,
    resizeMode: 'contain',
  },
});
