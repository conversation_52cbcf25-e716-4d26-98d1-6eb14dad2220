import { log } from '@/services/loggerService';
import Slider from '@react-native-community/slider';
import { forwardRef, useCallback, useRef, useState } from 'react';
import {
  Animated,
  Modal,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { WebView, WebViewMessageEvent } from 'react-native-webview';

/**
 * EmbeddedMapWebView
 *
 * Loads the embedded map HTML from the app's assets using a WebView.
 * Handles loading, error, and message passing. Supports injected JS for tokens and theming.
 * Includes a map type selector overlay with various map styles, map layers controls,
 * and animation controls for time-aware layers.
 *
 * Props:
 *   - injectedConfig: EmbeddedMapWebViewInjectedConfig (config for injected JS)
 *   - onMessage?: (event: WebViewMessageEvent) => void (handler for messages from the WebView)
 *   - style?: any (optional style for the container)
 *
 * Usage:
 *   <EmbeddedMapWebView
 *     injectedConfig={...}
 *     onMessage={...}
 *     style={{ flex: 1 }}
 *   />
 */

// Available map styles based on Pangea user guide and testharness
export const MAP_STYLES = [
  { 
    id: 'standard', 
    name: 'Standard', 
    url: 'mapbox://styles/mapbox/standard',
    description: 'Default modern style' 
  },
  { 
    id: 'streets', 
    name: 'Streets', 
    url: 'mapbox://styles/mapbox/streets-v12',
    description: 'Detailed street view' 
  },
  { 
    id: 'outdoors', 
    name: 'Outdoors', 
    url: 'mapbox://styles/mapbox/outdoors-v12',
    description: 'Perfect for outdoor activities' 
  },
  { 
    id: 'light', 
    name: 'Light', 
    url: 'mapbox://styles/mapbox/light-v11',
    description: 'Clean, minimal style' 
  },
  { 
    id: 'dark', 
    name: 'Dark', 
    url: 'mapbox://styles/mapbox/dark-v11',
    description: 'Dark theme for low light' 
  },
  { 
    id: 'satellite', 
    name: 'Satellite', 
    url: 'mapbox://styles/mapbox/satellite-v9',
    description: 'High-resolution satellite imagery' 
  },
  { 
    id: 'satellite-streets', 
    name: 'Satellite Streets', 
    url: 'mapbox://styles/mapbox/satellite-streets-v12',
    description: 'Satellite with street labels' 
  },
];

// Available map layers based on Pangea user guide and testharness
export const MAP_LAYERS = [
  {
    id: 'satrad',
    name: 'Satellite/Radar',
    description: 'Weather radar and satellite imagery',
    icon: '🛰️',
    isAnimatable: true // This layer supports animation
  },
  {
    id: 'snow',
    name: 'Snow Accumulation',
    description: 'Snow depth and accumulation data',
    icon: '❄️',
    isAnimatable: true // This layer supports animation
  },
  {
    id: 'resorts',
    name: 'Resort Locations',
    description: 'Ski resort markers and information',
    icon: '🎿',
    isAnimatable: false // Static layer
  },
];

// Animation control state interface based on testharness patterns
export interface AnimationControlState {
  active: boolean;
  isPlaying: boolean;
  currentFrame: number;
  totalFrames: number;
  currentTime: string;
}

// Platform-specific asset loading for map-embed.html in assets directory
const getLocalHtmlSource = () => {
  if (Platform.OS === 'android') {
      return { uri: 'file:///android_asset/pangea-bundle/index.html' };
  } else if (Platform.OS === 'ios') {
    // Metro will bundle this file if referenced with require()
    return require('../assets/pangea-bundle/index.html');
  } else {
    return undefined;
  }
};

// Define the config interface for injected variables
export interface EmbeddedMapWebViewInjectedConfig {
  mapboxToken: string;
  sunApiToken: string;
  initialLocation?: { latitude: number; longitude: number };
  initialZoomLevel?: number;
  initialMapStyle?: string; // Add default map style
  theme?: {
    background: string;
    text: string;
  };
}

export interface EmbeddedMapWebViewProps {
  injectedConfig: EmbeddedMapWebViewInjectedConfig;
  onMessage?: (event: WebViewMessageEvent) => void;
  style?: any;
}

// Helper to generate the injected JS from the config
function buildInjectedJS(config: EmbeddedMapWebViewInjectedConfig): string {
  const lines = [
    `window.MAPBOX_TOKEN = "${config.mapboxToken}";`,
    `window.SUN_API_TOKEN = "${config.sunApiToken}";`,
  ];
  if (config.initialLocation) {
    lines.push(`window.INITIAL_LOCATION = { latitude: ${config.initialLocation.latitude}, longitude: ${config.initialLocation.longitude} };`);
  }
  if (typeof config.initialZoomLevel === 'number') {
    lines.push(`window.INITIAL_ZOOM_LEVEL = ${config.initialZoomLevel};`);
  }
  if (config.initialMapStyle) {
    lines.push(`window.INITIAL_MAP_STYLE = "${config.initialMapStyle}";`);
  }
  if (config.theme) {
    lines.push(`document.documentElement.style.setProperty('--background', '${config.theme.background}');`);
    lines.push(`document.documentElement.style.setProperty('--text', '${config.theme.text}');`);
  }
  return `(function() {\n${lines.join('\n')}\n})();\ntrue;`;
}

// Helper function to format timestamp into user-friendly format
const formatAnimationTime = (timestamp: string): string => {
  if (!timestamp || timestamp === '--:--:--') {
    return 'Mon 3:00 pm';
  }
  
  try {
    const date = new Date(timestamp);
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'short',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };
    return date.toLocaleString('en-US', options);
  } catch (error) {
    log('[EmbeddedMapWebView]', 'warn', 'Failed to parse animation timestamp:', [timestamp]);
    return 'Mon 3:00 pm';
  }
};

export const EmbeddedMapWebView = forwardRef<WebView, EmbeddedMapWebViewProps>(
  ({ injectedConfig, onMessage, style }, ref) => {
    const [webViewError, setWebViewError] = useState(false);
    const [isMapStyleModalVisible, setIsMapStyleModalVisible] = useState(false);
    const [isMapLayersModalVisible, setIsMapLayersModalVisible] = useState(false);
    const [currentMapStyle, setCurrentMapStyle] = useState(
      injectedConfig.initialMapStyle || MAP_STYLES[0].url
    );
    const [activeLayers, setActiveLayers] = useState<Set<string>>(new Set());
    
    // Animation control state management
    const [animationState, setAnimationState] = useState<AnimationControlState>({
      active: false,
      isPlaying: false,
      currentFrame: 1,
      totalFrames: 1,
      currentTime: '--:--:--'
    });
    
    // Animation panel slide animation
    const animationPanelHeight = useRef(new Animated.Value(0)).current;
    const [isAnimationPanelVisible, setIsAnimationPanelVisible] = useState(false);
    
    const webViewRef = useRef<WebView>(null);
    const localHtml = getLocalHtmlSource();

    log('[EmbeddedMapWebView]', 'debug', 'Rendering with currentMapStyle:', [currentMapStyle]);
    log('[EmbeddedMapWebView]', 'debug', 'Animation state:', [animationState]);

    // Find current style details for display
    const getCurrentStyleInfo = useCallback(() => {
      return MAP_STYLES.find(style => style.url === currentMapStyle) || MAP_STYLES[0];
    }, [currentMapStyle]);

    // Check if any active layers are animatable
    const hasAnimatableLayers = useCallback(() => {
      return Array.from(activeLayers).some(layerId => 
        MAP_LAYERS.find(layer => layer.id === layerId)?.isAnimatable
      );
    }, [activeLayers]);

    // Slide animation control for animation panel
    const showAnimationPanel = useCallback(() => {
      log('[EmbeddedMapWebView]', 'debug', 'Showing animation panel');
      setIsAnimationPanelVisible(true);
      Animated.timing(animationPanelHeight, {
        toValue: 140, // Increased height to clear tab bar
        duration: 300,
        useNativeDriver: false,
      }).start();
    }, [animationPanelHeight]);

    const hideAnimationPanel = useCallback(() => {
      log('[EmbeddedMapWebView]', 'debug', 'Hiding animation panel');
      Animated.timing(animationPanelHeight, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start(() => {
        setIsAnimationPanelVisible(false);
      });
    }, [animationPanelHeight]);

    // Animation control handlers - following testharness pattern exactly
    const handleAnimationPlay = useCallback(() => {
      log('[EmbeddedMapWebView]', 'debug', 'Play button pressed - sending playAnimation message');
      if (webViewRef.current) {
        // Testharness: postMapMessage('playAnimation', {})
        const message = {
          type: 'playAnimation',
          payload: {},
          requestId: `req-${Date.now()}-${Math.random()}`
        };
        log('[EmbeddedMapWebView]', 'debug', 'Sending play message:', [message]);
        webViewRef.current.postMessage(JSON.stringify(message));
      }
    }, []);

    const handleAnimationPause = useCallback(() => {
      log('[EmbeddedMapWebView]', 'debug', 'Pause button pressed - sending stopAnimation message');
      if (webViewRef.current) {
        // Testharness: postMapMessage('stopAnimation', {})
        const message = {
          type: 'stopAnimation',
          payload: {},
          requestId: `req-${Date.now()}-${Math.random()}`
        };
        log('[EmbeddedMapWebView]', 'debug', 'Sending stop message:', [message]);
        webViewRef.current.postMessage(JSON.stringify(message));
      }
    }, []);

    const handleFrameChange = useCallback((frame: number) => {
      log('[EmbeddedMapWebView]', 'debug', 'Frame slider changed to:', [frame, '- sending setAnimationFrame message']);
      if (webViewRef.current) {
        // Testharness: postMapMessage('setAnimationFrame', { frame })
        const message = {
          type: 'setAnimationFrame',
          payload: { frame },
          requestId: `req-${Date.now()}-${Math.random()}`
        };
        log('[EmbeddedMapWebView]', 'debug', 'Sending frame change message:', [message]);
        webViewRef.current.postMessage(JSON.stringify(message));
      }
    }, []);

    // Handle map style selection
    const handleMapStyleSelect = useCallback((selectedStyle: typeof MAP_STYLES[0]) => {
      setCurrentMapStyle(selectedStyle.url);
      setIsMapStyleModalVisible(false);
      
      // Send message to WebView to change map style
      if (webViewRef.current) {
        const message = {
          type: 'setBasemap',
          payload: { styleUrl: selectedStyle.url },
          requestId: `req-${Date.now()}-${Math.random()}`
        };
        
        webViewRef.current.postMessage(JSON.stringify(message));
      }
    }, []);

    // Handle map layer toggle
    const handleLayerToggle = useCallback(async (layerId: string) => {
      log('[EmbeddedMapWebView]', 'debug', 'Layer toggle pressed for:', [layerId]);
      const isCurrentlyActive = activeLayers.has(layerId);
      const newActiveLayers = new Set(activeLayers);
      
      if (isCurrentlyActive) {
        // Remove layer
        log('[EmbeddedMapWebView]', 'debug', 'Removing layer:', [layerId]);
        newActiveLayers.delete(layerId);
        setActiveLayers(newActiveLayers);
        
        if (webViewRef.current) {
          const message = {
            type: 'removeLayer',
            payload: { layerId },
            requestId: `req-${Date.now()}-${Math.random()}`
          };
          log('[EmbeddedMapWebView]', 'debug', 'Sending removeLayer message:', [message]);
          webViewRef.current.postMessage(JSON.stringify(message));
        }
      } else {
        // Add layer
        log('[EmbeddedMapWebView]', 'debug', 'Adding layer:', [layerId]);
        newActiveLayers.add(layerId);
        setActiveLayers(newActiveLayers);
        
        if (webViewRef.current) {
          if (layerId === 'resorts') {
            // For resorts, fetch GeoJSON data first (like in testharness)
            try {
              log('[EmbeddedMapWebView]', 'debug', 'Fetching resorts GeoJSON data...');
              let resortsGeojson = undefined;
              
              // Platform-specific asset loading for map-embed.html in assets directory
             
                if (Platform.OS === 'android') {
                  resortsGeojson =  { uri: 'file:///android_asset/pangea-bundle/resorts.json' };
                } else if (Platform.OS === 'ios') {
                  // Metro will bundle this file if referenced with require()
                  resortsGeojson = require('../assets/pangea-bundle/resorts.json');
                } else {
                  log('[EmbeddedMapWebView]', 'debug', 'No platform found, setting resortsGeojson to undefined'); 
                  resortsGeojson = undefined;
                }
              
              const message = {
                type: 'addLayerWithData',
                payload: { layerId: 'resorts', data: resortsGeojson },
                requestId: `req-${Date.now()}-${Math.random()}`
              };
              log('[EmbeddedMapWebView]', 'debug', 'Sending addLayerWithData message:', [message.type, message.requestId]);
              webViewRef.current.postMessage(JSON.stringify(message));
            } catch (error) {
              log('[EmbeddedMapWebView]', 'error', 'Failed to load resorts data:', [error]);
              // Rollback the layer state
              newActiveLayers.delete(layerId);
              setActiveLayers(newActiveLayers);
            }
          } else {
            // For other layers, use simple addLayer
            const message = {
              type: 'addLayer',
              payload: { layerId },
              requestId: `req-${Date.now()}-${Math.random()}`
            };
            log('[EmbeddedMapWebView]', 'debug', 'Sending addLayer message:', [message]);
            webViewRef.current.postMessage(JSON.stringify(message));
          }
        }
      }

      // Check if we should show/hide animation controls after layer change
      const willHaveAnimatableLayers = Array.from(newActiveLayers).some(id => 
        MAP_LAYERS.find(layer => layer.id === id)?.isAnimatable
      );
      
      if (willHaveAnimatableLayers && !animationState.active) {
        log('[EmbeddedMapWebView]', 'debug', 'Animatable layer added, will show animation controls when ready');
      } else if (!willHaveAnimatableLayers && animationState.active) {
        log('[EmbeddedMapWebView]', 'debug', 'No more animatable layers, hiding animation controls');
        hideAnimationPanel();
        setAnimationState(prev => ({ ...prev, active: false }));
      }
    }, [activeLayers]);

    // Handle messages from WebView (enhanced to capture style changes and animation updates)
    const handleWebViewMessage = useCallback((event: WebViewMessageEvent) => {
      try {
        const message = JSON.parse(event.nativeEvent.data);
        
        // Only log non-log messages to avoid spam
        if (message.type !== 'log') {
          log('[EmbeddedMapWebView]', 'debug', 'Received message:', [message]);
        }
        
        // Update current style if the WebView reports a style change
        if (message.type === 'mapStyleChanged' && message.styleUrl) {
          log('[EmbeddedMapWebView]', 'info', 'Map style changed to:', [message.styleUrl]);
          setCurrentMapStyle(message.styleUrl);
        }
        
        // Handle animation updates - following testharness pattern exactly
        if (message.type === 'animationUpdate') {
          log('[EmbeddedMapWebView]', 'debug', 'Animation update received (raw):', [message]);
          // Testharness pattern uses event.data.data, so message.data should contain the animation data
          const animationData = message.data;
          log('[EmbeddedMapWebView]', 'debug', 'Animation data:', [animationData]);
          
          if (animationData && typeof animationData.active !== 'undefined') {
            const { active, isPlaying, currentFrame, totalFrames, currentTime } = animationData;
            
            const newAnimationState = {
              active,
              isPlaying,
              currentFrame,
              totalFrames,
              currentTime
            };
            
            log('[EmbeddedMapWebView]', 'debug', 'Setting new animation state:', [newAnimationState]);
            setAnimationState(newAnimationState);
            
            // Show/hide animation panel based on activity
            if (active && !animationState.active) {
              log('[EmbeddedMapWebView]', 'debug', 'Showing animation panel - going from inactive to active');
              showAnimationPanel();
            } else if (!active && animationState.active) {
              log('[EmbeddedMapWebView]', 'debug', 'Hiding animation panel - going from active to inactive');
              hideAnimationPanel();
            }
          } else {
            log('[EmbeddedMapWebView]', 'warn', 'Invalid animation data received:', [animationData]);
          }
        }
        
        // Handle Pangea animation events (started, stopped, frameChanged) - these provide real-time state updates
        if (message.type === 'pangeaAnimationEvent') {
          log('[EmbeddedMapWebView]', 'debug', 'Pangea animation event received:', [message]);
          const { event, data } = message;
          
          if (event === 'started') {
            log('[EmbeddedMapWebView]', 'debug', 'Animation started event - updating play state');
            setAnimationState(prev => ({
              ...prev,
              isPlaying: true,
              currentFrame: data.currentFrame || prev.currentFrame,
              totalFrames: data.totalFrames || prev.totalFrames
            }));
          } else if (event === 'stopped') {
            log('[EmbeddedMapWebView]', 'debug', 'Animation stopped event - updating play state');
            setAnimationState(prev => ({
              ...prev,
              isPlaying: false,
              currentFrame: data.currentFrame || prev.currentFrame,
              totalFrames: data.totalFrames || prev.totalFrames
            }));
          } else if (event === 'frameChanged') {
            log('[EmbeddedMapWebView]', 'debug', 'Animation frame changed event - updating frame and time');
            setAnimationState(prev => ({
              ...prev,
              currentFrame: data.currentFrame || prev.currentFrame,
              totalFrames: data.totalFrames || prev.totalFrames,
              currentTime: data.currentTime || prev.currentTime
            }));
          }
        }
        

      } catch (error) {
        log('[EmbeddedMapWebView]', 'error', 'Error parsing WebView message:', [error]);
        // Handle non-JSON messages or pass through to parent handler
      }
      
      // Call parent's onMessage handler
      if (onMessage) {
        onMessage(event);
      }
    }, [onMessage, animationState.active, showAnimationPanel, hideAnimationPanel]);

    if (!localHtml) {
      return (
        <View style={[styles.center, style]}>
          <Text>Map bundle not available for this platform.</Text>
        </View>
      );
    }

    // Generate the injected JS from the config
    const injectedJS = buildInjectedJS(injectedConfig);

    return (
      <View style={[{ flex: 1 }, style]}>
        {webViewError ? (
          <View style={styles.center}>
            <Text style={{ fontSize: 16, textAlign: 'center' }}>
              Failed to load the embedded map.
            </Text>
          </View>
        ) : (
          <>
            <WebView
              ref={webViewRef}
              source={localHtml}
              style={{ flex: 1 }}
              onError={() => setWebViewError(true)}
              startInLoadingState={true}
              renderError={() => <></>} // handled by onError
              onMessage={handleWebViewMessage}
              injectedJavaScriptBeforeContentLoaded={injectedJS}
              scrollEnabled={false}
              nestedScrollEnabled={false}
              originWhitelist={['*']}
              allowFileAccess
              allowUniversalAccessFromFileURLs
            />
          </>
        )}
        
        {/* Map Type Selector Button - Always on top */}
        {!webViewError && (
          <TouchableOpacity 
            style={[
              styles.mapTypeButton,
              isAnimationPanelVisible && { bottom: 240 } // Move up when animation panel is visible
            ]}
            onPress={() => setIsMapStyleModalVisible(true)}
            activeOpacity={0.8}
          >
            {/* <Text style={styles.mapTypeButtonIcon}>🗺️</Text> */}
            <Text style={styles.mapTypeButtonText}>Map</Text>
          </TouchableOpacity>
        )}

        {/* Map Layers Button - Stacked above Map Type button */}
        {!webViewError && (
          <TouchableOpacity 
            style={[
              styles.mapLayersButton,
              isAnimationPanelVisible && { bottom: 310 } // Move up when animation panel is visible
            ]}
            onPress={() => setIsMapLayersModalVisible(true)}
            activeOpacity={0.8}
          >
            {/* <Text style={styles.mapLayersButtonIcon}>📍</Text> */}
            <Text style={styles.mapLayersButtonText}>Layers</Text>
          </TouchableOpacity>
        )}

        {/* Map Style Selection Modal */}
        <Modal
          visible={isMapStyleModalVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setIsMapStyleModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <SafeAreaView>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Choose Map Style</Text>
                <TouchableOpacity 
                  onPress={() => setIsMapStyleModalVisible(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.modalContent}>
                {MAP_STYLES.map((mapStyle) => (
                  <TouchableOpacity
                    key={mapStyle.id}
                    style={[
                      styles.mapStyleItem,
                      currentMapStyle === mapStyle.url && styles.mapStyleItemSelected
                    ]}
                    onPress={() => handleMapStyleSelect(mapStyle)}
                  >
                    <View style={styles.mapStyleItemContent}>
                      <Text style={[
                        styles.mapStyleItemName,
                        currentMapStyle === mapStyle.url && styles.mapStyleItemNameSelected
                      ]}>
                        {mapStyle.name}
                      </Text>
                      <Text style={[
                        styles.mapStyleItemDescription,
                        currentMapStyle === mapStyle.url && styles.mapStyleItemDescriptionSelected
                      ]}>
                        {mapStyle.description}
                      </Text>
                    </View>
                    {currentMapStyle === mapStyle.url && (
                      <Text style={styles.checkmark}>✓</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </SafeAreaView>
          </View>
        </Modal>

        {/* Map Layers Selection Modal */}
        <Modal
          visible={isMapLayersModalVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setIsMapLayersModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <SafeAreaView>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Map Layers</Text>
                <TouchableOpacity 
                  onPress={() => setIsMapLayersModalVisible(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.modalContent}>
                {MAP_LAYERS.map((layer) => (
                  <TouchableOpacity
                    key={layer.id}
                    style={[
                      styles.mapLayerItem,
                      activeLayers.has(layer.id) && styles.mapLayerItemSelected
                    ]}
                    onPress={() => handleLayerToggle(layer.id)}
                  >
                    <View style={styles.mapLayerItemContent}>
                      <View style={styles.mapLayerItemHeader}>
                        <Text style={styles.mapLayerItemIcon}>{layer.icon}</Text>
                        <Text style={[
                          styles.mapLayerItemName,
                          activeLayers.has(layer.id) && styles.mapLayerItemNameSelected
                        ]}>
                          {layer.name}
                        </Text>
                      </View>
                      <Text style={[
                        styles.mapLayerItemDescription,
                        activeLayers.has(layer.id) && styles.mapLayerItemDescriptionSelected
                      ]}>
                        {layer.description}
                      </Text>
                    </View>
                    <View style={[
                      styles.checkbox,
                      activeLayers.has(layer.id) && styles.checkboxSelected
                    ]}>
                      {activeLayers.has(layer.id) && (
                        <Text style={styles.checkboxCheck}>✓</Text>
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </SafeAreaView>
          </View>
        </Modal>

        {/* Animation Controls Panel - Slide up from bottom */}
        {/* Debug overlay removed for production */}
        
        <Animated.View 
          style={[
            styles.animationPanel,
            {
              height: animationPanelHeight,
              opacity: animationPanelHeight.interpolate({
                inputRange: [0, 140],
                outputRange: [0, 1],
              }),
            }
          ]}
        >
          <View style={styles.animationPanelContent}>
            <View style={styles.animationHeader}>
              <Text style={styles.animationTitle}>Animation</Text>
            </View>
            
            <View style={styles.animationControls}>
              {!animationState.isPlaying ? (
                <TouchableOpacity
                  style={styles.animationButton}
                  onPress={handleAnimationPlay}
                >
                  <Text style={styles.animationButtonText}>▶️</Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={styles.animationButton}
                  onPress={handleAnimationPause}
                >
                  <Text style={styles.animationButtonText}>⏸️</Text>
                </TouchableOpacity>
              )}
              
              <View style={styles.timeDisplay}>
                <Text style={styles.currentTime}>
                  {formatAnimationTime(animationState.currentTime)}
                </Text>
                <Text style={styles.timeRange}>
                  {animationState.totalFrames > 1 ? `${animationState.totalFrames} Hrs` : '6 Hrs'}
                </Text>
              </View>
            </View>
            
            <View style={styles.sliderContainer}>
              <Slider
                style={styles.frameSlider}
                minimumValue={1}
                maximumValue={animationState.totalFrames}
                value={animationState.currentFrame}
                onValueChange={(value: number) => {
                  // Round to nearest integer since we want discrete frame numbers
                  const targetFrame = Math.round(value);
                  log('[EmbeddedMapWebView]', 'debug', 'Slider value changed to frame:', [targetFrame]);
                  handleFrameChange(targetFrame);
                }}
                step={1} // Ensure we only get integer frame values
                minimumTrackTintColor="#007AFF"      // Progress color (left of thumb)
                maximumTrackTintColor="#E5E5EA"      // Remaining color (right of thumb)
                thumbTintColor="#007AFF"             // Thumb color
              />
              <View style={styles.timeSliderLabels}>
                {Array.from({ length: Math.min(animationState.totalFrames, 8) }, (_, i) => {
                  // Calculate time progression more intelligently
                  const totalLabels = Math.min(animationState.totalFrames, 8);
                  const step = Math.max(1, Math.floor(animationState.totalFrames / (totalLabels - 1)));
                  const frameNumber = i * step + 1;
                  
                  // Convert frame to hour (assuming each frame = 1 hour starting from 1pm)
                  const startHour = 13; // 1pm in 24hr format
                  const currentHour = startHour + frameNumber - 1;
                  
                  let label;
                  if (currentHour <= 12) {
                    label = `${currentHour}a`;
                  } else if (currentHour <= 24) {
                    const displayHour = currentHour > 12 ? currentHour - 12 : currentHour;
                    label = `${displayHour}p`;
                  } else {
                    const nextDayHour = currentHour - 24;
                    const displayHour = nextDayHour > 12 ? nextDayHour - 12 : nextDayHour;
                    label = `${displayHour}a`;
                  }
                  
                  return (
                    <Text key={i} style={styles.timeSliderLabel}>
                      {label}
                    </Text>
                  );
                })}
              </View>
            </View>
          </View>
        </Animated.View>
      </View>
    );
  }
);

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Map Type Button Styles
  mapTypeButton: {
    position: 'absolute',
    bottom: 100, // Move up to clear the tab bar (typical tab bar is ~80px)
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    zIndex: 9999,
  },
  mapTypeButtonIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  mapTypeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  // Modal Styles
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  modalContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  // Map Style Item Styles
  mapStyleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  mapStyleItemSelected: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
  },
  mapStyleItemContent: {
    flex: 1,
  },
  mapStyleItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  mapStyleItemNameSelected: {
    color: '#1976d2',
  },
  mapStyleItemDescription: {
    fontSize: 14,
    color: '#666',
  },
  mapStyleItemDescriptionSelected: {
    color: '#1565c0',
  },
  checkmark: {
    fontSize: 20,
    color: '#2196f3',
    fontWeight: 'bold',
    marginLeft: 12,
  },
  // Map Layers Button Styles
  mapLayersButton: {
    position: 'absolute',
    bottom: 170, // Stacked above the map type button (100 + 70 for spacing)
    right: 20, // Same right position as map type button
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    zIndex: 9999,
  },
  mapLayersButtonIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  mapLayersButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  // Map Layer Item Styles
  mapLayerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  mapLayerItemSelected: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4caf50',
  },
  mapLayerItemContent: {
    flex: 1,
  },
  mapLayerItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  mapLayerItemIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  mapLayerItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  mapLayerItemNameSelected: {
    color: '#2e7d32',
  },
  mapLayerItemDescription: {
    fontSize: 14,
    color: '#666',
  },
  mapLayerItemDescriptionSelected: {
    color: '#388e3c',
  },
  // Checkbox Styles
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#ccc',
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  checkboxSelected: {
    backgroundColor: '#4caf50',
    borderColor: '#4caf50',
  },
  checkboxCheck: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  // Animation Control Panel Styles
  animationPanel: {
    position: 'absolute',
    bottom: 80, // Start above the tab bar (typical tab bar height is ~80px)
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 15, // Higher elevation to ensure it's above everything
    zIndex: 10000, // Higher z-index
  },
  animationPanelContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  animationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  animationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  animationStatus: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  animationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  animationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  animationButtonText: {
    fontSize: 16,
    color: '#fff',
  },
  animationInfo: {
    flex: 1,
    marginLeft: 8,
  },
  animationFrame: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  animationTime: {
    fontSize: 12,
    color: '#666',
  },
  sliderContainer: {
    paddingHorizontal: 4,
    marginTop: 4,
  },
  frameSlider: {
    width: '100%',
    height: 40,
  },
  timeDisplay: {
    flex: 1,
    marginLeft: 16,
  },
  currentTime: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  timeRange: {
    fontSize: 14,
    color: '#666',
  },
  timeSliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  timeSliderLabel: {
    fontSize: 10,
    color: '#666',
  },
});

// TODO: Test on both Android and iOS for correct asset path resolution. 
// TODO: Implement proper error handling for map style changes and layer operations
// TODO: Add support for custom map styles and layers via config
// TODO: Add layer status persistence across app sessions 
// TODO: Add animation controls for time-aware layers (satellite/radar, snow accumulation) 