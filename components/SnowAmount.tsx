import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Condition_snow_style_color_light from './weather_conditions/Condition_snow_style_color_light';

interface SnowAmountProps {
  snowfall: string | number;
}

/**
 * Displays a snow icon, snowfall amount, and '24h snow' label, centered vertically.
 * Used in the expanded weather summary.
 */
const SnowAmount: React.FC<SnowAmountProps> = ({ snowfall }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  // Handle missing/invalid snowfall
  const value = snowfall !== undefined && snowfall !== null && snowfall !== '' ? snowfall : '--';

  return (
    <View style={[styles.container, { flexDirection: 'row', alignItems: 'center', gap: 6 }]}> 
      <Condition_snow_style_color_light style={{ marginRight: 6, width: 20, height: 20 }} />
      <View style={{ alignItems: 'center' }}>
        <Text style={styles.snowfallText}>{value}"</Text>
        <Text style={styles.snowfallLabel}>24h snow</Text>
      </View>
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {},
  snowfallText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.textOnCard,
    fontFamily: 'Inter',
  },
  snowfallLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: 'Inter',
  },
});

export default SnowAmount; 