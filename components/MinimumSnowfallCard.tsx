import React, { useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SemanticColors, useTheme } from '../app/theme/ThemeContext';
import { useProbabilisticSnowfallQuery } from '../hooks/useProbabilisticSnowfallQuery';
import { useTranslation } from '../i18n/useTranslation';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface MinimumSnowfallCardProps {
  location: string; // geocode in "lat,lon" format
  initialTimespan?: '24hr' | '48hr' | '72hr';
  showDebug?: boolean; // Show debug information
}

interface CumulativeData {
  amount: number;
  probability: number;
  color: string;
}

/**
 * MinimumSnowfallCard component that displays probabilistic snowfall forecast
 * as a vertical cumulative probability chart showing minimum snowfall amounts
 */
export const MinimumSnowfallCard: React.FC<MinimumSnowfallCardProps> = ({ 
  location, 
  initialTimespan = '24hr',
  showDebug = false 
}) => {
  const { colors } = useTheme();
  const { t } = useTranslation();
  const styles = createStyles(colors);
  const [debugExpanded, setDebugExpanded] = useState(false);
  const [selectedTimespan, setSelectedTimespan] = useState<'24hr' | '48hr' | '72hr'>(initialTimespan);

  const timespans: Array<'24hr' | '48hr' | '72hr'> = ['24hr', '48hr', '72hr'];

  // Get probabilistic snowfall data for all timespans
  const { 
    data: data24hr, 
    isLoading: isLoading24hr, 
    error: error24hr,
    refetch: refetch24hr
  } = useProbabilisticSnowfallQuery(location, '24hr');

  const { 
    data: data48hr, 
    isLoading: isLoading48hr, 
    error: error48hr,
    refetch: refetch48hr
  } = useProbabilisticSnowfallQuery(location, '48hr');

  const { 
    data: data72hr, 
    isLoading: isLoading72hr, 
    error: error72hr,
    refetch: refetch72hr
  } = useProbabilisticSnowfallQuery(location, '72hr');

  // Helper function to get data for a specific timespan
  const getDataForTimespan = (timespan: '24hr' | '48hr' | '72hr') => {
    switch (timespan) {
      case '24hr': return data24hr;
      case '48hr': return data48hr;
      case '72hr': return data72hr;
    }
  };

  // Get current selected data
  const probabilisticData = getDataForTimespan(selectedTimespan);
  const isProbabilisticLoading = isLoading24hr || isLoading48hr || isLoading72hr;
  const probabilisticError = error24hr || error48hr || error72hr;
  
  const refetchProbabilistic = () => {
    refetch24hr();
    refetch48hr();
    refetch72hr();
  };

  /**
   * Convert probabilistic bins to cumulative probability data for visualization
   * If the data is already alternate type (cumulative), use it directly
   * If it's primary type, extract the meaningful cumulative data
   */
  const convertToCumulativeData = (data: any): CumulativeData[] => {
    if (!data || !data.bins || data.bins.length === 0) return [];

    try {
      // If this is alternate card data, it already has cumulative probabilities
      if (data.cardType === 'alternate') {
        return data.bins
          .filter((bin: any) => bin.label.includes('At least') || bin.label.includes('More than'))
          .map((bin: any, index: number) => {
            // Extract amount from label (e.g., "At least 5 in" -> 5)
            const match = bin.label.match(/(\d+)/);
            const amount = match ? parseFloat(match[1]) : 0;
            
            // Colors for different probability ranges (from darkest to lightest)
            const colors = ['#1E40AF', '#3B82F6', '#93C5FD']; // Blue gradient
            
            return {
              amount,
              probability: bin.probability,
              color: colors[index % colors.length]
            };
          })
          .sort((a: CumulativeData, b: CumulativeData) => a.amount - b.amount);
      }

      // For primary card data, we need to convert to cumulative
      // Sort bins by their numeric range values
      const sortedBins = [...data.bins].sort((a: any, b: any) => {
        // Extract the lower bound from each range
        const getMinValue = (range: string) => {
          if (range.includes('<')) {
            return 0; // "< 1 in" starts at 0
          }
          const match = range.match(/(\d+)/);
          return match ? parseFloat(match[1]) : 0;
        };
        
        return getMinValue(a.range) - getMinValue(b.range);
      });

      // Calculate cumulative probabilities (at least X amount)
      const cumulativeData: CumulativeData[] = [];
      let cumulativeProbability = 100; // Start with 100% chance of at least 0"

      sortedBins.forEach((bin: any, index: number) => {
        // Extract amount from range
        const getMinValue = (range: string) => {
          if (range.includes('<')) return 0;
          const match = range.match(/(\d+)/);
          return match ? parseFloat(match[1]) : 0;
        };
        
        const amount = getMinValue(bin.range);
        
        // Colors for different probability ranges
        const colors = ['#1E40AF', '#3B82F6', '#93C5FD'];
        
        if (cumulativeProbability > 0) {
          cumulativeData.push({
            amount,
            probability: cumulativeProbability,
            color: colors[cumulativeData.length % colors.length]
          });
        }

        // Reduce cumulative probability by this bin's probability
        cumulativeProbability = Math.max(0, cumulativeProbability - bin.probability);
      });

      return cumulativeData.filter(item => item.probability > 0);
    } catch (error) {
      console.error('Error converting to cumulative data:', error);
      return [];
    }
  };

  // Loading state
  if (isProbabilisticLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <ThemedText style={styles.title}>{t('minimumSnowfall.title')}</ThemedText>
          <TouchableOpacity
            style={styles.debugToggle}
            onPress={() => setDebugExpanded(!debugExpanded)}
          >
            <ThemedText style={styles.debugToggleText}>
              {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
            </ThemedText>
          </TouchableOpacity>
        </View>
        
        {/* Tab Bar */}
        <View style={styles.tabContainer}>
          {timespans.map((timespan) => (
            <TouchableOpacity
              key={timespan}
              style={[
                styles.tab,
                timespan === selectedTimespan && styles.activeTab
              ]}
              onPress={() => setSelectedTimespan(timespan)}
            >
              <ThemedText style={[
                styles.tabText,
                timespan === selectedTimespan && styles.activeTabText
              ]}>
                {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <ThemedText style={styles.loadingText}>{t('minimumSnowfall.loading')}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  // Error state
  if (probabilisticError) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <ThemedText style={styles.title}>{t('minimumSnowfall.title')}</ThemedText>
          <TouchableOpacity
            style={styles.debugToggle}
            onPress={() => setDebugExpanded(!debugExpanded)}
          >
            <ThemedText style={styles.debugToggleText}>
              {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
            </ThemedText>
          </TouchableOpacity>
        </View>
        
        {/* Tab Bar */}
        <View style={styles.tabContainer}>
          {timespans.map((timespan) => (
            <TouchableOpacity
              key={timespan}
              style={[
                styles.tab,
                timespan === selectedTimespan && styles.activeTab
              ]}
              onPress={() => setSelectedTimespan(timespan)}
            >
              <ThemedText style={[
                styles.tabText,
                timespan === selectedTimespan && styles.activeTabText
              ]}>
                {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>
            {t('minimumSnowfall.loadingError')}
          </ThemedText>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              refetchProbabilistic();
            }}
          >
            <Text style={[styles.retryButtonText, { color: colors.primaryText }]}>
              {t('minimumSnowfall.retryButton')}
            </Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  // No data state
  if (!probabilisticData || !probabilisticData.bins || probabilisticData.bins.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.headerContainer}>
          <ThemedText style={styles.title}>{t('minimumSnowfall.title')}</ThemedText>
        </View>
        
        <ThemedText style={styles.noDataText}>
          {t('minimumSnowfall.noData')}
        </ThemedText>
      </ThemedView>
    );
  }

  // Convert data to cumulative format
  const cumulativeData = convertToCumulativeData(probabilisticData);
  const maxAmount = Math.max(...cumulativeData.map(item => item.amount));
  const highestProbability = Math.max(...cumulativeData.map(item => item.probability));

  // Generate description text
  const getDescriptionText = () => {
    if (cumulativeData.length === 0) return t('minimumSnowfall.noSnowfallExpected');
    
    // For alternate card data, we have proper cumulative probabilities
    if (probabilisticData.cardType === 'alternate') {
      const highestProb = cumulativeData[0]; // First item has the highest amount/lowest probability
      const lowestAmount = Math.min(...cumulativeData.map(item => item.amount));
      const maxAmount = Math.max(...cumulativeData.map(item => item.amount));
      const timespanText = selectedTimespan === '24hr' ? '24' : selectedTimespan === '48hr' ? '48' : '72';
      
      // Find the probability for the minimum amount that has a reasonable chance
      const meaningfulProb = cumulativeData.find(item => item.probability >= 50) || cumulativeData[cumulativeData.length - 1];
      
      return t('minimumSnowfall.description', {
        probability: Math.round(meaningfulProb.probability),
        amount: meaningfulProb.amount,
        maxAmount: maxAmount,
        timespan: timespanText
      });
    }
    
    // For primary card data converted to cumulative
    const highestProb = cumulativeData[0];
    const maxPossible = maxAmount;
    const timespanText = selectedTimespan === '24hr' ? '24' : selectedTimespan === '48hr' ? '48' : '72';
    
    return t('minimumSnowfall.description', {
      probability: Math.round(highestProb.probability),
      amount: highestProb.amount,
      maxAmount: maxPossible,
      timespan: timespanText
    });
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.headerContainer}>
        <ThemedText style={styles.title}>{t('minimumSnowfall.title')}</ThemedText>
        <TouchableOpacity
          style={styles.debugToggle}
          onPress={() => setDebugExpanded(!debugExpanded)}
        >
          <ThemedText style={styles.debugToggleText}>
            {debugExpanded ? '🔍 Hide Debug' : '🔍 Debug'}
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Tab Bar */}
      <View style={styles.tabContainer}>
        {timespans.map((timespan) => (
          <TouchableOpacity
            key={timespan}
            style={[
              styles.tab,
              timespan === selectedTimespan && styles.activeTab
            ]}
            onPress={() => setSelectedTimespan(timespan)}
          >
            <ThemedText style={[
              styles.tabText,
              timespan === selectedTimespan && styles.activeTabText
            ]}>
              {timespan === '24hr' ? '24h' : timespan === '48hr' ? '48h' : '72h'}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>

      {/* Debug information */}
      {(showDebug || debugExpanded) && (
        <View style={styles.debugContainer}>
          <ThemedText>Raw Data Bins: {probabilisticData.bins.length}</ThemedText>
          <ThemedText>Cumulative Data Points: {cumulativeData.length}</ThemedText>
          <ThemedText>Max Amount: {maxAmount}"</ThemedText>
          <ThemedText>Highest Probability: {highestProbability}%</ThemedText>
          <ThemedText>Cumulative Data:</ThemedText>
          {cumulativeData.map((item, index) => (
            <ThemedText key={index}>
              {item.probability}% chance of ≥{item.amount}"
            </ThemedText>
          ))}
        </View>
      )}

      {/* Description */}
      <ThemedText style={styles.description}>
        {getDescriptionText()}
      </ThemedText>

      {/* Vertical Bar Chart */}
      <View style={styles.chartContainer}>
        <View style={styles.chartContent}>
          {/* Y-axis labels */}
          <View style={styles.yAxisContainer}>
            {[20, 15, 10, 5].map((value) => (
              <View key={value} style={styles.yAxisLabel}>
                <Text style={[styles.yAxisText, { color: colors.textOnCardSecondary }]}>
                  {value}"
                </Text>
              </View>
            ))}
          </View>

          {/* Single Stacked Bar */}
          <View style={styles.stackedBarContainer}>
            <View style={styles.stackedBar}>
              {cumulativeData.map((item, index) => {
                const prevAmount = index > 0 ? cumulativeData[index - 1].amount : 0;
                const segmentHeight = item.amount - prevAmount;
                const heightPercentage = (segmentHeight / 20) * 100; // Normalize to 20" max
                
                return (
                  <View
                    key={index}
                    style={[
                      styles.stackedSegment,
                      {
                        height: `${heightPercentage}%`,
                        backgroundColor: item.color,
                      }
                    ]}
                  >
                    <View style={styles.segmentLabelContainer}>
                      <Text style={[styles.segmentLabel, { color: colors.textOnDark }]}>
                        {Math.round(item.probability)}%+ Chance
                      </Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        </View>

        {/* X-axis label */}
        <View style={styles.xAxisContainer}>
          <Text style={[styles.xAxisText, { color: colors.textOnCardSecondary }]}>
            Next {selectedTimespan === '24hr' ? '24' : selectedTimespan === '48hr' ? '48' : '72'} hours
          </Text>
        </View>
      </View>
    </ThemedView>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 12,
    minHeight: 300,
    borderWidth: 1,
    borderColor: colors.border,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter',
    fontWeight: '600',
    color: colors.textOnCard,
  },
  debugToggle: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: colors.border,
  },
  debugToggleText: {
    fontSize: 12,
    color: colors.textOnCardSecondary,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.border,
    borderRadius: 8,
    padding: 2,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textOnCardSecondary,
  },
  activeTabText: {
    color: colors.primaryText,
  },
  debugContainer: {
    backgroundColor: colors.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: colors.textOnCard,
    lineHeight: 20,
    marginBottom: 20,
  },
  chartContainer: {
    flex: 1,
    minHeight: 180,
  },
  chartContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  yAxisContainer: {
    width: 30,
    height: 150,
    justifyContent: 'space-between',
    marginRight: 12,
  },
  yAxisLabel: {
    alignItems: 'flex-end',
  },
  yAxisText: {
    fontSize: 12,
    fontWeight: '400',
  },
  stackedBarContainer: {
    flex: 1,
    height: 150,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  stackedBar: {
    width: 60,
    height: 150,
    flexDirection: 'column-reverse', // Stack from bottom to top
    alignItems: 'stretch',
    borderRadius: 8,
    overflow: 'hidden',
  },
  stackedSegment: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 20,
  },
  segmentLabelContainer: {
    padding: 4,
  },
  segmentLabel: {
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  xAxisContainer: {
    alignItems: 'center',
    marginTop: 12,
  },
  xAxisText: {
    fontSize: 12,
    fontWeight: '400',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  errorText: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  noDataText: {
    fontSize: 14,
    color: colors.textOnCardSecondary,
    textAlign: 'center',
    paddingVertical: 20,
  },
}); 