import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useConfig } from '../contexts/ConfigContext';

interface WIPBannerWrapperProps {
  children: React.ReactNode;
}

/**
 * WIPBannerWrapper overlays a Work In Progress banner over its children if enabled in config.
 * Reads the showWIPBanners flag from app.defaults in config.
 */
const WIPBannerWrapper: React.FC<WIPBannerWrapperProps> = ({ children }) => {
  const { config } = useConfig();
  // Defensive: fallback to false if config is missing or malformed
  const showWIP = !!(config?.showWIPBanners);

  return (
    <View style={styles.container}>
      {children}
      {showWIP && (
        <View pointerEvents="none" style={styles.bannerOverlay}>
          <View style={styles.bannerBox}>
            <Text style={styles.bannerText}>🚧 WIP 🚧</Text>
          </View>
        </View>
      )}
    </View>
  );
};

// Styles for the overlay banner
const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  // Overlay covers the entire child area, centers the banner
  bannerOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    pointerEvents: 'none',
  },
  // Banner box is semi-transparent, rounded, and centers the text
  bannerBox: {
    backgroundColor: 'rgba(255, 204, 0, 0.2)', // yellow, semi-transparent
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    transform: [{ rotate: '-40deg' }],
  },
  bannerText: {
    color: '#222',
    fontWeight: 'bold',
    fontSize: 20,
    letterSpacing: 2,
    textAlign: 'center',
    // No background here, handled by bannerBox
  },
});

export default WIPBannerWrapper; 