import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import React, { useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import { LeaderboardEntry } from '../hooks/useLeaderboardQuery';
import { useSkiResortsQuery } from '../hooks/useSkiResortsQuery';
import useThreeDayForecast from '../hooks/useThreeDayForecast';
import { Resort } from '../types';
import { countryCodeToName } from '../utils/countryName';
import { ThemedText } from './ThemedText';
import ResortLogo from './ui/ResortLogo';

interface LeaderboardRowProps {
    entry: LeaderboardEntry;
    showLogos?: boolean;
    onPress?: (resort: Resort) => void;
    style?: ViewStyle;
}

/**
 * Renders a single row in the leaderboard. Handles logo, name, snowfall, and row press.
 * Future: Add more data fields and custom styling as needed.
 */
const LeaderboardRow: React.FC<LeaderboardRowProps> = ({ entry, showLogos = true, onPress, style }) => {
    const { colors } = useTheme();
    // State for lazy-loaded details
    const [countryName, setCountryName] = useState<string>('');
    const [geocode, setGeocode] = useState<string | null>(null);

    // Fetch all resorts (from real API or mock)
    const { data: allResorts } = useSkiResortsQuery();

    // Find the full resort object by ID
    useEffect(() => {
        if (!allResorts) return;
        const resort = allResorts.find(r => String(r.id) === String(entry.resortId));
        setCountryName(resort?.location?.country || 'Unknown');
        if (resort?.coordinates?.latitude && resort?.coordinates?.longitude) {
            setGeocode(`${resort.coordinates.latitude},${resort.coordinates.longitude}`);
        } else {
            setGeocode(null);
        }
    }, [allResorts, entry.resortId]);

    // Fetch weather data using real API
    const { data: weatherData, isLoading: weatherLoading } = { data: {}, isLoading: false };//useWeatherQuery(geocode || '');
    // Fetch 3-day forecast using real API
    const { data: forecastData, isLoading: forecastLoading } = useThreeDayForecast(geocode || '');

    const styles = createStyles(colors);

    if (!entry) {
        return (
            <View style={[styles.rowContainer, style]}>
                <ThemedText style={styles.errorText}>No data</ThemedText>
            </View>
        );
    }

    // TODO: Wire up real status if available
    const status: 'open' | 'closed' = 'open';

    const handlePress = () => {
        if (onPress) {
            const resort: Resort = {
                id: entry.resortId,
                name: entry.resortName || `Resort ${entry.resortId}`,
                description: `Details for ${entry.resortName || `Resort ${entry.resortId}`}. Snowfall: ${String(entry.snowfall)}"`,
                logo: entry.resortLogoUrl,
                coordinates: { latitude: 0, longitude: 0 },
            };
            onPress(resort);
        }
    };

    return (
        <TouchableOpacity onPress={handlePress} activeOpacity={0.7} disabled={!onPress}>
            <View style={[styles.rowContainer, style]}>
                {/* Status dot in upper right */}
                {/* <View style={[styles.statusDot, status === 'open' ? styles.statusDotOpen : styles.statusDotClosed]} /> */}
                {/* Left: Logo or initials */}
                <View style={styles.logoCol}>
                    <ResortLogo name={entry.resortName || `Resort ${entry.resortId}`} logoUrl={entry.resortLogoUrl} size={36} />
                </View>
                {/* Middle: Resort info */}
                <View style={styles.infoCol}>
                    {/* Top row: Name */}
                    <View style={styles.topRow}>
                        <ThemedText style={styles.resortName} numberOfLines={1}>
                            {entry.resortName || `Resort ID: ${entry.resortId}`}
                        </ThemedText>
                    </View>
                    {/* Bottom row: Details (country, temp) or loading */}
                    <View style={styles.bottomRow}>
                        {/* Country name always shown */}
                        <View style={styles.detailItem}>
                            <ThemedText style={styles.detailText}>
                                {countryCodeToName(countryName) || 'Unknown'}
                            </ThemedText>
                        </View>
                        {/* Future: Weather/forecast details can be added here */}
                        {/* {weatherLoading || !weatherData ? (
                            <ActivityIndicator size="small" color={colors.textMuted} style={{ marginLeft: 2 }} />
                        ) : (
                            <></>
                        )} */}
                    </View>
                </View>
                {/* Right: Snowfall */}
                <View style={styles.snowCol}>
                    <ThemedText style={styles.snowfall}>{Number(entry.snowfall).toFixed(2)}"</ThemedText>
                    {/* <ThemedText style={styles.snowfallLabel}>inches</ThemedText> */}
                </View>
            </View>
        </TouchableOpacity>
    );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 4,
        position: 'relative', // For status dot positioning
    },
    statusDot: {
        position: 'absolute',
        top: 6,
        right: 8,
        width: 12,
        height: 12,
        borderRadius: 6,
        zIndex: 2,
    },
    statusDotOpen: {
        backgroundColor: colors.success,
    },
    statusDotClosed: {
        backgroundColor: colors.error,
    },
    logoCol: {
        marginRight: 10,
    },
    infoCol: {
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'center',
        minWidth: 0,
    },
    topRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 0,
        minWidth: 0,
    },
    resortName: {
        fontFamily: 'Inter',
        fontSize: 14,
        fontStyle: 'normal',
        fontWeight: '600',
        color: colors.textOnCard,
        flexShrink: 1,
        minWidth: 0,
        lineHeight: 16,
    },
    bottomRow: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 0,
        minWidth: 0,
        width: '100%',
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 2,
        minWidth: 0,
    
    },
    detailText: {
        fontSize: 10,
        color: colors.textMuted,
        fontWeight: '500',
        fontFamily: 'Inter',
        alignItems: 'center',
        justifyContent: 'center',
        lineHeight: 16,
    },
    snowCol: {
        alignItems: 'flex-end',
        justifyContent: 'center',
        minWidth: 54,
        marginLeft: 10, // More space to avoid dot overlap
    },
    snowfall: {
        fontFamily: 'Inter',
        fontSize: 13,
        fontStyle: 'normal',
        fontWeight: '600',
        color: colors.textOnCard,
        flexShrink: 1,
        minWidth: 0,
    },
    snowfallLabel: {
        fontSize: 10,
        color: colors.textMuted,
        textAlign: 'right',
        marginTop: -2,
    },
    errorText: {
        color: colors.error,
        fontStyle: 'italic',
    },
    forecastRight: {
        marginLeft: 'auto',
        flexDirection: 'row',
        alignItems: 'center',
        minWidth: 0,
    },
});

export default LeaderboardRow; 