import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_rain_style_outline = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<foreignObject x=\"-17.1641\" y=\"-19.167\" width=\"50.332\" height=\"47.667\"><div xmlns=\"http://www.w3.org/1999/xhtml\" style=\"backdrop-filter:blur(10px);clip-path:url(#bgblur_0_47957_59480_clip_path);height:100%;width:100%\"></div></foreignObject><path data-figma-bg-blur-radius=\"20\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.98403 1.83301C6.68112 1.83301 5.59259 2.86456 5.48358 4.20282L5.45043 4.60974L5.0451 4.65862C4.37481 4.73947 3.83594 5.33081 3.83594 6.06902C3.83594 6.79265 4.35395 7.37547 5.00582 7.47408L5.22288 7.48903L10.1194 7.48902L10.1433 7.49133C10.2006 7.49684 10.2587 7.49967 10.3177 7.49967C11.3279 7.49967 12.1693 6.65497 12.1693 5.58487C12.1693 4.63897 11.5094 3.86596 10.6588 3.70241L10.3947 3.65162L10.2915 3.40327C9.90495 2.47314 9.01212 1.83301 7.98403 1.83301ZM4.537 3.74667C4.84538 2.09573 6.26234 0.833008 7.98403 0.833008C9.35064 0.833008 10.5268 1.63048 11.1065 2.78311C12.3049 3.13473 13.1693 4.26252 13.1693 5.58487C13.1693 7.18209 11.905 8.49967 10.3177 8.49967C10.2352 8.49967 10.1534 8.49608 10.0725 8.48902L5.18849 8.48903L4.93976 8.4719H4.92062L4.88668 8.46723C3.71857 8.30642 2.83594 7.28513 2.83594 6.06902C2.83594 4.97771 3.54689 4.04302 4.537 3.74667Z\" fill=\"#252422\"/>\n<path d=\"M5.87003 10.5338L5.45595 11.6714C5.34868 11.9662 5.50064 12.292 5.79535 12.3993C6.09007 12.5066 6.41595 12.3546 6.52322 12.0599L6.9373 10.9222C7.04457 10.6275 6.89261 10.3016 6.59789 10.1943C6.30318 10.0871 5.9773 10.239 5.87003 10.5338Z\" fill=\"#252422\"/>\n<path d=\"M9.49713 11.6102L8.62782 13.9986C8.52055 14.2933 8.67251 14.6192 8.96723 14.7265C9.26195 14.8337 9.58782 14.6818 9.69509 14.387L10.5644 11.9987C10.6717 11.7039 10.5197 11.3781 10.225 11.2708C9.93027 11.1635 9.6044 11.3155 9.49713 11.6102Z\" fill=\"#252422\"/>\n<defs>\n<clipPath id=\"bgblur_0_47957_59480_clip_path\" transform=\"translate(17.1641 19.167)\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.98403 1.83301C6.68112 1.83301 5.59259 2.86456 5.48358 4.20282L5.45043 4.60974L5.0451 4.65862C4.37481 4.73947 3.83594 5.33081 3.83594 6.06902C3.83594 6.79265 4.35395 7.37547 5.00582 7.47408L5.22288 7.48903L10.1194 7.48902L10.1433 7.49133C10.2006 7.49684 10.2587 7.49967 10.3177 7.49967C11.3279 7.49967 12.1693 6.65497 12.1693 5.58487C12.1693 4.63897 11.5094 3.86596 10.6588 3.70241L10.3947 3.65162L10.2915 3.40327C9.90495 2.47314 9.01212 1.83301 7.98403 1.83301ZM4.537 3.74667C4.84538 2.09573 6.26234 0.833008 7.98403 0.833008C9.35064 0.833008 10.5268 1.63048 11.1065 2.78311C12.3049 3.13473 13.1693 4.26252 13.1693 5.58487C13.1693 7.18209 11.905 8.49967 10.3177 8.49967C10.2352 8.49967 10.1534 8.49608 10.0725 8.48902L5.18849 8.48903L4.93976 8.4719H4.92062L4.88668 8.46723C3.71857 8.30642 2.83594 7.28513 2.83594 6.06902C2.83594 4.97771 3.54689 4.04302 4.537 3.74667Z\"/>\n</clipPath></defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_rain_style_outline;
