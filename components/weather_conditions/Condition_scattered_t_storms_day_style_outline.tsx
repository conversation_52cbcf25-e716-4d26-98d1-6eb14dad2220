import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_scattered_t_storms_day_style_outline = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_123280)\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M8.82602 5.27214C8.91959 4.11015 9.86048 3.18555 11.0198 3.18555C11.9326 3.18555 12.7125 3.75976 13.0469 4.57364L13.1709 4.87555L13.4782 4.93532C14.2211 5.07978 14.7736 5.75477 14.7736 6.55316C14.7736 7.45512 14.0679 8.19944 13.1814 8.19944C13.1333 8.19944 13.0858 8.19725 13.039 8.19298L8.45163 8.1894L8.44714 8.18941L8.44595 8.18941L8.44267 8.1894H8.36806L8.29116 8.17868C7.7261 8.09994 7.30078 7.59914 7.30078 7.00675C7.30078 6.40782 7.7356 5.90254 8.3098 5.83238L8.78559 5.77425L8.82602 5.27214ZM8.4499 7.78941L8.44344 7.7894L8.39579 7.7894L8.34637 7.78251C7.98819 7.7326 7.70078 7.40901 7.70078 7.00675C7.70078 6.59994 7.99457 6.27387 8.35831 6.22943L9.1581 6.13171L9.22473 5.30424C9.30272 4.33571 10.083 3.58555 11.0198 3.58555C11.7582 3.58555 12.3994 4.05015 12.6769 4.72562L12.8828 5.22703L13.4019 5.32796C13.9496 5.43447 14.3736 5.93878 14.3736 6.55316C14.3736 7.24871 13.8327 7.79944 13.1814 7.79944C13.1429 7.79944 13.105 7.79756 13.0679 7.79393L13.0312 7.79035L13.0309 7.79297L8.4499 7.78941Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M8.45093 7.98989L8.44672 7.9899L8.44267 7.98989H8.38192L8.31876 7.9806C7.85714 7.91627 7.50078 7.50456 7.50078 7.00724C7.50078 6.50436 7.86509 6.0887 8.33406 6.0314L8.97185 5.95347L9.02538 5.28868C9.11115 4.22342 9.97175 3.38604 11.0198 3.38604C11.8454 3.38604 12.556 3.90544 12.8619 4.65012L13.0269 5.05178L13.4401 5.13213C14.0854 5.25761 14.5736 5.84726 14.5736 6.55364C14.5736 7.3524 13.9503 7.99993 13.1814 7.99993C13.1364 7.99993 13.092 7.99773 13.0485 7.99347L8.45093 7.98989ZM8.41659 7.48989L8.38777 7.48587C8.18476 7.45758 8.00078 7.2669 8.00078 7.00724C8.00078 6.74451 8.1888 6.55286 8.3947 6.52771L9.43749 6.4003L9.52376 5.32881C9.59007 4.50536 10.2499 3.88604 11.0198 3.88604C11.6273 3.88604 12.1645 4.26843 12.3994 4.8401L12.6668 5.49112L13.3446 5.62293C13.7459 5.70098 14.0736 6.07728 14.0736 6.55364C14.0736 7.09438 13.6564 7.49993 13.1814 7.49993C13.1526 7.49993 13.1245 7.49852 13.0971 7.49584L13.073 7.49349L8.45132 7.48989L8.4497 7.4899L8.44801 7.4899L8.44462 7.48989L8.41659 7.48989Z\" fill=\"#252422\"/>\n<path d=\"M8.89054 8.94988L7.71822 11.3666C7.59769 11.6151 7.70141 11.9142 7.94988 12.0348C8.19834 12.1553 8.49747 12.0516 8.61799 11.8031L9.79031 9.38633C9.91084 9.13787 9.80712 8.83874 9.55865 8.71822C9.31019 8.59769 9.01106 8.70141 8.89054 8.94988Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.1796 8.42105C12.3613 8.52041 12.428 8.74827 12.3287 8.92998L11.1321 11.1182H13.4493L11.9415 14.0602C11.847 14.2445 11.621 14.3173 11.4367 14.2229C11.2524 14.1284 11.1796 13.9024 11.274 13.7181L12.2221 11.8682H9.86719L11.6706 8.57016C11.77 8.38844 11.9978 8.32168 12.1796 8.42105Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3.83203 7.99967C3.83203 5.69849 5.69751 3.83301 7.9987 3.83301C8.27484 3.83301 8.4987 4.05687 8.4987 4.33301C8.4987 4.60915 8.27484 4.83301 7.9987 4.83301C6.2498 4.83301 4.83203 6.25077 4.83203 7.99967C4.83203 9.30981 5.62776 10.4353 6.76428 10.9169C7.01854 11.0246 7.13731 11.3181 7.02956 11.5723C6.92182 11.8266 6.62836 11.9453 6.37411 11.8376C4.881 11.2049 3.83203 9.72535 3.83203 7.99967Z\" fill=\"#252422\"/>\n<path d=\"M7.49993 2.5V1.5C7.49993 1.22386 7.72379 1 7.99993 1C8.27607 1 8.49993 1.22386 8.49993 1.5V2.5C8.49993 2.77614 8.27607 3 7.99993 3C7.72379 3 7.49993 2.77614 7.49993 2.5Z\" fill=\"#252422\"/>\n<path d=\"M2.5 8.5H1.5C1.22386 8.5 1 8.27614 1 8C1 7.72386 1.22386 7.5 1.5 7.5L2.5 7.5C2.77614 7.5 3 7.72386 3 8C3 8.27614 2.77614 8.5 2.5 8.5Z\" fill=\"#252422\"/>\n<path d=\"M3.04969 3.75746L3.7568 4.46456C3.95206 4.65982 4.26864 4.65982 4.4639 4.46456C4.65917 4.2693 4.65917 3.95272 4.4639 3.75746L3.7568 3.05035C3.56154 2.85509 3.24495 2.85509 3.04969 3.05035C2.85443 3.24561 2.85443 3.56219 3.04969 3.75746Z\" fill=\"#252422\"/>\n<path d=\"M4.46383 12.2427L3.75672 12.9498C3.56146 13.1451 3.24488 13.1451 3.04962 12.9498C2.85435 12.7546 2.85435 12.438 3.04962 12.2427L3.75672 11.5356C3.95198 11.3403 4.26857 11.3403 4.46383 11.5356C4.65909 11.7309 4.65909 12.0475 4.46383 12.2427Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_123280\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_scattered_t_storms_day_style_outline;
