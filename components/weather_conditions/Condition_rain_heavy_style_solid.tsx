import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_rain_heavy_style_solid = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_120666)\">\n<path d=\"M7.64739 9.46977L6.93636 11.4233C6.84191 11.6828 6.97571 11.9697 7.23521 12.0642C7.49471 12.1586 7.78165 12.0248 7.8761 11.7653L8.58712 9.8118C8.68157 9.5523 8.54777 9.26537 8.28827 9.17092C8.02877 9.07647 7.74184 9.21026 7.64739 9.46977Z\" fill=\"#252422\"/>\n<path d=\"M4.87953 10.8157L4.07717 13.0202C3.98272 13.2797 4.11652 13.5666 4.37602 13.6611C4.63552 13.7555 4.92245 13.6217 5.0169 13.3622L5.81926 11.1578C5.91371 10.8983 5.77991 10.6113 5.52041 10.5169C5.26091 10.4224 4.97398 10.5562 4.87953 10.8157Z\" fill=\"#252422\"/>\n<path d=\"M10.1763 10.9172L9.41083 13.0202C9.31637 13.2797 9.45017 13.5666 9.70967 13.6611C9.96918 13.7555 10.2561 13.6217 10.3506 13.3622L11.116 11.2592C11.2104 10.9997 11.0766 10.7128 10.8171 10.6184C10.5576 10.5239 10.2707 10.6577 10.1763 10.9172Z\" fill=\"#252422\"/>\n<foreignObject x=\"-17.1641\" y=\"-19.166\" width=\"50.332\" height=\"47.667\"><div xmlns=\"http://www.w3.org/1999/xhtml\" style=\"backdrop-filter:blur(10px);clip-path:url(#bgblur_1_19_120666_clip_path);height:100%;width:100%\"></div></foreignObject><path data-figma-bg-blur-radius=\"20\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.537 3.74765C4.84538 2.09671 6.26234 0.833984 7.98403 0.833984C9.35064 0.833984 10.5268 1.63145 11.1065 2.78409C12.3049 3.13571 13.1693 4.26349 13.1693 5.58585C13.1693 7.18306 11.905 8.50065 10.3177 8.50065C10.2352 8.50065 10.1534 8.49706 10.0725 8.49L5.18849 8.49L4.93976 8.47288H4.92062L4.88668 8.46821C3.71857 8.3074 2.83594 7.2861 2.83594 6.07C2.83594 4.97869 3.54689 4.044 4.537 3.74765Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"bgblur_1_19_120666_clip_path\" transform=\"translate(17.1641 19.166)\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.537 3.74765C4.84538 2.09671 6.26234 0.833984 7.98403 0.833984C9.35064 0.833984 10.5268 1.63145 11.1065 2.78409C12.3049 3.13571 13.1693 4.26349 13.1693 5.58585C13.1693 7.18306 11.905 8.50065 10.3177 8.50065C10.2352 8.50065 10.1534 8.49706 10.0725 8.49L5.18849 8.49L4.93976 8.47288H4.92062L4.88668 8.46821C3.71857 8.3074 2.83594 7.2861 2.83594 6.07C2.83594 4.97869 3.54689 4.044 4.537 3.74765Z\"/>\n</clipPath><clipPath id=\"clip0_19_120666\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_rain_heavy_style_solid;
