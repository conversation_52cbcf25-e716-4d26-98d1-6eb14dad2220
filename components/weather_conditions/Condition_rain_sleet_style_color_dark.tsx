import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_rain_sleet_style_color_dark = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_123893)\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.42 3.68252C4.73833 1.9733 6.20099 0.666016 7.97822 0.666016C9.38891 0.666016 10.603 1.49163 11.2014 2.68495C12.4384 3.04898 13.3307 4.21657 13.3307 5.58559C13.3307 7.23918 12.0257 8.60327 10.3872 8.60327C10.302 8.60327 10.2176 8.59955 10.1341 8.59224L5.0925 8.59225L4.83575 8.57452H4.81599L4.78096 8.56968C3.57517 8.40319 2.66406 7.34586 2.66406 6.08683C2.66406 4.957 3.39795 3.98933 4.42 3.68252Z\" fill=\"#CAD9E2\"/>\n<path d=\"M7.90618 9.51273L7.34126 10.9102C7.23526 11.1725 7.36784 11.4703 7.63363 11.5671C7.88655 11.6591 8.16693 11.5343 8.2678 11.2848L8.83273 9.88728C8.93873 9.62505 8.80615 9.32719 8.54036 9.23045C8.28744 9.13839 8.00705 9.2632 7.90618 9.51273Z\" fill=\"#93CCD3\"/>\n<path d=\"M10.01 10.2018L9.17378 12.4994C9.07933 12.7588 9.21313 13.0458 9.47262 13.1402C9.73211 13.2347 10.019 13.1009 10.1135 12.8414L10.9497 10.5439C11.0442 10.2844 10.9104 9.99744 10.6509 9.903C10.3914 9.80855 10.1045 9.94234 10.01 10.2018Z\" fill=\"#93CCD3\"/>\n<path d=\"M7.83203 14H3.83203C3.55589 14 3.33203 14.2239 3.33203 14.5C3.33203 14.7761 3.55589 15 3.83203 15H7.83203C8.10817 15 8.33203 14.7761 8.33203 14.5C8.33203 14.2239 8.10817 14 7.83203 14Z\" fill=\"#CAD9E2\"/>\n<path d=\"M5.33203 12.5C5.33203 12.7761 5.55589 13 5.83203 13C6.10817 13 6.33203 12.7761 6.33203 12.5C6.33203 12.2239 6.10817 12 5.83203 12C5.55589 12 5.33203 12.2239 5.33203 12.5Z\" fill=\"#CAD9E2\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_123893\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_rain_sleet_style_color_dark;
