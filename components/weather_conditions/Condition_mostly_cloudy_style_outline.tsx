import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_mostly_cloudy_style_outline = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_47959_81860)\">\n<path d=\"M11.6626 12.1493L12.3559 12.8426C12.5473 13.034 12.8577 13.034 13.0492 12.8426C13.2406 12.6511 13.2406 12.3407 13.0492 12.1493L12.3559 11.456C12.1644 11.2646 11.8541 11.2646 11.6626 11.456C11.4712 11.6474 11.4712 11.9578 11.6626 12.1493Z\" fill=\"#252422\"/>\n<path d=\"M7.70578 13.3821V14.3626C7.70578 14.6333 7.92526 14.8528 8.196 14.8528C8.46674 14.8528 8.68622 14.6333 8.68622 14.3626L8.68622 13.3821C8.68622 13.1114 8.46674 12.8919 8.196 12.8919C7.92526 12.8919 7.70578 13.1114 7.70578 13.3821Z\" fill=\"#252422\"/>\n<path d=\"M4.72936 12.1494L4.03608 12.8427C3.84464 13.0342 3.53425 13.0342 3.3428 12.8427C3.15136 12.6513 3.15136 12.3409 3.3428 12.1494L4.03608 11.4562C4.22752 11.2647 4.53792 11.2647 4.72936 11.4562C4.9208 11.6476 4.9208 11.958 4.72936 12.1494Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.98891 8.18888C5.25573 8.11773 5.52971 8.27635 5.60086 8.54317C5.65461 8.74475 5.76 8.99578 5.88859 9.23575C6.01883 9.47881 6.15351 9.6744 6.24974 9.77822C6.70169 10.2658 7.39329 10.5872 8.18094 10.5872C8.96859 10.5872 9.66019 10.2658 10.1121 9.77822C10.2147 9.6676 10.3725 9.45018 10.5038 9.20807C10.6449 8.94784 10.7027 8.7545 10.7027 8.67193C10.7027 8.39579 10.9266 8.17193 11.2027 8.17193C11.4789 8.17193 11.7027 8.39579 11.7027 8.67193C11.7027 9.02131 11.5353 9.40364 11.3828 9.6848C11.2205 9.98409 11.0163 10.2738 10.8456 10.458C10.1996 11.1549 9.2399 11.5872 8.18094 11.5872C7.12198 11.5872 6.16229 11.1549 5.51633 10.458C5.33927 10.267 5.15793 9.98942 5.00717 9.70807C4.85475 9.42364 4.71414 9.09904 4.63462 8.80083C4.56347 8.53401 4.7221 8.26003 4.98891 8.18888Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M10.4362 7.8929L10.4831 7.89685C10.5355 7.90127 10.5889 7.90355 10.6432 7.90355C11.5617 7.90355 12.3503 7.22807 12.3503 6.34659C12.3503 5.56885 11.7315 4.94605 10.9558 4.81553L10.438 4.7284L10.2356 4.30231C9.85307 3.49687 8.97523 2.95257 7.97617 2.95257C6.71083 2.95257 5.6343 3.83159 5.52612 4.99359L5.46112 5.69173L4.66636 5.77561C4.1117 5.83415 3.64445 6.26829 3.64445 6.83074C3.64445 7.37679 4.08526 7.80242 4.61858 7.87989L4.83462 7.8929L10.4362 7.8929ZM4.80091 8.75074L4.51666 8.73362H4.51428C3.46966 8.60779 2.66406 7.80379 2.66406 6.83074C2.66406 5.84694 3.48755 5.03596 4.54897 4.92394C4.69649 3.3394 6.17456 2.09473 7.97617 2.09473C9.39691 2.09473 10.6164 2.86877 11.1409 3.97312C12.3874 4.18285 13.3307 5.16573 13.3307 6.34659C13.3307 7.68025 12.1275 8.76139 10.6432 8.76139C10.5575 8.76139 10.4728 8.75779 10.3891 8.75074L4.80091 8.75074Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_47959_81860\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_mostly_cloudy_style_outline;
