import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_scattered_t_storms_day_style_solid = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_123240)\">\n<path d=\"M7.49993 2.5V1.5C7.49993 1.22386 7.72379 1 7.99993 1C8.27607 1 8.49993 1.22386 8.49993 1.5V2.5C8.49993 2.77614 8.27607 3 7.99993 3C7.72379 3 7.49993 2.77614 7.49993 2.5Z\" fill=\"#252422\"/>\n<path d=\"M2.5 8.5H1.5C1.22386 8.5 1 8.27614 1 8C1 7.72386 1.22386 7.5 1.5 7.5L2.5 7.5C2.77614 7.5 3 7.72386 3 8C3 8.27614 2.77614 8.5 2.5 8.5Z\" fill=\"#252422\"/>\n<path d=\"M3.04969 3.75746L3.7568 4.46456C3.95206 4.65982 4.26864 4.65982 4.4639 4.46456C4.65917 4.2693 4.65917 3.95272 4.4639 3.75746L3.7568 3.05035C3.56154 2.85509 3.24495 2.85509 3.04969 3.05035C2.85443 3.24561 2.85443 3.56219 3.04969 3.75746Z\" fill=\"#252422\"/>\n<path d=\"M4.46383 12.2427L3.75672 12.9498C3.56146 13.1451 3.24488 13.1451 3.04962 12.9498C2.85435 12.7546 2.85435 12.438 3.04962 12.2427L3.75672 11.5356C3.95198 11.3403 4.26857 11.3403 4.46383 11.5356C4.65909 11.7309 4.65909 12.0475 4.46383 12.2427Z\" fill=\"#252422\"/>\n<path d=\"M6.75134 10.7699C6.655 10.9685 6.59547 11.1742 6.57017 11.3775C5.25536 10.8203 4.33301 9.51783 4.33301 7.99992C4.33301 5.97487 5.97463 4.33325 7.99967 4.33325C8.09681 4.33325 8.19306 4.33703 8.28829 4.34444C8.17715 4.61614 8.10344 4.90817 8.07385 5.21365C7.18286 5.3462 6.5 6.13758 6.5 7.09196C6.5 7.90225 6.99228 8.59517 7.6864 8.86697C7.65032 8.92505 7.61682 8.98573 7.58618 9.04889L6.75134 10.7699Z\" fill=\"#252422\"/>\n<path d=\"M8.44594 7.99059L8.44189 7.99057H8.38114L8.31798 7.98177C7.85636 7.91744 7.5 7.50525 7.5 7.00792C7.5 6.50505 7.8643 6.08938 8.33328 6.03208L8.97107 5.95415L9.02459 5.28936C9.11037 4.2241 9.97097 3.38672 11.019 3.38672C11.8446 3.38672 12.5552 3.90613 12.8611 4.6508L13.0261 5.05246L13.4393 5.13281C14.0846 5.2583 14.5729 5.84795 14.5729 6.55433C14.5729 7.35309 13.9495 8.00061 13.1806 8.00061C13.1356 8.00061 13.0912 7.99841 13.0477 7.99416L8.45015 7.99058L8.44998 7.99058L8.44594 7.99059Z\" fill=\"#252422\"/>\n<path d=\"M8.89054 8.94988L7.71822 11.3666C7.59769 11.6151 7.70141 11.9142 7.94988 12.0348C8.19834 12.1553 8.49747 12.0516 8.61799 11.8031L9.79031 9.38633C9.91084 9.13787 9.80712 8.83874 9.55865 8.71822C9.31019 8.59769 9.01106 8.70141 8.89054 8.94988Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12.1796 8.42105C12.3613 8.52041 12.428 8.74827 12.3287 8.92998L11.1321 11.1182H13.4493L11.9415 14.0602C11.847 14.2445 11.621 14.3173 11.4367 14.2229C11.2524 14.1284 11.1796 13.9024 11.274 13.7181L12.2221 11.8682H9.86719L11.6706 8.57016C11.77 8.38844 11.9978 8.32168 12.1796 8.42105Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_123240\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_scattered_t_storms_day_style_solid;
