import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_partly_cloudy_day_style_color_light = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_120161)\">\n<path d=\"M7.74212 2.5V1.5C7.74212 1.22386 7.96598 1 8.24212 1C8.51826 1 8.74212 1.22386 8.74212 1.5V2.5C8.74212 2.77614 8.51826 3 8.24212 3C7.96598 3 7.74212 2.77614 7.74212 2.5Z\" fill=\"#FFB900\"/>\n<path d=\"M7.74212 14.5V13.5C7.74212 13.2239 7.96598 13 8.24212 13C8.51826 13 8.74212 13.2239 8.74212 13.5V14.5C8.74212 14.7761 8.51826 15 8.24212 15C7.96598 15 7.74212 14.7761 7.74212 14.5Z\" fill=\"#FFB900\"/>\n<path d=\"M1.74219 8.5H2.74219C3.01833 8.5 3.24219 8.27614 3.24219 8C3.24219 7.72386 3.01833 7.5 2.74219 7.5L1.74219 7.5C1.46605 7.5 1.24219 7.72386 1.24219 8C1.24219 8.27614 1.46605 8.5 1.74219 8.5Z\" fill=\"#FFB900\"/>\n<path d=\"M12.485 12.9498L11.7779 12.2426C11.5826 12.0474 11.5826 11.7308 11.7779 11.5355C11.9731 11.3403 12.2897 11.3403 12.485 11.5355L13.1921 12.2426C13.3874 12.4379 13.3874 12.7545 13.1921 12.9497C12.9968 13.145 12.6803 13.145 12.485 12.9498Z\" fill=\"#FFB900\"/>\n<path d=\"M3.2922 3.75737L3.99931 4.46448C4.19457 4.65974 4.51116 4.65974 4.70642 4.46448C4.90168 4.26922 4.90168 3.95264 4.70642 3.75737L3.99931 3.05027C3.80405 2.855 3.48747 2.855 3.2922 3.05027C3.09694 3.24553 3.09694 3.56211 3.2922 3.75737Z\" fill=\"#FFB900\"/>\n<path d=\"M4.70634 12.2427L3.99924 12.9498C3.80397 13.1451 3.48739 13.1451 3.29213 12.9498C3.09687 12.7546 3.09687 12.438 3.29213 12.2427L3.99924 11.5356C4.1945 11.3403 4.51108 11.3403 4.70634 11.5356C4.9016 11.7309 4.9016 12.0475 4.70634 12.2427Z\" fill=\"#FFB900\"/>\n<path d=\"M7.89672 4.34937C6.03369 4.52346 4.57552 6.09143 4.57552 7.99997C4.57552 10.025 6.21714 11.6666 8.24219 11.6666C9.82271 11.6666 11.1697 10.6666 11.6849 9.2648L7.71508 9.26182L7.71245 9.26183L7.70799 9.26184L7.70621 9.26182H7.58445L7.46038 9.24517C6.54778 9.12275 5.84578 8.34078 5.84578 7.39675C5.84578 6.44224 6.56341 5.65358 7.49052 5.54454L7.59222 5.53258L7.60076 5.43053C7.63295 5.04563 7.73597 4.68089 7.89672 4.34937Z\" fill=\"#FFB900\"/>\n<path d=\"M7.71217 8.48427L7.70755 8.48423H7.63772L7.56513 8.47449C7.03458 8.40332 6.625 7.94728 6.625 7.39705C6.625 6.84068 7.04371 6.38079 7.58271 6.3174L8.31574 6.23118L8.37727 5.49567C8.47585 4.31709 9.46497 3.39062 10.6695 3.39062C11.6184 3.39062 12.4351 3.96529 12.7867 4.78918L12.9763 5.23357L13.4512 5.32247C14.1929 5.4613 14.7541 6.11367 14.7541 6.8952C14.7541 7.77893 14.0377 8.49534 13.1539 8.49534C13.1022 8.49534 13.0512 8.49291 13.0011 8.4882L7.71697 8.48426L7.71217 8.48427Z\" fill=\"#A1B6C3\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_120161\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_partly_cloudy_day_style_color_light;
