import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_clear_night_style_color_dark = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_120742)\">\n<path d=\"M7.49013 1.5C7.46086 1.50005 7.43165 1.50266 7.40283 1.5078C5.81071 1.79059 4.37954 2.65251 3.38498 3.92753C2.39042 5.20254 1.90282 6.80048 2.0161 8.41355C2.12938 10.0266 2.83552 11.5407 3.99852 12.6642C5.16152 13.7877 6.69911 14.4412 8.31513 14.4986C8.39718 14.5016 8.47923 14.4986 8.56058 14.4986C9.60999 14.4992 10.6441 14.2471 11.5755 13.7636C12.5069 13.2801 13.3082 12.5794 13.9116 11.7208C13.9605 11.6469 13.9891 11.5613 13.9944 11.4728C13.9997 11.3842 13.9816 11.2958 13.9419 11.2165C13.9022 11.1372 13.8423 11.0698 13.7682 11.021C13.6941 10.9722 13.6085 10.9438 13.5199 10.9387C12.521 10.851 11.5556 10.5349 10.6984 10.0146C9.84118 9.49439 9.11506 8.784 8.57617 7.93837C8.03728 7.09274 7.70006 6.13452 7.59057 5.13777C7.48108 4.14102 7.60226 3.13245 7.94473 2.19C7.97387 2.1146 7.98457 2.03332 7.97594 1.95295C7.96731 1.87257 7.93961 1.79542 7.89514 1.72792C7.85066 1.66042 7.79071 1.60452 7.72026 1.56487C7.64981 1.52523 7.57091 1.50299 7.49013 1.5Z\" fill=\"#A1B6C3\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_120742\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_clear_night_style_color_dark;
