import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_snow_showers_style_color_light = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_47959_81852)\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.42 4.21337C4.73833 2.58185 6.20099 1.33398 7.97822 1.33398C9.38891 1.33398 10.603 2.12207 11.2014 3.26115C12.4384 3.60863 13.3307 4.72315 13.3307 6.02994C13.3307 7.60837 12.0257 8.91046 10.3872 8.91046C10.302 8.91046 10.2176 8.90691 10.1341 8.89993L5.0925 8.89993L4.83575 8.88301H4.81599L4.78096 8.87839C3.57517 8.71947 2.66406 7.7102 2.66406 6.5084C2.66406 5.42993 3.39795 4.50624 4.42 4.21337Z\" fill=\"#A1B6C3\"/>\n<path d=\"M7.88772 10C8.0258 10 8.13772 10.1119 8.13772 10.25V10.4016C8.23265 10.3496 8.35419 10.3638 8.43459 10.4442C8.53222 10.5418 8.53222 10.7001 8.43459 10.7977L8.13772 11.0946V13.1865L8.43909 13.5035C8.53422 13.6036 8.53022 13.7618 8.43015 13.857C8.34933 13.9338 8.23055 13.946 8.13772 13.8951V14.1019C8.13772 14.2399 8.0258 14.3519 7.88772 14.3519C7.74965 14.3519 7.63772 14.2399 7.63772 14.1019V13.8895C7.54243 13.9475 7.41645 13.9361 7.33303 13.8545C7.23433 13.758 7.23257 13.5997 7.32912 13.501L7.63772 13.1855V11.0938L7.33106 10.7871C7.23343 10.6895 7.23344 10.5312 7.33107 10.4335C7.41427 10.3503 7.54151 10.338 7.63772 10.3966V10.25C7.63772 10.1119 7.74965 10 7.88772 10Z\" fill=\"#A1B6C3\"/>\n<path d=\"M9.73994 11.0867C9.80803 11.2068 9.76675 11.3609 9.64773 11.4309L9.46544 11.5381C9.50181 11.5949 9.51536 11.6666 9.49713 11.7377C9.46259 11.8725 9.32702 11.9543 9.19432 11.9204L8.93022 11.8528L7.13811 12.9066L6.98506 13.2732C6.93146 13.4016 6.7857 13.4624 6.65949 13.4091C6.59456 13.3817 6.54745 13.3294 6.52432 13.2676L6.37258 13.3568C6.25356 13.4268 6.10188 13.3861 6.03379 13.266C5.9657 13.1459 6.00699 12.9918 6.12601 12.9218L6.3007 12.8191C6.26785 12.77 6.25202 12.7089 6.26042 12.6452C6.27861 12.5073 6.40353 12.4095 6.53944 12.4269L6.89128 12.4718L8.68321 11.4181L8.76774 11.1105C8.80462 10.9763 8.94157 10.897 9.07364 10.9333C9.15445 10.9555 9.2147 11.0159 9.24085 11.0902L9.40116 10.9959C9.52018 10.9259 9.67186 10.9665 9.73994 11.0867Z\" fill=\"#A1B6C3\"/>\n<path d=\"M9.73897 13.2654C9.67088 13.3855 9.5192 13.4261 9.40018 13.3562L9.24009 13.262C9.21389 13.3362 9.15368 13.3964 9.07296 13.4186C8.9409 13.455 8.80394 13.3756 8.76707 13.2414L8.68264 12.9342L6.89089 11.8806L6.53882 11.9255C6.40292 11.9429 6.278 11.8451 6.25981 11.7072C6.2514 11.6435 6.26726 11.5823 6.30016 11.5332L6.12503 11.4302C6.00601 11.3602 5.96473 11.2061 6.03281 11.086C6.1009 10.9659 6.25258 10.9253 6.3716 10.9953L6.52373 11.0847C6.54688 11.023 6.59397 10.9707 6.65884 10.9433C6.78505 10.89 6.93081 10.9508 6.98441 11.0792L7.13733 11.4455L8.92921 12.4992L9.19369 12.4316C9.32639 12.3977 9.46196 12.4794 9.49649 12.6142C9.51473 12.6855 9.50112 12.7572 9.46466 12.8141L9.64675 12.9212C9.76577 12.9912 9.80706 13.1453 9.73897 13.2654Z\" fill=\"#A1B6C3\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_47959_81852\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_snow_showers_style_color_light;
