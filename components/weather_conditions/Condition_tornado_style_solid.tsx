import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_tornado_style_solid = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3.63376 3.15086C3.63376 2.88309 3.85083 2.66602 4.11861 2.66602H12.8459C13.1137 2.66602 13.3307 2.88309 13.3307 3.15086C13.3307 3.41864 13.1137 3.63571 12.8459 3.63571H4.11861C3.85083 3.63571 3.63376 3.41864 3.63376 3.15086Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2.66406 5.09026C2.66406 4.82248 2.88114 4.60541 3.14891 4.60541L11.3913 4.60541C11.6591 4.60541 11.8762 4.82248 11.8762 5.09026C11.8762 5.35803 11.6591 5.57511 11.3913 5.57511L3.14891 5.57511C2.88114 5.57511 2.66406 5.35803 2.66406 5.09026Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2.66406 8.96905C2.66406 8.70127 2.88114 8.4842 3.14891 8.4842L8.48224 8.4842C8.75002 8.4842 8.96709 8.70127 8.96709 8.96905C8.96709 9.23682 8.75002 9.45389 8.48224 9.45389L3.14891 9.45389C2.88114 9.45389 2.66406 9.23682 2.66406 8.96905Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M3.14891 7.02965C3.14891 6.76188 3.36599 6.5448 3.63376 6.5448L9.45194 6.5448C9.71972 6.5448 9.93679 6.76188 9.93679 7.02965C9.93679 7.29743 9.71972 7.5145 9.45194 7.5145L3.63376 7.5145C3.36599 7.5145 3.14891 7.29743 3.14891 7.02965Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.60346 10.9084C4.60346 10.6407 4.82053 10.4236 5.0883 10.4236H7.9974C8.26517 10.4236 8.48224 10.6407 8.48224 10.9084C8.48224 11.1762 8.26517 11.3933 7.9974 11.3933H5.0883C4.82053 11.3933 4.60346 11.1762 4.60346 10.9084Z\" fill=\"#252422\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.54285 12.8478C6.54285 12.5801 6.75992 12.363 7.0277 12.363H8.48224C8.75002 12.363 8.96709 12.5801 8.96709 12.8478C8.96709 13.1156 8.75002 13.3327 8.48224 13.3327H7.0277C6.75992 13.3327 6.54285 13.1156 6.54285 12.8478Z\" fill=\"#252422\"/>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_tornado_style_solid;
