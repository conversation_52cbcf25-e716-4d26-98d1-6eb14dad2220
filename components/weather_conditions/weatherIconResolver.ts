import React from 'react';
import { Text } from 'react-native';
import iconCodeToComponentMap from './iconCodeToComponentMap.json';

// Map of component names to their React components
const componentMap: Record<string, React.FC<any>> = {
  Condition_breezy_style_color_light: require('./Condition_breezy_style_color_light').default,
  Condition_clear_night_style_color_light: require('./Condition_clear_night_style_color_light').default,
  Condition_cloudy_style_color_light: require('./Condition_cloudy_style_color_light').default,
  Condition_foggy_style_color_light: require('./Condition_foggy_style_color_light').default,
  Condition_mostly_cloudy_style_color_light: require('./Condition_mostly_cloudy_style_color_light').default,
  Condition_partly_cloudy_day_style_color_light: require('./Condition_partly_cloudy_day_style_color_light').default,
  Condition_partly_cloudy_night_style_color_light: require('./Condition_partly_cloudy_night_style_color_light').default,
  Condition_rain_heavy_style_color_light: require('./Condition_rain_heavy_style_color_light').default,
  Condition_rain_style_color_light: require('./Condition_rain_style_color_light').default,
  Condition_snow_style_color_light: require('./Condition_snow_style_color_light').default,
  Condition_sunny_day_style_color_light: require('./Condition_sunny_day_style_color_light').default,
  Condition_thunderstorms_style_color_light: require('./Condition_thunderstorms_style_color_light').default,
  Condition_heavy_snow_style_color_light: require('./Condition_heavy_snow_style_color_light').default,
  // Added missing components for full coverage:
  Condition_tornado_style_color_light: require('./Condition_tornado_style_color_light').default,
  Condition_tropical_storm_style_color_light: require('./Condition_tropical_storm_style_color_light').default,
  Condition_hurricane_style_color_light: require('./Condition_hurricane_style_color_light').default,
  Condition_rain_snow_wintery_mix_style_color_light: require('./Condition_rain_snow_wintery_mix_style_color_light').default,
  Condition_rain_sleet_style_color_light: require('./Condition_rain_sleet_style_color_light').default,
  Condition_snow_showers_style_color_light: require('./Condition_snow_showers_style_color_light').default,
  Condition_haze_style_color_light: require('./Condition_haze_style_color_light').default,
  Condition_smoke_style_color_light: require('./Condition_smoke_style_color_light').default,
  // TODO: Add more as needed
};

// Fallback icon component for unknown codes or names
const FallbackIcon: () => React.ReactElement = () => {
  return React.createElement(Text, { style: { fontSize: 28, marginBottom: 4 } }, '❓');
};

/**
 * Returns the correct weather icon React component for a given icon code and time of day.
 * Falls back to a question mark emoji if the code or component is unmapped.
 * @param iconCode - The weather icon code (number)
 * @param timeOfDay - 'day' or 'night'
 * @returns A React component for the icon
 */
export function getWeatherIconComponent(
  iconCode: number,
  timeOfDay: 'day' | 'night' = 'day',
): () => React.ReactElement {
  const iconSet = (iconCodeToComponentMap as any)[iconCode];
  if (!iconSet) {
    // Unknown icon code
    return FallbackIcon;
  }
  const componentName = iconSet[timeOfDay] || iconSet['day'];
  const IconComponent = componentMap[componentName];
  if (!IconComponent) {
    // Unknown component name
    return FallbackIcon;
  }
  return IconComponent as () => React.ReactElement;
}

// TODO: Consider auto-generating the componentMap for all available icons to avoid manual updates. 