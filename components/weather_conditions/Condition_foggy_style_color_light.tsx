import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_foggy_style_color_light = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_122785)\">\n<path d=\"M12.4797 11.2206C13.8076 10.8545 14.7828 9.6381 14.7828 8.19389C14.7828 6.65863 13.6807 5.38078 12.2245 5.1083C11.6119 3.67271 10.1874 2.6665 8.52789 2.6665C6.42346 2.6665 4.69696 4.28458 4.52464 6.34448C3.73051 6.43777 3.0504 6.90385 2.66406 7.56299H8.51899C9.43068 7.56299 9.95891 8.35869 9.95891 9.06299C9.95891 9.26109 9.91711 9.46643 9.83675 9.65926H11.0411C11.9528 9.65926 12.481 10.455 12.481 11.1593C12.481 11.1796 12.4806 11.2001 12.4797 11.2206Z\" fill=\"#A1B6C3\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M1.33594 9.06299C1.33594 8.78685 1.5598 8.56299 1.83594 8.56299H8.46116C8.7373 8.56299 8.96116 8.78685 8.96116 9.06299C8.96116 9.33913 8.7373 9.56299 8.46116 9.56299H1.83594C1.5598 9.56299 1.33594 9.33913 1.33594 9.06299ZM3.84533 11.1592C3.84533 10.8831 4.06919 10.6592 4.34533 10.6592H10.9706C11.2467 10.6592 11.4706 10.8831 11.4706 11.1592C11.4706 11.4354 11.2467 11.6592 10.9706 11.6592H4.34533C4.06919 11.6592 3.84533 11.4354 3.84533 11.1592ZM1.33594 13.3521C1.33594 13.0759 1.5598 12.8521 1.83594 12.8521H8.46115C8.7373 12.8521 8.96115 13.0759 8.96115 13.3521C8.96115 13.6282 8.7373 13.8521 8.46115 13.8521H1.83594C1.5598 13.8521 1.33594 13.6282 1.33594 13.3521Z\" fill=\"#A1B6C3\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_122785\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_foggy_style_color_light;
