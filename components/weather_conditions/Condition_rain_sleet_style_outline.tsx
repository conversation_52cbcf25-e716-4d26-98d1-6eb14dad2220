import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_rain_sleet_style_outline = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_123929)\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.97822 1.70229C6.63328 1.70229 5.50964 2.77024 5.39711 4.15574L5.36289 4.57702L4.94449 4.62763C4.25257 4.71133 3.69632 5.32354 3.69632 6.08781C3.69632 6.83698 4.23104 7.44036 4.90394 7.54246L5.128 7.55793L10.1824 7.55793L10.2071 7.56031C10.2663 7.56602 10.3263 7.56895 10.3872 7.56895C11.4299 7.56895 12.2985 6.69444 12.2985 5.58657C12.2985 4.60729 11.6173 3.80699 10.7393 3.63766L10.4666 3.58509L10.3601 3.32797C9.96111 2.36501 9.03948 1.70229 7.97822 1.70229ZM4.42 3.68349C4.73833 1.97428 6.20099 0.666992 7.97822 0.666992C9.38891 0.666992 10.603 1.49261 11.2014 2.68593C12.4384 3.04995 13.3307 4.21754 13.3307 5.58657C13.3307 7.24016 12.0257 8.60425 10.3872 8.60425C10.302 8.60425 10.2176 8.60053 10.1341 8.59322L5.0925 8.59322L4.83575 8.57549H4.81599L4.78096 8.57066C3.57517 8.40417 2.66406 7.34683 2.66406 6.08781C2.66406 4.95798 3.39795 3.9903 4.42 3.68349Z\" fill=\"#252422\"/>\n<path d=\"M7.90618 9.51273L7.34126 10.9102C7.23526 11.1725 7.36784 11.4703 7.63363 11.5671C7.88655 11.6591 8.16693 11.5343 8.2678 11.2848L8.83273 9.88728C8.93873 9.62505 8.80615 9.32719 8.54036 9.23045C8.28744 9.13839 8.00705 9.2632 7.90618 9.51273Z\" fill=\"#252422\"/>\n<path d=\"M10.01 10.2018L9.17378 12.4994C9.07933 12.7588 9.21313 13.0458 9.47262 13.1402C9.73211 13.2347 10.019 13.1009 10.1135 12.8414L10.9497 10.5439C11.0442 10.2844 10.9104 9.99744 10.6509 9.903C10.3914 9.80855 10.1045 9.94234 10.01 10.2018Z\" fill=\"#252422\"/>\n<path d=\"M7.83203 14H3.83203C3.55589 14 3.33203 14.2239 3.33203 14.5C3.33203 14.7761 3.55589 15 3.83203 15H7.83203C8.10817 15 8.33203 14.7761 8.33203 14.5C8.33203 14.2239 8.10817 14 7.83203 14Z\" fill=\"#252422\"/>\n<path d=\"M5.33203 12.5C5.33203 12.7761 5.55589 13 5.83203 13C6.10817 13 6.33203 12.7761 6.33203 12.5C6.33203 12.2239 6.10817 12 5.83203 12C5.55589 12 5.33203 12.2239 5.33203 12.5Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_123929\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_rain_sleet_style_outline;
