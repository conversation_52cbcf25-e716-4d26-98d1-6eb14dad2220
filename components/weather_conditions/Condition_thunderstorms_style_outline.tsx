import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_thunderstorms_style_outline = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_121120)\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.99325 2.33398C6.61257 2.33398 5.48781 3.38233 5.37583 4.70536L5.34135 5.11272L4.93527 5.15986C4.21493 5.24349 3.66406 5.84757 3.66406 6.57C3.66406 7.27763 4.19256 7.87172 4.89129 7.97435L5.5187 8.00111C5.79459 8.01287 6.00871 8.24606 5.99694 8.52195C5.98518 8.79784 5.75199 9.01196 5.47609 9.0002L4.8025 8.97147L4.7804 8.96855C3.58995 8.81081 2.66406 7.80158 2.66406 6.57C2.66406 5.46163 3.41391 4.53336 4.43172 4.24373C4.75363 2.58234 6.22949 1.33398 7.99325 1.33398C9.39613 1.33398 10.615 2.12308 11.2181 3.28022C12.4514 3.62484 13.361 4.74684 13.361 6.08585C13.361 7.69329 12.0506 8.98801 10.4414 9.00056L10.4376 9.00059L10.1642 9.00065C9.88806 9.00073 9.66414 8.77693 9.66406 8.50079C9.66398 8.22465 9.88778 8.00073 10.1639 8.00065L10.4354 8.00058C11.5059 7.99128 12.361 7.13217 12.361 6.08585C12.361 5.15449 11.6839 4.37144 10.7793 4.20403L10.517 4.15548L10.4113 3.91057C10.0118 2.9853 9.08088 2.33398 7.99325 2.33398Z\" fill=\"#252422\"/>\n<path d=\"M8.28286 14.4858C8.12797 14.7675 7.77098 14.8657 7.49379 14.7028C7.22808 14.5466 7.13443 14.2076 7.28228 13.9372L8.38983 11.9114H5L7.45631 7.50895C7.61201 7.22989 7.96678 7.13351 8.24227 7.29543C8.50947 7.45247 8.60205 7.79433 8.45061 8.06474L6.9435 10.7558H10.3333L8.28286 14.4858Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_121120\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_thunderstorms_style_outline;
