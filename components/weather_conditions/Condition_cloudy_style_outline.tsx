import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_cloudy_style_outline = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_120230)\">\n<foreignObject x=\"-19\" y=\"-17\" width=\"54\" height=\"50\"><div xmlns=\"http://www.w3.org/1999/xhtml\" style=\"backdrop-filter:blur(10px);clip-path:url(#bgblur_1_19_120230_clip_path);height:100%;width:100%\"></div></foreignObject><path data-figma-bg-blur-radius=\"20\" d=\"M9.0625 3C10.9399 3 12.4138 4.45632 12.5195 6.5293L12.5547 6.77148L13.3496 6.85547L13.3779 6.86328C13.8595 6.98746 14.2691 7.20641 14.5596 7.53711C14.855 7.8735 14.9999 8.29434 15 8.76367C15 9.25719 14.8019 9.76603 14.3838 10.1592C14.0337 10.4884 13.5465 10.7191 12.9268 10.8145C12.9733 10.9711 13 11.1371 13 11.3096C13 12.1564 12.3954 12.8582 11.6123 12.9678L11.5049 12.9824H11.4023L11.3955 12.9834H11.3877L3.58789 12.9893C3.51406 12.9965 3.43866 13 3.3623 13C2.05777 13 1.00002 11.8973 1 10.5371C1 9.33432 1.8282 8.33006 2.92285 8.11621L3.62402 7.97949L3.9043 7.2959C4.2446 6.46439 4.88072 5.79892 5.67285 5.43848C5.70574 4.98866 5.93018 4.59387 6.15234 4.30469C6.39848 3.98443 6.6831 3.73618 6.85352 3.60645C7.45283 3.15032 8.21466 3.00003 9.0625 3ZM7.0293 6.01855C5.96437 6.01865 5.05422 6.69024 4.66406 7.64355L4.20801 8.75586L3.07324 8.97754C2.35651 9.11756 1.82715 9.77194 1.82715 10.5371C1.82716 11.402 2.5036 12.125 3.3623 12.125C3.41294 12.125 3.46242 12.1219 3.51074 12.1172L3.54883 12.1133L11.3877 12.1074H11.4502L11.5039 12.0996C11.8889 12.0456 12.1728 11.703 12.1729 11.3096C12.1729 10.9117 11.8825 10.5657 11.4912 10.5176L9.73633 10.3018L9.58887 8.45703C9.48024 7.10303 8.38409 6.01855 7.0293 6.01855ZM9.0625 3.84766C8.29608 3.84769 7.74575 3.98664 7.35352 4.28516C7.22427 4.38352 6.99847 4.58039 6.80957 4.82617C6.71958 4.94327 6.64805 5.06067 6.5957 5.17383C6.73781 5.15492 6.88233 5.14259 7.0293 5.14258C8.80741 5.14258 10.2675 6.56881 10.4131 8.38281L10.5039 9.51465L11.5859 9.64746C11.9012 9.68612 12.1878 9.82193 12.4199 10.0234C12.4471 10.0159 12.4757 10.0115 12.5049 10.0098C13.1574 9.96992 13.5723 9.76849 13.8184 9.53711C14.0639 9.30622 14.166 9.02343 14.166 8.76367C14.1659 8.48024 14.0825 8.26663 13.9375 8.10156C13.7911 7.93501 13.5553 7.78798 13.2012 7.69238L11.8223 7.5459L11.6885 6.61035V6.58984C11.611 4.92245 10.4669 3.84766 9.0625 3.84766Z\" fill=\"#252422\"/>\n</g>\n<defs>\n<clipPath id=\"bgblur_1_19_120230_clip_path\" transform=\"translate(19 17)\"><path d=\"M9.0625 3C10.9399 3 12.4138 4.45632 12.5195 6.5293L12.5547 6.77148L13.3496 6.85547L13.3779 6.86328C13.8595 6.98746 14.2691 7.20641 14.5596 7.53711C14.855 7.8735 14.9999 8.29434 15 8.76367C15 9.25719 14.8019 9.76603 14.3838 10.1592C14.0337 10.4884 13.5465 10.7191 12.9268 10.8145C12.9733 10.9711 13 11.1371 13 11.3096C13 12.1564 12.3954 12.8582 11.6123 12.9678L11.5049 12.9824H11.4023L11.3955 12.9834H11.3877L3.58789 12.9893C3.51406 12.9965 3.43866 13 3.3623 13C2.05777 13 1.00002 11.8973 1 10.5371C1 9.33432 1.8282 8.33006 2.92285 8.11621L3.62402 7.97949L3.9043 7.2959C4.2446 6.46439 4.88072 5.79892 5.67285 5.43848C5.70574 4.98866 5.93018 4.59387 6.15234 4.30469C6.39848 3.98443 6.6831 3.73618 6.85352 3.60645C7.45283 3.15032 8.21466 3.00003 9.0625 3ZM7.0293 6.01855C5.96437 6.01865 5.05422 6.69024 4.66406 7.64355L4.20801 8.75586L3.07324 8.97754C2.35651 9.11756 1.82715 9.77194 1.82715 10.5371C1.82716 11.402 2.5036 12.125 3.3623 12.125C3.41294 12.125 3.46242 12.1219 3.51074 12.1172L3.54883 12.1133L11.3877 12.1074H11.4502L11.5039 12.0996C11.8889 12.0456 12.1728 11.703 12.1729 11.3096C12.1729 10.9117 11.8825 10.5657 11.4912 10.5176L9.73633 10.3018L9.58887 8.45703C9.48024 7.10303 8.38409 6.01855 7.0293 6.01855ZM9.0625 3.84766C8.29608 3.84769 7.74575 3.98664 7.35352 4.28516C7.22427 4.38352 6.99847 4.58039 6.80957 4.82617C6.71958 4.94327 6.64805 5.06067 6.5957 5.17383C6.73781 5.15492 6.88233 5.14259 7.0293 5.14258C8.80741 5.14258 10.2675 6.56881 10.4131 8.38281L10.5039 9.51465L11.5859 9.64746C11.9012 9.68612 12.1878 9.82193 12.4199 10.0234C12.4471 10.0159 12.4757 10.0115 12.5049 10.0098C13.1574 9.96992 13.5723 9.76849 13.8184 9.53711C14.0639 9.30622 14.166 9.02343 14.166 8.76367C14.1659 8.48024 14.0825 8.26663 13.9375 8.10156C13.7911 7.93501 13.5553 7.78798 13.2012 7.69238L11.8223 7.5459L11.6885 6.61035V6.58984C11.611 4.92245 10.4669 3.84766 9.0625 3.84766Z\"/>\n</clipPath><clipPath id=\"clip0_19_120230\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_cloudy_style_outline;
