import React from 'react';
import { SvgXml } from 'react-native-svg';

interface Props {
  width?: number | string;
  height?: number | string;
  style?: any;
  [key: string]: any;
}

const Condition_cloudy_style_color_light = (props: Props) => {
  const { width, height, style, ...rest } = props;
  
  const w = width ?? style?.width ?? 16;
  const h = height ?? style?.height ?? 16;

  return (
    <SvgXml
      xml={"<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_19_120226)\">\n<path d=\"M7.17404 4.36449C6.83284 4.36449 6.50089 4.40573 6.18261 4.48366C6.72679 3.37717 7.85722 3 9.16025 3C10.8995 3 12.3276 4.39795 12.47 6.17631L12.5588 6.83597L13.6172 6.96606C14.3954 7.06172 15 7.75564 15 8.59515C15 9.40558 14.4365 10.0806 13.6971 10.2122C13.3384 9.35788 12.5607 8.73457 11.631 8.62148L11.4606 8.60074L11.4462 8.42383C11.2623 6.14994 9.41928 4.36449 7.17404 4.36449Z\" fill=\"#A1B6C3\"/>\n<path d=\"M11.4511 12.983L11.4442 12.9831L11.4373 12.983L3.59986 12.9891C3.52561 12.9963 3.45004 13 3.37325 13C2.06254 13 1 11.9054 1 10.5552C1 9.36111 1.83233 8.36436 2.93234 8.15224L3.6367 8.01641L3.91796 7.33744C4.4394 6.07863 5.6507 5.20062 7.058 5.20062C8.84456 5.20062 10.3116 6.61614 10.4578 8.41687L10.549 9.54064L11.6362 9.67237C12.4357 9.76923 13.0567 10.4719 13.0567 11.3219C13.0567 12.1626 12.4492 12.8594 11.6623 12.9682L11.5546 12.983H11.4511Z\" fill=\"#A1B6C3\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_19_120226\">\n<rect width=\"16\" height=\"16\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>\n"}
      width={w}
      height={h}
      viewBox="0 0 16 16"
      style={style}
      {...rest}
    />
  );
};

export default Condition_cloudy_style_color_light;
