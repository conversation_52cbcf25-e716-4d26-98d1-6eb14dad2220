import { useTheme } from '@/app/theme/ThemeContext';
import { Slider } from '@miblanchard/react-native-slider';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { IconSymbol } from './ui/IconSymbol';

export interface AnimationTimelineProps {
  /** Current playback time as Date object */
  currentTime: Date;
  /** Start time of the animation timeline */
  startTime: Date;
  /** End time of the animation timeline */
  endTime: Date;
  /** Whether the animation is currently playing */
  isPlaying: boolean;
  /** Whether to display time in 12-hour format (default: false for 24-hour) */
  use12HourFormat?: boolean;
  /** Current progress as a value between 0 and 1 */
  progress: number;
  /** Total duration label (e.g., "6 Hrs") */
  durationLabel?: string;
  /** Callback when play/pause button is pressed */
  onPlayPause: () => void;
  /** Callback when progress bar is tapped/dragged */
  onProgressChange?: (progress: number) => void;
  /** Custom style for the container */
  style?: any;
}

const AnimationTimeline: React.FC<AnimationTimelineProps> = ({
  currentTime,
  startTime,
  endTime,
  isPlaying,
  use12HourFormat = false,
  progress,
  durationLabel,
  onPlayPause,
  onProgressChange,
  style,
}) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);

  // Simple collapsible state
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Toggle collapsed state
  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Format time for display
  const formatTime = (date: Date): string => {
    if (use12HourFormat) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } else {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    }
  };

  // Calculate duration in hours for default label
  const calculateDurationLabel = (): string => {
    if (durationLabel) return durationLabel;
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationHours = Math.round(durationMs / (1000 * 60 * 60));
    return `${durationHours} Hrs`;
  };

  // Generate time markers for the progress bar
  const generateTimeMarkers = () => {
    const markers = [];
    const totalDuration = endTime.getTime() - startTime.getTime();
    const hourInMs = 60 * 60 * 1000;
    
    // Create markers for each hour
    for (let i = 0; i <= Math.ceil(totalDuration / hourInMs); i++) {
      const markerTime = new Date(startTime.getTime() + (i * hourInMs));
      if (markerTime <= endTime) {
        const markerProgress = (markerTime.getTime() - startTime.getTime()) / totalDuration;
        markers.push({
          time: markerTime,
          progress: markerProgress,
          label: `${i + 1}p`, // 1p, 2p, 3p, etc.
          isMajor: true,
        });
      }
    }
    
    return markers;
  };

  // Generate minor tick marks (every 15 minutes - 3 ticks between each hour)
  const generateMinorTicks = () => {
    const ticks = [];
    const totalDuration = endTime.getTime() - startTime.getTime();
    const fifteenMinInMs = 15 * 60 * 1000;
    
    // Create minor ticks every 15 minutes
    for (let i = 1; i < Math.ceil(totalDuration / fifteenMinInMs); i++) {
      const tickTime = new Date(startTime.getTime() + (i * fifteenMinInMs));
      if (tickTime < endTime) {
        const tickProgress = (tickTime.getTime() - startTime.getTime()) / totalDuration;
        // Only add if it's not on a major hour mark (every 4th tick would be on the hour)
        const isOnHourMark = (i * 15) % 60 === 0;
        if (!isOnHourMark) {
          ticks.push({
            progress: tickProgress,
            isMajor: false,
          });
        }
      }
    }
    
    return ticks;
  };

  const timeMarkers = generateTimeMarkers();
  const minorTicks = generateMinorTicks();

  // Handle slider value change
  const handleSliderChange = (value: number[]) => {
    if (!onProgressChange) return;
    
    // Convert slider value (0-1) to progress
    const newProgress = Math.max(0, Math.min(1, value[0]));
    onProgressChange(newProgress);
  };

  // Calculate current time from progress
  const calculateTimeFromProgress = (prog: number): Date => {
    const totalDuration = endTime.getTime() - startTime.getTime();
    return new Date(startTime.getTime() + (prog * totalDuration));
  };

  const displayTime = calculateTimeFromProgress(progress);

  return (
    <View style={[themedStyles.container, style]}>
      {/* Collapse Handle */}
      <TouchableOpacity 
        style={themedStyles.collapseHandle}
        onPress={toggleCollapsed}
        activeOpacity={0.7}
      >
        <View style={themedStyles.handleBar} />
        <ThemedText style={themedStyles.handleText}>
          {isCollapsed ? '⌃' : '⌄'}
        </ThemedText>
      </TouchableOpacity>

      {/* Main Content - conditionally rendered */}
      {!isCollapsed && (
        <View>
          {/* Header with current time and duration */}
          <View style={themedStyles.header}>
            <ThemedText style={themedStyles.currentTimeText}>
              {formatTime(displayTime)}
            </ThemedText>
            <View style={themedStyles.durationContainer}>
              <IconSymbol 
                name="clock" 
                size={16} 
                color={colors.textSecondary}
                style={themedStyles.clockIcon}
              />
              <ThemedText style={themedStyles.durationText}>
                {calculateDurationLabel()}
              </ThemedText>
            </View>
          </View>

          {/* Controls section */}
          <View style={themedStyles.controlsContainer}>
            {/* Play/Pause Button */}
            <TouchableOpacity
              style={themedStyles.playButton}
              onPress={onPlayPause}
              activeOpacity={0.7}
            >
              <IconSymbol
                name={isPlaying ? "pause.fill" : "play.fill"}
                size={20}
                color={colors.text}
              />
            </TouchableOpacity>

            {/* Progress Bar Container */}
            <View style={themedStyles.progressContainer}>
              {/* Time markers */}
              <View style={themedStyles.timeMarkersContainer}>
                {/* Minor ticks */}
                {minorTicks.map((tick, index) => (
                  <View
                    key={`minor-${index}`}
                    style={[
                      themedStyles.minorTick,
                      { left: `${tick.progress * 100}%` }
                    ]}
                  />
                ))}
                
                {/* Major time markers */}
                {timeMarkers.map((marker, index) => (
                  <View
                    key={`major-${index}`}
                    style={[
                      themedStyles.timeMarker,
                      { left: `${marker.progress * 100}%` }
                    ]}
                  >
                    <View style={themedStyles.markerTick} />
                    <ThemedText style={themedStyles.markerLabel}>
                      {marker.label}
                    </ThemedText>
                  </View>
                ))}
              </View>

              {/* Progress slider */}
              <View style={themedStyles.sliderContainer}>
                <Slider
                  value={[progress]}
                  onValueChange={handleSliderChange}
                  minimumValue={0}
                  maximumValue={1}
                  thumbStyle={themedStyles.sliderThumb}
                  trackStyle={themedStyles.sliderTrack}
                  minimumTrackStyle={{ backgroundColor: colors.text }}
                  maximumTrackStyle={{ backgroundColor: colors.border }}
                  containerStyle={themedStyles.slider}
                />
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    width: '100%',
  },
  collapseHandle: {
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: colors.textMuted,
    borderRadius: 2,
    marginBottom: 4,
  },
  handleText: {
    fontSize: 12,
    color: colors.textMuted,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentTimeText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 10,
  },
  clockIcon: {
    marginRight: 4,
  },
  durationText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    height: 80,
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 10,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    flex: 1,
    height: 68,
    paddingBottom: 10,
  },
  timeMarkersContainer: {
    position: 'relative',
    height: 30,
    paddingHorizontal: 2,
    marginHorizontal: 10,
    marginTop: -16,
  },
  timeMarker: {
    position: 'absolute',
    alignItems: 'center',
    transform: [{ translateX: -3 }],
  },
  markerTick: {
    width: 2,
    height: 8,
    backgroundColor: colors.textMuted,
  },
  markerLabel: {
    fontSize: 8,
    color: colors.textMuted,
    fontWeight: '400',
  },
  minorTick: {
    position: 'absolute',
    width: 1,
    height: 4,
    backgroundColor: colors.textMuted,
    transform: [{ translateX: -0.5 }],
  },
  sliderContainer: {
    paddingHorizontal: 8,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    width: 10,
    height: 10,
    backgroundColor: colors.text,
    borderRadius: 8,
  },
  sliderTrack: {
    height: 8,
    borderRadius: 8,
  },
});

export default AnimationTimeline;
