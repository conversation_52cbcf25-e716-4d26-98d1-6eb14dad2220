import { useTheme } from '@/app/theme/ThemeContext';
import Slider from '@react-native-community/slider';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { IconSymbol } from './ui/IconSymbol';

export interface AnimationTimelineProps {
  /** Current playback time as Date object */
  currentTime: Date;
  /** Start time of the animation timeline */
  startTime: Date;
  /** End time of the animation timeline */
  endTime: Date;
  /** Whether the animation is currently playing */
  isPlaying: boolean;
  /** Whether to display time in 12-hour format (default: false for 24-hour) */
  use12HourFormat?: boolean;
  /** Current progress as a value between 0 and 1 */
  progress: number;
  /** Total duration label (e.g., "6 Hrs") */
  durationLabel?: string;
  /** Callback when play/pause button is pressed */
  onPlayPause: () => void;
  /** Callback when progress bar is tapped/dragged */
  onProgressChange?: (progress: number) => void;
  /** Custom style for the container */
  style?: any;
}

/**
 * AnimationTimeline Component
 * 
 * A timeline control for map animations that displays:
 * - Current playback time
 * - Total duration
 * - Play/pause toggle button
 * - Progress bar with time markers
 */
export const AnimationTimeline: React.FC<AnimationTimelineProps> = ({
  currentTime,
  startTime,
  endTime,
  isPlaying,
  use12HourFormat = false,
  progress,
  durationLabel,
  onPlayPause,
  onProgressChange,
  style,
}) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);

  // Format time for display
  const formatTime = (date: Date): string => {
    if (use12HourFormat) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } else {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    }
  };

  // Calculate duration in hours for default label
  const calculateDurationLabel = (): string => {
    if (durationLabel) return durationLabel;
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationHours = Math.round(durationMs / (1000 * 60 * 60));
    return `${durationHours} Hrs`;
  };

  // Generate time markers for the progress bar
  const generateTimeMarkers = () => {
    const markers = [];
    const totalDuration = endTime.getTime() - startTime.getTime();
    const hourInMs = 60 * 60 * 1000;

    // Create markers for each hour
    for (let i = 0; i <= Math.ceil(totalDuration / hourInMs); i++) {
      const markerTime = new Date(startTime.getTime() + (i * hourInMs));
      if (markerTime <= endTime) {
        const markerProgress = (markerTime.getTime() - startTime.getTime()) / totalDuration;
        markers.push({
          time: markerTime,
          progress: markerProgress,
          label: `${i + 1}p`, // 1p, 2p, 3p, etc.
          isMajor: true,
        });
      }
    }

    return markers;
  };

  // Generate minor tick marks (every 15 minutes - 3 ticks between each hour)
  const generateMinorTicks = () => {
    const ticks = [];
    const totalDuration = endTime.getTime() - startTime.getTime();
    const fifteenMinInMs = 15 * 60 * 1000;

    // Create minor ticks every 15 minutes
    for (let i = 1; i < Math.ceil(totalDuration / fifteenMinInMs); i++) {
      const tickTime = new Date(startTime.getTime() + (i * fifteenMinInMs));
      if (tickTime < endTime) {
        const tickProgress = (tickTime.getTime() - startTime.getTime()) / totalDuration;
        // Only add if it's not on a major hour mark (every 4th tick would be on the hour)
        const isOnHourMark = (i * 15) % 60 === 0;
        if (!isOnHourMark) {
          ticks.push({
            progress: tickProgress,
            isMajor: false,
          });
        }
      }
    }

    return ticks;
  };

  const timeMarkers = generateTimeMarkers();
  const minorTicks = generateMinorTicks();

  // Handle slider value change
  const handleSliderChange = (value: number) => {
    if (!onProgressChange) return;

    // Convert slider value (0-1) to progress
    const newProgress = Math.max(0, Math.min(1, value));
    console.log('Slider changed to progress:', newProgress);
    onProgressChange(newProgress);
  };

  return (
    <View style={[themedStyles.container, style]}>
      {/* Header with current time and duration */}
      <View style={themedStyles.header}>
        <ThemedText style={themedStyles.currentTimeText}>
          {formatTime(currentTime)}
        </ThemedText>
        <View style={themedStyles.durationContainer}>
          <IconSymbol 
            name="clock" 
            size={16} 
            color={colors.textSecondary}
            style={themedStyles.clockIcon}
          />
          <ThemedText style={themedStyles.durationText}>
            {calculateDurationLabel()}
          </ThemedText>
        </View>
      </View>

      {/* Controls section */}
      <View style={themedStyles.controlsContainer}>
        {/* Play/Pause Button */}
        <TouchableOpacity
          style={themedStyles.playButton}
          onPress={onPlayPause}
          activeOpacity={0.7}
        >
          <IconSymbol
            name={isPlaying ? "pause.fill" : "play.fill"}
            size={20}
            color={colors.text}
          />
        </TouchableOpacity>

        {/* Progress Bar Container */}
        <View style={themedStyles.progressContainer}>
          
          {/* Progress slider */}
          <View style={themedStyles.sliderContainer}>
            <Slider
              style={themedStyles.slider}
              minimumValue={0}
              maximumValue={1}
              value={progress}
              onValueChange={handleSliderChange}
              minimumTrackTintColor={colors.text}
              maximumTrackTintColor={colors.border}
              thumbTintColor={colors.text}
              // To make the thumb smaller, use a custom image:
              thumbImage={require('../assets/images/slider-thumb.png')}
            />
          </View>
          {/* Time markers */}
          <View style={themedStyles.timeMarkersContainer}>
            {/* Minor ticks */}
            {minorTicks.map((tick, index) => (
              <View
                key={`minor-${index}`}
                style={[
                  themedStyles.minorTick,
                  { left: `${tick.progress * 100}%` }
                ]}
              />
            ))}

            {/* Major time markers */}
            {timeMarkers.map((marker, index) => (
              <View
                key={`major-${index}`}
                style={[
                  themedStyles.timeMarker,
                  { left: `${marker.progress * 100}%` }
                ]}
              >
                <View style={themedStyles.markerTick} />
                <ThemedText style={themedStyles.markerLabel}>
                  {marker.label}
                </ThemedText>
              </View>
            ))}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    padding: 10,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  currentTimeText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 10,
  },
  clockIcon: {
    marginRight: 4,
  },
  durationText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    height: 80,
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 10,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    flex: 1,
    height: 45,
  },
  timeMarkersContainer: {
    position: 'relative',
    height: 30,
    paddingLeft: 4,
  },
  timeMarker: {
    position: 'absolute',
    alignItems: 'center',
    transform: [{ translateX: -2 }], // Center the marker
  },
  markerTick: {
    width: 2,
    height: 8,
    backgroundColor: colors.textMuted,
    // marginBottom: 2,
  },
  markerLabel: {
    fontSize: 8,
    color: colors.textMuted,
    fontWeight: '400',
  },
  minorTick: {
    position: 'absolute',
    width: 1,
    height: 4,
    backgroundColor: colors.textMuted,
    transform: [{ translateX: -0.5 }], // Center the tick
  },

  sliderContainer: {
    // paddingHorizontal: 4,
    // marginTop: 4,
  },
  slider: {
    width: '100%',
    height: 40,
    // Additional styling options you can uncomment:
    // backgroundColor: colors.backgroundSecondary,
    // borderRadius: 8,
    // Platform-specific thumb sizing (less reliable):
    // transform: [{ scale: 0.8 }], // Makes entire slider smaller
  },
});

export default AnimationTimeline;
