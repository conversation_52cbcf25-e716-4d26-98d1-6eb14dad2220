import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import { HourlyForecastData, useHourlyForecastQuery } from '@/hooks/useHourlyForecastQuery';
import React from 'react';
import { ActivityIndicator, ScrollView, StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { getWeatherIconComponent } from './weather_conditions/weatherIconResolver';

interface HourlyForecastBarProps {
  location: string;
}

function formatHourLabel(isoString: string) {
  const date = new Date(isoString);
  let hour = date.getHours();
  const ampm = hour >= 12 ? 'pm' : 'am';
  hour = hour % 12;
  if (hour === 0) hour = 12;
  return `${hour}${ampm}`;
}

export const HourlyForecastBar: React.FC<HourlyForecastBarProps> = ({ location }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  // Call hooks before any early returns
  const { data, isLoading, error } = useHourlyForecastQuery(location || '');
  
  // Define HourItem component inside to access styles
  const HourItem: React.FC<{ item: HourlyForecastData }> = ({ item }) => {
    // Determine if this hour is day or night
    let isNight: boolean;
    if (item.dayOrNight === 'N') {
      isNight = true;
    } else if (item.dayOrNight === 'D') {
      isNight = false;
    } else {
      const date = new Date(item.time);
      const hour = date.getHours();
      isNight = hour < 8 || hour >= 20;
    }
    const timeOfDay = isNight ? 'night' : 'day';
    const IconComponent = getWeatherIconComponent(Number(item.icon), timeOfDay);
    return (
      <View style={styles.hourItem}>
        <ThemedText style={styles.time}>{formatHourLabel(item.time)}</ThemedText>
        {/* Render weather icon component instead of emoji */}
        <View style={{ marginBottom: 4 }}>
          <IconComponent style={{ width: 28, height: 28 }} />
        </View>
        <ThemedText style={styles.temp}>{item.temp}</ThemedText>
      </View>
    );
  };
  
  if (!location || location.trim() === "") {
    return <ThemedText style={styles.error}>Location not available</ThemedText>;
  }

  if (isLoading) return <ActivityIndicator size="small" style={styles.centered} />;
  if (error) return <ThemedText style={styles.error}>Failed to load hourly forecast</ThemedText>;
  if (!data) return null;

  // Always start with the current hour (or closest future hour)
  const now = new Date();
  let startIdx = data.findIndex(item => new Date(item.time).getHours() === now.getHours() && new Date(item.time) >= now);
  if (startIdx === -1) {
    // If no exact match for current hour, find the first future hour
    startIdx = data.findIndex(item => new Date(item.time) >= now);
  }
  if (startIdx === -1) {
    // If all hours are in the past, show the last 24
    startIdx = Math.max(0, data.length - 24);
  }
  const hours = data.slice(startIdx, startIdx + 24);

  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.bar} contentContainerStyle={{ gap: 4 }}>
      {hours.map((item, idx) => (
        <HourItem key={idx} item={item} />
      ))}
    </ScrollView>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  bar: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 12,
    marginVertical: 12,
    minHeight: 110,
  },
  hourItem: {
    alignItems: 'center',
    minWidth: 50,
  },
  time: {
    fontSize: 12,
    color: colors.textOnCard,
    marginBottom: 4,
  },
  emoji: {
    fontSize: 22,
    marginBottom: 4,
  },
  temp: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textOnCardSecondary,
    marginBottom: 2,
  },
  centered: {
    marginVertical: 16,
  },
  error: {
    color: 'red',
    textAlign: 'center',
    marginVertical: 16,
  },
}); 