import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { getWeatherIconComponent } from './weather_conditions/weatherIconResolver';

interface ThreeDayForecastDay {
  calendarDay: string;
  dayOfWeek: string;
  narrative: string;
  temperatureMax: number;
  temperatureMin: number;
  iconCode: number;
}

interface ThreeDayForecastProps {
  forecasts: ThreeDayForecastDay[];
}

/**
 * Compact 3-day forecast display component (shows only 3 days out, not today)
 */
const ThreeDayForecast: React.FC<ThreeDayForecastProps> = ({ forecasts }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  // Only show the 3 days out (skip today)
  const days = forecasts && forecasts.length > 3 ? forecasts.slice(1, 4) : [];
  if (!days.length) {
    return <Text style={styles.errorText}>No forecast data available.</Text>;
  }

  return (
    <View style={styles.container}>
      {days.map((day, idx) => {
        const IconComponent = getWeatherIconComponent(day.iconCode, 'day');
        return (
          <View key={day.calendarDay || idx} style={styles.dayContainer}>
            <Text style={styles.dayOfWeek}>{day.dayOfWeek}</Text>
            <View style={styles.iconWrap}>
              <IconComponent style={{ width: 32, height: 32 }} />
            </View>
            <View style={styles.tempsRow}>
              <Text style={styles.tempMax}>{day.temperatureMax}°</Text>
              <Text style={styles.tempMin}>/{day.temperatureMin}°</Text>
            </View>
          </View>
        );
      })}
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginVertical: 8,
  },
  dayContainer: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors.backgroundOverlayLight,
    borderRadius: 12,
    padding: 8,
    marginHorizontal: 4,
    minWidth: 90,
    maxWidth: 120,
  },
  dayOfWeek: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textOnCard,
    fontFamily: 'Inter',
    marginBottom: 4,
  },
  iconWrap: {
    marginBottom: 4,
  },
  tempsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  tempMax: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.weatherHighlight,
  },
  tempMin: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textMuted,
    fontFamily: 'Inter',
    marginLeft: 2,
  },
  emoji: {
    fontSize: 28,
    marginBottom: 4,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 8,
  },
});

export default ThreeDayForecast; 