import { useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { ThemedText } from './ThemedText';

// Tab definition type
export interface TabDefinition {
  key: string;
  title: string;
}

interface TabBarProps {
  tabs: TabDefinition[];
  activeTab: string;
  onTabPress: (key: string) => void;
  style?: ViewStyle;
  tabStyle?: ViewStyle;
  activeTabStyle?: ViewStyle;
  tabTextStyle?: TextStyle;
  activeTabTextStyle?: TextStyle;
  renderTab?: (tab: TabDefinition, isActive: boolean, onPress: () => void) => React.ReactNode;
}

/**
 * TabBar
 * Renders a row of tabs and manages active state.
 */
const TabBar: React.FC<TabBarProps> = ({
  tabs,
  activeTab,
  onTabPress,
  style,
  tabStyle,
  activeTabStyle,
  tabTextStyle,
  activeTabTextStyle,
  renderTab,
}) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);
  
  return (
    <View style={[themedStyles.tabBarContainer, style]}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.key;
        const handlePress = () => onTabPress(tab.key);
        if (renderTab) {
          return renderTab(tab, isActive, handlePress);
        }
        return (
          <TouchableOpacity
            key={tab.key}
            onPress={handlePress}
            style={[
              themedStyles.tabItem,
              tabStyle,
              isActive && [themedStyles.activeTabItem, activeTabStyle],
            ]}
          >
            <ThemedText
              style={[
                themedStyles.tabText,
                tabTextStyle,
                isActive ? [themedStyles.activeTabText, activeTabTextStyle] : {},
              ]}
            >
              {tab.title}
            </ThemedText>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = (colors: any) => StyleSheet.create({
  tabBarContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingVertical: 0,
    borderBottomWidth: 0,
    borderBottomColor: colors.border,
    backgroundColor: colors.background,
    
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
    marginTop: 13,  //TODO: the top margin is a hack to not have the tabs cut off by the header (on some devices)
  },
  activeTabItem: {},
  tabText: {
    paddingVertical: 15,
    paddingHorizontal: 10,
    fontFamily: 'Inter',
    fontSize: 13,
    fontStyle: 'normal',
    fontWeight: '500',
    textAlign: 'center',
  },
  activeTabText: {
    fontWeight: '600',
  },
});

export default TabBar; 