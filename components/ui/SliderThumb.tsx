import { useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, View } from 'react-native';

interface SliderThumbProps {
  size?: number;
  color?: string;
}

/**
 * Custom Slider Thumb Component
 * 
 * A small circular thumb for the slider component.
 * This can be used as a custom thumb image by converting to PNG.
 */
export const SliderThumb: React.FC<SliderThumbProps> = ({ 
  size = 12, 
  color 
}) => {
  const { colors } = useTheme();
  const thumbColor = color || colors.text;

  return (
    <View style={[
      styles.thumb,
      {
        width: size,
        height: size,
        backgroundColor: thumbColor,
        borderRadius: size / 2,
      }
    ]} />
  );
};

const styles = StyleSheet.create({
  thumb: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
});

export default SliderThumb; 