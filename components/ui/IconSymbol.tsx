// Fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolViewProps, SymbolWeight } from 'expo-symbols';
import { ComponentProps } from 'react';
import { OpaqueColorValue, type StyleProp, type TextStyle } from 'react-native';

type IconMapping = Record<SymbolViewProps['name'], ComponentProps<typeof MaterialIcons>['name']>;
type IconSymbolName = keyof typeof MAPPING;

/**
 * Add your SF Symbols to Material Icons mappings here.
 * - see Material Icons in the [Icons Directory](https://icons.expo.fyi).
 * - see SF Symbols in the [SF Symbols](https://developer.apple.com/sf-symbols/) app.
 */
const MAPPING = {
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  'chevron.down': 'keyboard-arrow-down',
  'location.fill': 'location-on',
  // Weather icon mappings for MaterialIcons
  'wb-sunny': 'wb-sunny',
  'wb-cloudy': 'wb-cloudy',
  'cloud': 'cloud',
  'umbrella': 'umbrella',
  'ac-unit': 'ac-unit',
  'foggy': 'foggy',
  'air': 'air',
  // Added for navigation tabs
  'snowflake': 'ac-unit', // Today
  'magnifyingglass': 'search', // Explore
  'map': 'map', // Map
  'mountain.2': 'terrain', // Destinations
  'person.crop.circle': 'account-circle', // Profile
  // Animation timeline icons
  'play.fill': 'play-arrow',
  'pause.fill': 'pause',
  'clock': 'access-time',
  // Add more as needed
} as const;

/**
 * An icon component that uses native SF Symbols on iOS, and Material Icons on Android and web.
 * This ensures a consistent look across platforms, and optimal resource usage.
 * Icon `name`s are based on SF Symbols and require manual mapping to Material Icons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  // Fallback to 'wb-sunny' if name is not in mapping
  const iconName = MAPPING[name] || MAPPING['wb-sunny'];
  return <MaterialIcons color={color} size={size} name={iconName} style={style} />;
}
