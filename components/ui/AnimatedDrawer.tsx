import React, { useRef, useState } from 'react';
import { Animated, PanResponder, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import { ThemedText } from '../ThemedText';

export interface AnimatedDrawerProps {
  /** Initial collapsed state */
  initialCollapsed?: boolean;
  /** Height when collapsed */
  collapsedHeight?: number;
  /** Height when expanded */
  expandedHeight?: number;
  /** Drag range distance for full transition */
  dragRange?: number;
  /** Minimum drag distance to trigger state change */
  dragThreshold?: number;
  /** Minimum drag progress (0-1) to complete transition */
  dragProgressThreshold?: number;
  /** Animation duration in milliseconds */
  animationDuration?: number;
  /** Container style overrides */
  containerStyle?: ViewStyle;
  /** Handle container style overrides */
  handleStyle?: ViewStyle;
  /** Handle bar style overrides */
  handleBarStyle?: ViewStyle;
  /** Handle text style overrides */
  handleTextStyle?: any;
  /** Handle touch area style overrides */
  handleTouchAreaStyle?: ViewStyle;
  /** Content container style overrides */
  contentStyle?: ViewStyle;
  /** Custom handle bar component */
  renderHandleBar?: () => React.ReactNode;
  /** Custom handle text component */
  renderHandleText?: (isCollapsed: boolean) => React.ReactNode;
  /** Callback when collapsed state changes */
  onStateChange?: (isCollapsed: boolean) => void;
  /** Children to render in the drawer content */
  children: React.ReactNode;
}

export const AnimatedDrawer: React.FC<AnimatedDrawerProps> = ({
  initialCollapsed = false,
  collapsedHeight = 60,
  expandedHeight = 200,
  dragRange = 150,
  dragThreshold = 50,
  dragProgressThreshold = 0.3,
  animationDuration = 300,
  containerStyle,
  handleStyle,
  handleBarStyle,
  handleTextStyle,
  handleTouchAreaStyle,
  contentStyle,
  renderHandleBar,
  renderHandleText,
  onStateChange,
  children,
}) => {
  // Collapsible state and animation
  const [isCollapsed, setIsCollapsed] = useState(initialCollapsed);
  const animatedHeight = useRef(new Animated.Value(initialCollapsed ? 0 : 1)).current;
  const dragOffset = useRef(new Animated.Value(0)).current;

  // Toggle collapsed state with animation
  const toggleCollapsed = () => {
    const newCollapsed = !isCollapsed;
    const toValue = newCollapsed ? 0 : 1;
    setIsCollapsed(newCollapsed);
    
    // Reset drag offset and animate to final position
    dragOffset.setValue(0);
    Animated.timing(animatedHeight, {
      toValue,
      duration: animationDuration,
      useNativeDriver: false,
    }).start();

    // Notify parent of state change
    onStateChange?.(newCollapsed);
  };

  // Pan responder for full-range drag gestures
  const panResponder = React.useMemo(() => {
    let startValue = 0; // Store the starting animation value
    
    return PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to vertical gestures with some threshold
        return Math.abs(gestureState.dy) > Math.abs(gestureState.dx) && Math.abs(gestureState.dy) > 5;
      },
      onPanResponderGrant: () => {
        // Store the current animation value when starting drag
        startValue = isCollapsed ? 0 : 1;
        // Stop any ongoing animations
        animatedHeight.stopAnimation();
        dragOffset.setValue(0);
      },
      onPanResponderMove: (_, gestureState) => {
        // Calculate the drag progress based on gesture
        const dragProgress = gestureState.dy / dragRange;
        
        // Calculate new animation value based on starting state and drag
        let newValue;
        if (isCollapsed) {
          // When collapsed, dragging up (negative dy) should expand
          newValue = Math.max(0, Math.min(1, startValue - dragProgress));
        } else {
          // When expanded, dragging down (positive dy) should collapse
          newValue = Math.max(0, Math.min(1, startValue - dragProgress));
        }
        
        // Update the animation value in real-time
        animatedHeight.setValue(newValue);
      },
      onPanResponderRelease: (_, gestureState) => {
        const dragProgress = Math.abs(gestureState.dy) / dragRange;
        
        // Determine final state based on drag distance and direction
        let shouldToggle = false;
        let finalValue = isCollapsed ? 0 : 1; // Default to current state
        
        if (Math.abs(gestureState.dy) > dragThreshold) {
          if (dragProgress > dragProgressThreshold) { // If dragged more than threshold
            shouldToggle = true;
            finalValue = isCollapsed ? 1 : 0;
          }
        }
        
        // Update state if needed
        if (shouldToggle) {
          const newCollapsed = !isCollapsed;
          setIsCollapsed(newCollapsed);
          onStateChange?.(newCollapsed);
        }
        
        // Animate to final position
        dragOffset.setValue(0);
        Animated.spring(animatedHeight, {
          toValue: finalValue,
          useNativeDriver: false,
          tension: 150,
          friction: 8,
        }).start();
      },
    });
  }, [isCollapsed, animatedHeight, dragOffset, dragRange, dragThreshold, dragProgressThreshold, onStateChange]);

  // Default handle bar component
  const defaultHandleBar = () => (
    <View style={[styles.handleBar, handleBarStyle]} />
  );

  // Default handle text component
  const defaultHandleText = (collapsed: boolean) => (
    <ThemedText style={[styles.handleText, handleTextStyle]}>
      {collapsed ? '⌃' : '⌄'}
    </ThemedText>
  );

  return (
    <Animated.View 
      style={[
        styles.container,
        containerStyle,
        {
          height: animatedHeight.interpolate({
            inputRange: [0, 1],
            outputRange: [collapsedHeight, expandedHeight],
          }),
          overflow: 'hidden',
        }
      ]}
    >
      {/* Collapse Handle */}
      <View 
        style={[styles.handle, handleStyle]}
        {...panResponder.panHandlers}
      >
        <TouchableOpacity 
          style={[styles.handleTouchArea, handleTouchAreaStyle]}
          onPress={toggleCollapsed}
          activeOpacity={0.7}
        >
          {renderHandleBar ? renderHandleBar() : defaultHandleBar()}
          {renderHandleText ? renderHandleText(isCollapsed) : defaultHandleText(isCollapsed)}
        </TouchableOpacity>
      </View>

      {/* Content with animated opacity */}
      <Animated.View 
        style={[
          styles.content,
          contentStyle,
          {
            opacity: animatedHeight.interpolate({
              inputRange: [0, 0.3, 1],
              outputRange: [0, 0, 1],
            }),
          }
        ]}
      >
        {children}
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    width: '100%',
  },
  handle: {
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  handleTouchArea: {
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: '#999999',
    borderRadius: 2,
    marginBottom: 4,
  },
  handleText: {
    fontSize: 12,
    color: '#999999',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default AnimatedDrawer;
