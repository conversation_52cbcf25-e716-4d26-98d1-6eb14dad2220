import React, { useState } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { SemanticColors, useTheme } from '../../app/theme/ThemeContext';

interface ResortLogoProps {
  name: string;
  logoUrl?: string;
  size?: number; // Optional, default 48
}

/**
 * ResortLogo displays the resort logo if available, otherwise shows initials with a themed fallback background.
 * Handles image load errors and uses the new theme color for fallback.
 */
const ResortLogo: React.FC<ResortLogoProps> = ({ name, logoUrl, size = 48 }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [imageError, setImageError] = useState(false);

  // Extract initials from resort name (first two words, alphabetic chars only)
  const getInitials = (resortName: string) => {
    if (!resortName) return '';
    // Split by space, filter out empty, take first two words
    const words = resortName.split(' ').filter(Boolean);
    let initials = '';
    for (let i = 0; i < words.length && initials.length < 2; i++) {
      // Find first alphabetic character in the word
      const match = words[i].replace(/[^A-Za-z]/g, '').charAt(0);
      if (match) {
        initials += match.toUpperCase();
      }
    }
    return initials;
  };

  // Show logo if logoUrl is present and no error, otherwise fallback
  if (logoUrl && !imageError) {
    return (
      <Image
        source={{ uri: logoUrl }}
        style={[styles.logo, { width: size, height: size, borderRadius: size * 0.2 }]}
        resizeMode="contain"
        onError={() => setImageError(true)}
        accessibilityLabel={`${name} logo`}
      />
    );
  }

  // Fallback: initials with themed background
  return (
    <View
      style={[
        styles.fallback,
        {
          width: size,
          height: size,
          borderRadius: size * 0.2,
          backgroundColor: colors.resortLogoFallbackBackground,
        },
      ]}
      accessibilityLabel={`${name} logo fallback`}
    >
      <Text style={[styles.initials, { fontSize: size * 0.45 }]}>{getInitials(name)}</Text>
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  logo: {
    backgroundColor: 'transparent',
  },
  fallback: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.card,
    // Shadow for iOS
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 3.84,
    // Elevation for Android
    elevation: 4,
  },
  initials: {
    color: colors.textOnDark,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
});

export default ResortLogo; 