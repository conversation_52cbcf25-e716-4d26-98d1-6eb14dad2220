import { useTheme } from '@/app/theme/ThemeContext';
import { useEffect, useState } from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import ConfigService from '../../services/appConfigService';
import { ThemedText } from '../ThemedText';

/**
 * CacheBypassIndicator
 * Shows a small indicator when cache bypass is enabled (debug mode only)
 * Positioned subtly in the bottom right corner above the tab bar
 */
export function CacheBypassIndicator() {
  const { colors } = useTheme();
  const [bypassEnabled, setBypassEnabled] = useState(false);
  const [isDev, setIsDev] = useState(false);

  useEffect(() => {
    // Check if we're in development mode
    const devMode = __DEV__ ?? false;
    setIsDev(devMode);

    if (!devMode) {
      return; // Don't show indicator in production
    }

    // Check initial bypass state
    const checkBypassState = () => {
      const enabled = ConfigService.get('debug.bypassCache', false);
      setBypassEnabled(enabled);
    };

    checkBypassState();

    // Set up a listener for config changes (if available)
    // Note: This is a simple polling approach since ConfigService doesn't have events
    const interval = setInterval(checkBypassState, 2000); // Check every 2 seconds

    return () => clearInterval(interval);
  }, []);

  // Don't render anything if not in dev mode or bypass is disabled
  if (!isDev || !bypassEnabled) {
    return null;
  }

  const themedStyles = styles(colors);

  return (
    <View style={themedStyles.container}>
      <View style={themedStyles.indicator}>
        <ThemedText style={themedStyles.text}>🚫</ThemedText>
      </View>
    </View>
  );
}

const styles = (colors: any) => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: Platform.select({
      ios: 85, // Above tab bar on iOS
      android: 65, // Above tab bar on Android
      default: 65,
    }),
    right: 8,
    zIndex: 1000,
    pointerEvents: 'none', // Don't interfere with touch events
  },
  indicator: {
    backgroundColor: colors.background,
    borderColor: colors.border,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  text: {
    fontSize: 12,
    opacity: 0.7,
  },
});

export default CacheBypassIndicator; 