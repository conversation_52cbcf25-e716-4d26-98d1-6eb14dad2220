import { useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, View } from 'react-native'; // Added Image and TouchableOpacity
import { LeaderboardEntry } from '../hooks/useLeaderboardQuery'; // Assuming this path is correct
import { Resort } from '../types'; // Import Resort type
import LeaderboardRow from './LeaderboardRow';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface LeaderboardSectionProps {
  title: string;
  subtitle: string;
  entries: LeaderboardEntry[];
  showLogos?: boolean; // To match the design, logos are not shown for all sections
  onEntryPress?: (resort: Resort) => void; // Callback for when an entry is pressed
}

const LeaderboardSection: React.FC<LeaderboardSectionProps> = ({ title, subtitle, entries, showLogos = true, onEntryPress }) => {
  const { colors } = useTheme();
  const themedStyles = styles(colors);
  
  const handlePress = (entry: LeaderboardEntry) => {
    if (onEntryPress) {
      // Create a partial Resort object from the entry
      // In a real app, you might fetch full details or have them already
      const resort: Resort = {
        id: entry.resortId,
        name: entry.resortName || `Resort ${entry.resortId}`,
        description: `Details for ${entry.resortName || `Resort ${entry.resortId}`}. Snowfall: ${String(entry.snowfall)}"`, // Basic description
        logo: entry.resortLogoUrl,
        // Mock coordinates - replace with actual data if available
        coordinates: {
          latitude: 0,
          longitude: 0,
        },
        // Add other necessary fields from Resort type with default/mock values
        // backgroundImage: 'url_to_default_image.png', // Optional: if you have a default
        // website: 'https://example.com', // Optional
      };
      onEntryPress(resort);
    }
  };

  return (
    <ThemedView style={themedStyles.container}>
      <ThemedText type="title" style={themedStyles.title}>{title}</ThemedText>
      <ThemedText type="subtitle" style={themedStyles.subtitle}>{subtitle}</ThemedText>
      <View style={themedStyles.listContainer}>
        {entries.map((entry) => (
          <LeaderboardRow
            key={entry.resortId}
            entry={entry}
            showLogos={showLogos}
            onPress={onEntryPress}
          />
        ))}
      </View>
    </ThemedView>
  );
};

const styles = (colors: any) => StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 10,
    marginHorizontal: 10,
    marginVertical: 10,
    backgroundColor: colors.card,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontFamily: 'Inter',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '500',
    letterSpacing: -0.28,
  },
  subtitle: {
    fontFamily: 'Inter',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
    letterSpacing: -0.24,
    color: colors.textSecondary,
  },
  listContainer: {
    marginTop: 10,
  },
  entryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 4,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  logoImage: { // Style for the actual logo
    width: 30,
    height: 30,
    borderRadius: 15, // Optional: if logos should be circular
    marginRight: 10,
    resizeMode: 'contain', // Ensures the logo fits well
  },
  logoPlaceholder: { // Keep for fallback
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.logoPlaceholderBackground,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  logoText: {
    fontSize: 10,
    color: colors.textMuted,
  },
  resortName: {
    flex: 1, // Allows resort name to take available space
    fontSize: 16,
  },
  snowfall: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LeaderboardSection;
