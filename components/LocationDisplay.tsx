import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useLocation } from '@/contexts/LocationContext';
import { ActivityIndicator, Pressable, StyleSheet, View } from 'react-native';

export interface LocationDisplayProps {
  showIcon?: boolean;
  showChevron?: boolean;
  onPress?: () => void;
  textStyle?: any;
  containerStyle?: any;
}

export function LocationDisplay({ 
  showIcon = true, 
  showChevron = false, 
  onPress,
  textStyle,
  containerStyle 
}: LocationDisplayProps) {
  const { locationName, isLoading, error, hasPermission, refreshLocation } = useLocation();

  const handlePress = async () => {
    if (onPress) {
      onPress();
    } else if (!hasPermission) {
      console.log('[LocationDisplay] User pressed to request location permission.');
      await refreshLocation();
    }
  };

  const displayLocationName = locationName || 'Unknown Location';
  const displayText = error ? 'Tap to enable location' : displayLocationName;

  const Container = onPress || !hasPermission ? Pressable : View;

  return (
    <Container style={[styles.container, containerStyle]} onPress={handlePress}>
      {showIcon && (
        <IconSymbol 
          name="location.fill" 
          size={16} 
          color={textStyle?.color || "#FFFFFF"} 
        />
      )}
      {isLoading ? (
        <ActivityIndicator 
          size="small" 
          color={textStyle?.color || "#FFFFFF"} 
          style={styles.loader} 
        />
      ) : (
        <ThemedText style={[styles.text, textStyle]}>{displayText}</ThemedText>
      )}
      {showChevron && (
        <IconSymbol 
          name="chevron.down" 
          size={16} 
          color={textStyle?.color || "#FFFFFF"} 
        />
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginHorizontal: 6,
  },
  loader: {
    marginHorizontal: 6,
  },
});