import { SemanticColors, useTheme } from '@/app/theme/ThemeContext';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { getWeatherIconComponent } from './weather_conditions/weatherIconResolver';

interface WeatherSummaryCompactProps {
  temperature: number;
  condition: string;
  iconName?: string;
  iconCode?: number; // Optional icon code for custom weather icons
}

// Helper to infer icon from condition (legacy fallback)
const getWeatherIcon = (condition?: string) => {
  if (!condition) return 'partly-sunny';
  if (condition.includes('Snow')) return 'snow';
  if (condition.includes('Cloudy')) return 'cloudy';
  if (condition.includes('Clear') || condition.includes('Sunny')) return 'sunny';
  return 'partly-sunny';
};

const WeatherSummaryCompact: React.FC<WeatherSummaryCompactProps> = ({ temperature, condition, iconName, iconCode }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  // Prefer iconCode for custom weather icons, fallback to legacy iconName/condition
  let IconComponent: React.FC<any> | null = null;
  if (typeof iconCode === 'number') {
    IconComponent = getWeatherIconComponent(iconCode, 'day'); // TODO: Optionally infer day/night
  } else {
    // Fallback: try to infer icon from condition string (legacy)
    // TODO: Optionally use iconName if provided
    // This fallback returns a MaterialIcon name, not a component, so you may want to use a default icon or emoji
    // For now, show nothing if no iconCode
    IconComponent = null;
  }

  return (
    <View style={styles.container}>
      {/* Icon above temperature, vertically stacked */}
      {IconComponent && (
        <View style={styles.iconRow}>
          <IconComponent style={{ marginBottom: 2, width: 24, height: 24 }} />
        </View>
      )}
      <Text style={styles.temperatureText}>{temperature}&deg;</Text>
      {/* Removed condition text for compact display */}
    </View>
  );
};

const createStyles = (colors: SemanticColors) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 56,
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  iconRow: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 2,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 0,
  },
  temperatureText: {
    fontSize: 14, // smaller for compact display
    fontWeight: '500',
    color: colors.textOnCard,
    fontFamily: 'Inter',
    lineHeight: 18,
  },
  conditionText: {
    fontSize: 12, // matches weatherCondition
    color: colors.textDimmed,
    fontWeight: '500',
    marginTop: 0,
    textAlign: 'center',
  },
});

export default WeatherSummaryCompact; 