---
description: all Pangea Map SDK and map interactions.  This is a comprehensize guide to use the Pangea SDK and anything in the application that references map interactions or operations.  
globs: 
alwaysApply: false
---
# Pangea Web SDK Usage Guide

[README.md](mdc:project-info/pangea-user-guide/README.md) 

This guide provides comprehensive documentation for using the Pangea Web SDK in your projects. It covers everything from initial setup to advanced features, with practical code examples.

## Quick Start for AI Agents

**🌎 AI Agent Quick Reference [AI-AGENT-QUICK-REFERENCE.md](mdc:project-info/pangea-user-guide/AI-AGENT-QUICK-REFERENCE.md)** - Essential patterns, common tasks, and critical implementation details specifically designed for AI agents.

## Complete Guide

*   1. Introduction to Pangea Web SDK [01-introduction.md](mdc:project-info/pangea-user-guide/01-introduction.md)
*   2. Getting Started ([02-getting-started.md](mdc:project-info/pangea-user-guide/02-getting-started.md))
*   3. Basic Map Operations ([03-basic-map-operations.md](mdc:project-info/pangea-user-guide/03-basic-map-operations.md))
*   4. Working with Layers ([04-working-with-layers.md](mdc:project-info/pangea-user-guide/04-working-with-layers.md))
*   5. Adding Graphics and Markers ([05-graphics-and-markers.md](mdc:project-info/pangea-user-guide/05-graphics-and-markers.md))
*   6. Animations ([06-animations.md](mdc:project-info/pangea-user-guide/06-animations.md))
*   7. Error Handling and Debugging ([07-error-handling-and-debugging.md](mdc:project-info/pangea-user-guide/07-error-handling-and-debugging.md))
